'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Euro, Send } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { createDirectOffer } from '@/lib/offers';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

// Schema za validaciju
const offerSchema = z.object({
  title: z
    .string()
    .min(5, 'Naslov mora imati najmanje 5 karaktera')
    .max(200, 'Naslov je predugačak'),
  description: z.string().min(20, 'Opis mora imati najmanje 20 karaktera'),
  message: z.string().optional(),
  budget: z
    .number()
    .min(25, 'Minimalni budžet je 25 €')
    .max(25000, 'Maksimalni budžet je 25,000 €'),
  contentTypes: z
    .array(z.string())
    .min(1, 'Morate odabrati najmanje jedan tip sadržaja'),
  platforms: z
    .array(z.string())
    .min(1, 'Morate odabrati najmanje jednu platformu'),
  deadline: z.date().optional(),
  requirements: z.string().optional(),
  deliverables: z.string().optional(),
});

type OfferForm = z.infer<typeof offerSchema>;

// Tipovi za platforme
interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface DirectOfferFormProps {
  influencerId: string;
  influencerName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

// Konfiguracija tipova sadržaja po platformama
const platformContentTypes = [
  {
    platform: 'Instagram',
    types: ['Instagram Post', 'Instagram Story', 'Instagram Reel'],
  },
  {
    platform: 'TikTok',
    types: ['TikTok Video'],
  },
  {
    platform: 'YouTube',
    types: ['YouTube Video', 'YouTube Short'],
  },
];

export function DirectOfferForm({
  influencerId,
  influencerName,
  onSuccess,
  onCancel,
}: DirectOfferFormProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [platforms, setPlatforms] = useState<Platform[]>([]);

  // State za platforme i tipove sadržaja
  const [selectedPlatformData, setSelectedPlatformData] = useState<{
    [platformId: number]: {
      selected: boolean;
      contentTypes: string[];
    };
  }>({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    setError,
  } = useForm<OfferForm>({
    resolver: zodResolver(offerSchema),
    defaultValues: {
      contentTypes: [],
      platforms: [],
    },
  });

  // Load platforms on mount
  useEffect(() => {
    const loadPlatforms = async () => {
      try {
        const { data } = await supabase
          .from('platforms')
          .select('*')
          .eq('is_active', true)
          .not('name', 'in', '(Facebook,Twitter/X)')
          .order('name');

        if (data) {
          setPlatforms(data as any);
          // Initialize platform data state
          const initialPlatformData: any = {};
          data.forEach(platform => {
            initialPlatformData[platform.id] = {
              selected: false,
              contentTypes: [],
            };
          });
          setSelectedPlatformData(initialPlatformData);
        }
      } catch (error) {
        console.error('Error loading platforms:', error);
      }
    };

    loadPlatforms();
  }, []);

  // Helper funkcije za rad sa platformama
  const togglePlatform = (platformId: number) => {
    setSelectedPlatformData(prev => ({
      ...prev,
      [platformId]: {
        ...prev[platformId],
        selected: !prev[platformId]?.selected,
        contentTypes: prev[platformId]?.selected
          ? []
          : prev[platformId]?.contentTypes || [],
      },
    }));
  };

  const toggleContentType = (platformId: number, contentType: string) => {
    setSelectedPlatformData(prev => {
      const currentTypes = prev[platformId]?.contentTypes || [];
      const newTypes = currentTypes.includes(contentType)
        ? currentTypes.filter(type => type !== contentType)
        : [...currentTypes, contentType];

      return {
        ...prev,
        [platformId]: {
          ...prev[platformId],
          contentTypes: newTypes,
        },
      };
    });
  };

  const getAvailableContentTypes = (platformName: string): string[] => {
    const platformConfig = platformContentTypes.find(
      p => p.platform === platformName
    );
    return platformConfig?.types || [];
  };

  // Update React Hook Form when platform data changes
  useEffect(() => {
    const selectedPlatforms = Object.entries(selectedPlatformData).filter(
      ([, data]) => data.selected && data.contentTypes.length > 0
    );

    const allContentTypes = selectedPlatforms
      .flatMap(([, data]) => data.contentTypes)
      .filter((type, index, array) => array.indexOf(type) === index);

    const allPlatforms = selectedPlatforms
      .map(([platformId]) => {
        const platform = platforms.find(p => p.id === parseInt(platformId));
        return platform?.name || '';
      })
      .filter(Boolean);

    setValue('contentTypes', allContentTypes);
    setValue('platforms', allPlatforms);
  }, [selectedPlatformData, platforms, setValue]);

  const onSubmit = async (data: OfferForm) => {
    if (!user) return;

    // Validate platform selection
    const selectedPlatforms = Object.entries(selectedPlatformData).filter(
      ([, data]) => data.selected && data.contentTypes.length > 0
    );

    if (selectedPlatforms.length === 0) {
      setError('root', {
        message: 'Morate odabrati najmanje jednu platformu sa tipom sadržaja',
      });
      return;
    }

    setIsLoading(true);

    try {
      // Prepare platform and content type data
      const allContentTypes = selectedPlatforms
        .flatMap(([, data]) => data.contentTypes)
        .filter((type, index, array) => array.indexOf(type) === index); // Remove duplicates

      const allPlatforms = selectedPlatforms
        .map(([platformId]) => {
          const platform = platforms.find(p => p.id === parseInt(platformId));
          return platform?.name || '';
        })
        .filter(Boolean);

      const offerData = {
        influencer_id: influencerId,
        title: data.title,
        description: data.description,
        business_message: data.message || null,
        budget: data.budget,
        content_types: allContentTypes,
        platforms: allPlatforms,
        deadline: data.deadline ? format(data.deadline, 'yyyy-MM-dd') : null,
        requirements: data.requirements || null,
      };

      const { error } = await createDirectOffer(offerData);

      if (error) {
        setError('root', { message: 'Greška pri slanju ponude' });
        return;
      }

      toast.success('Ponuda je uspješno poslana!');
      onSuccess();
    } catch {
      setError('root', { message: 'Neočekivana greška' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white">
          Pošaljite direktnu ponudu
        </h2>
        <p className="text-white/80 mt-1">
          Pošaljite ponudu za saradnju sa{' '}
          <span className="font-medium text-white">{influencerName}</span>
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Kartica 1 */}
        <div className="relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5" />
          <div className="relative p-6 space-y-4">
            {/* Naslov */}
            <div>
              <Label htmlFor="title" className="text-white font-medium">
                Naslov ponude *
              </Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Promocija novog proizvoda"
                className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
              />
              {errors.title && (
                <p className="text-sm text-red-300 mt-1">
                  {errors.title.message}
                </p>
              )}
            </div>

            {/* Opis */}
            <div>
              <Label htmlFor="description" className="text-white font-medium">
                Kratki opis ponude *
              </Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Promocija naše nove kreme za lice. Želimo da kreirate sadržaj koji prikazuje proizvod na autentičan način."
                rows={4}
                className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
              />
              {errors.description && (
                <p className="text-sm text-red-300 mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Poruka */}
            <div>
              <Label htmlFor="message" className="text-white font-medium">
                Poruka influenceru (opcionalno)
              </Label>
              <Textarea
                id="message"
                {...register('message')}
                placeholder="Dodatna poruka ili napomene za influencera..."
                rows={3}
                className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
              />
            </div>

            {/* Koju tip sadržaja želite da kreiram i objavim? */}
            <div>
              <Label className="text-white font-medium">
                Koju tip sadržaja želite da kreiram i objavim? *
              </Label>
              <div className="space-y-4 mt-3">
                {platforms.map(platform => {
                  const availableTypes = getAvailableContentTypes(
                    platform.name
                  );
                  const isSelected =
                    selectedPlatformData[platform.id]?.selected || false;
                  const selectedTypes =
                    selectedPlatformData[platform.id]?.contentTypes || [];

                  return (
                    <div
                      key={platform.id}
                      className="border border-white/30 rounded-lg p-4 bg-white/5"
                    >
                      <div className="flex items-center space-x-3 mb-3">
                        <Checkbox
                          id={`platform-${platform.id}`}
                          checked={isSelected}
                          onCheckedChange={() => togglePlatform(platform.id)}
                          className="data-[state=checked]:bg-white data-[state=checked]:border-white data-[state=checked]:text-purple-600"
                        />
                        <Label
                          htmlFor={`platform-${platform.id}`}
                          className="flex items-center gap-2 text-base font-medium cursor-pointer text-white"
                        >
                          <PlatformIconSimple
                            platform={platform.name}
                            size="lg"
                          />
                          {platform.name}
                        </Label>
                      </div>

                      {isSelected && (
                        <div className="ml-6 space-y-2">
                          <p className="text-sm text-white/70">
                            Tipovi sadržaja:
                          </p>
                          <div className="grid grid-cols-2 gap-2">
                            {availableTypes.map(type => (
                              <div
                                key={type}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={`${platform.id}-${type}`}
                                  checked={selectedTypes.includes(type)}
                                  onCheckedChange={() =>
                                    toggleContentType(platform.id, type)
                                  }
                                  className="data-[state=checked]:bg-white data-[state=checked]:border-white data-[state=checked]:text-purple-600"
                                />
                                <Label
                                  htmlFor={`${platform.id}-${type}`}
                                  className="text-sm cursor-pointer text-white/80"
                                >
                                  {type}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Cijena */}
            <div>
              <Label htmlFor="budget" className="text-white font-medium">
                Cijena *
              </Label>
              <p className="text-sm text-white/70 mb-2">
                Koliko ste spremni platiti za ovu uslugu?
              </p>
              <div className="relative">
                <Euro className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/70" />
                <Input
                  id="budget"
                  type="number"
                  {...register('budget', { valueAsNumber: true })}
                  placeholder=""
                  className="pl-10 bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                />
              </div>
              {errors.budget && (
                <p className="text-sm text-red-300 mt-1">
                  {errors.budget.message}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Kartica 2 */}
        <div className="relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5" />
          <div className="relative p-6 space-y-4">
            {/* Specifični zahtjevi */}
            <div>
              <Label htmlFor="requirements" className="text-white font-medium">
                Specifični zahtjevi
              </Label>
              <Textarea
                id="requirements"
                {...register('requirements')}
                placeholder="Kako želite da predstavim proizvod ili uslugu? Kako zamišljate da izgleda sadržaj koji treba da kreiram ili ipak želite meni prepustiti da odlučim? Molimo uključite sve specifične zahtjeve i napomene."
                rows={3}
                className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
              />
            </div>

            {/* Deadline */}
            <div>
              <Label className="text-white font-medium">
                Koji je krajnji rok za kreiranje objava?
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal mt-2 bg-white/10 border-white/30 text-white hover:bg-white/20',
                      !watch('deadline') && 'text-white/60'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {watch('deadline')
                      ? format(watch('deadline')!, 'dd.MM.yyyy')
                      : 'Odaberite datum'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={watch('deadline')}
                    onSelect={date => setValue('deadline', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* Error message */}
        {errors.root && (
          <div className="text-sm text-red-300 text-center bg-red-500/20 p-3 rounded-lg border border-red-400/30">
            {errors.root.message}
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-between gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="flex-1 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
          >
            Otkaži
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex-1 bg-white text-purple-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600 mr-2"></div>
                Šalje se...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Pošalji ponudu
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
