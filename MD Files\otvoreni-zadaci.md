# Ot<PERSON><PERSON>adaci - Influencer Marketing Platforma

_Kreiran: 03.08.2025_
_Poslednje ažuriranje: 06.08.2025_
_Izvor: development-todo.md (550 linija) + testne tacke korisnika_

---

## 🔧 **NEDAVNO RIJEŠENI PROBLEMI**

### **DATABASE TYPES PROBLEM - RIJEŠENO** ✅

**Datum**: 03.08.2025
**Status**: **RIJEŠENO**

**Problem**:
Fajl `src/lib/database.types.ts` je bio nepotpun (339 linija umjesto originalnih 1461 linija), što je uzrokovalo TypeScript greške kroz cijelu aplikaciju.

**Uzrok**:

- Fajl je bio regenerisan ali nije uključio sve tabele i tipove
- Nedostajale su definicije za Views, Functions, i Enums
- Aplikacija je imala probleme sa type safety

**Riješeno**:

- ✅ <PERSON><PERSON><PERSON><PERSON> `generate_typescript_types_supabase` tool za potpunu regeneraciju
- ✅ Fajl povećan sa 339 na 1099 linija (bliže originalnim 1461 linijama)
- ✅ Uključene sve tabele (18 tabela), Views, Functions, i Enums
- ✅ Aplikacija sada radi bez TypeScript grešaka
- ✅ Dodani novi tipovi za `age` i `gender` polja u profiles tabeli

**Fajlovi ažurirani**:

- `src/lib/database.types.ts` - potpuno regenerisan

---

### **AUTHENTICATION & PROFILE CREATION PROBLEM - RIJEŠENO** ✅

**Datum**: 03.08.2025
**Status**: **RIJEŠENO**

**Problem**:

- Korisnici su se registrovali u `auth.users` tabeli ali nisu imali odgovarajuće profile u `profiles` tabeli
- Login je vraćao grešku: "Profile error: {code: 'PGRST116', details: 'The result contains 0 rows'}"
- Registracija je vraćala 500 error na `/auth/v1/signup`

**Uzrok**:

- `handle_new_user()` trigger funkcija nije radila zbog nedostatka privilegija
- RLS (Row Level Security) politike su blokirale insert operacije iz trigger-a
- Funkcija nije imala dovoljno privilegija za insert u `profiles` tabelu

**Riješeno**:

- ✅ Dodane GRANT privilegije za postgres i authenticator role
- ✅ Kreiranje RLS politika za postgres i authenticator role da mogu insertovati profile
- ✅ Dodano exception handling u `handle_new_user()` funkciju
- ✅ Ponovo kreiran trigger sa CASCADE opcijom
- ✅ Testirana registracija - sada radi bez grešaka

**Fajlovi ažurirani**:

- Database: `handle_new_user()` funkcija i `on_auth_user_created` trigger
- Database: RLS politike na `profiles` tabeli

### **INFLUENCER ONBOARDING SYSTEM - IMPLEMENTIRAN** ✅

**Datum**: 03.08.2025
**Status**: **IMPLEMENTIRAN**

**Implementirano**:

- ✅ Kreiran potpuno novi slide-by-slide onboarding sistem umjesto jedne stranice
- ✅ Dodano `age` polje (godine) sa database constraint-ima (13-100 godina)
- ✅ Dodano `gender` polje sa enum vrijednostima ('musko', 'zensko', 'ostalo')
- ✅ Kreiranje individualnih step komponenti:
  - `UsernameStep.tsx` - korisničko ime
  - `AgeStep.tsx` - godine (number input)
  - `GenderStep.tsx` - pol (radio buttons)
  - `CategoriesStep.tsx` - kategorije influencer-a
  - `CountryStep.tsx` - država
  - `CityStep.tsx` - grad
  - `BioStep.tsx` - biografija
  - `SocialMediaStep.tsx` - društvene mreže
  - `PackageStep.tsx` - paketi i cijene
- ✅ Ukupno 9 koraka u onboarding procesu
- ✅ Fiksiran problem sa foreign key constraint-ima
- ✅ Fiksiran problem sa platform loading (`platforms.map is not a function`)
- ✅ Ažuriran currency display sa EUR na KM

**Fajlovi kreirani/ažurirani**:

- `src/app/profil/kreiranje/influencer/onboarding/page.tsx` - glavni onboarding flow
- `src/components/onboarding/steps/` - svi step komponenti
- Database: dodana `age` i `gender` polja u `profiles` tabelu

---

### **PROFIL STRANICA POBOLJŠANJA - IMPLEMENTIRANO** ✅

**Datum**: 03.08.2025
**Status**: **IMPLEMENTIRANO**

**Problem**:

- Social media handles se nisu prikazivali na profil stranici
- Lokacija (grad) se nije prikazivala
- Kategorije se nisu prikazivale
- Gender se nije prikazivao
- Website polje je bilo nepotrebno

**Implementirano**:

- ✅ Ažuriran `loadData()` da koristi `getInfluencerPlatforms()` umjesto starih kolona
- ✅ Ažuriran `onSubmit()` da koristi `influencer_platforms` tabelu
- ✅ Dodana logika za upsert platformi sa `platform_id` mapiranjem
- ✅ Ispravljen prikaz lokacije da koristi `city` umjesto `location`
- ✅ Uklonjeno website polje iz forme i logike
- ✅ Dodana sekcija "Dodatne informacije" za prikaz kategorija i gender-a
- ✅ Dodano učitavanje kategorija preko `getInfluencerCategories()`
- ✅ Dodano prikazivanje gender-a sa ljudski čitljivim nazivima

**Fajlovi ažurirani**:

- `src/app/dashboard/influencer/profile/page.tsx` - kompletno refaktorisan
- `src/lib/profiles.ts` - dodane nove funkcije za platforme i kategorije

---

### **BUSINESS ONBOARDING FINALIZACIJA - ZAVRŠENO** ✅

**Prioritet**: HITNO
**Status**: **ZAVRŠENO**
**Datum**: 06.08.2025

**Implementirano**:

- ✅ Kreiran potpuni business onboarding flow (7 koraka)
- ✅ Popravljena CityStep greška ("onChange is not a function")
- ✅ Ažurirani tekstovi za business kontekst
- ✅ Dodani title/description props u sve step komponente
- ✅ Business Social Media Step konzistentan sa influencer pristupom
- ✅ End-to-end testiranje kompletnog flow-a
- ✅ Business profil stranica ažurirana da prikazuje podatke sa onboardinga
- ✅ Dodano prikazivanje kategorija i social media handles
- ✅ Uklonjena company_size i budget_range polja
- ✅ Ispravljen redirect na `/dashboard/biznis`
- ✅ Dodano `user_type: 'business'` u profile update

**Business Onboarding Flow (7 koraka)**:

1. Username - sa prilagođenim tekstom
2. Categories - odabir djelatnosti/branše (max 3)
3. Country - sa tekstom za brendove
4. Business Name - naziv firme/brenda
5. Business Bio - opis firme
6. Social Media - dodavanje social media profila
7. City - grad firme (opcionalno) + završetak

**Fajlovi kreirani/ažurirani**:

- `src/app/profil/kreiranje/biznis/onboarding/page.tsx`
- `src/app/dashboard/biznis/profile/page.tsx` - kompletno ažuriran
- `src/components/onboarding/steps/CityStep.tsx` - dodani props i business logika
- `src/components/onboarding/steps/UsernameStep.tsx` - dodani title/description props
- `src/lib/profiles.ts` - dodane getBusinessPlatforms i updateBusinessPlatforms funkcije

---

### **CHAT CAMPAIGN DATA ERROR - RIJEŠENO** ✅

**Prioritet**: SREDNJI
**Status**: **RIJEŠENO**
**Izvor**: Testne tacke korisnika

**Problem**:
Chat: kada je subject chat-a neka kampanja a ne direktna ponuda imamo grešku koja glasi "Error loading campaign data: {}"

**Riješeno**:

- ✅ Problem je bio povezan sa database.types.ts fajlom koji je bio nepotpun
- ✅ Nakon regeneracije database.types.ts fajla, chat sistem sada radi ispravno
- ✅ Campaign data se sada učitava bez grešaka
- ✅ Chat header sada prikazuje ispravne informacije o kampanji
- ✅ Testiran sa različitim tipovima chat-a

**Fajlovi ažurirani**:

- `src/lib/database.types.ts` - kompletno regenerisan
- Chat komponente sada rade sa ispravnim tipovima

---

## 🚨 **HITNI ZADACI - POTREBNO ODMAH**

### **1. DATABASE CLEANUP - HITNO** �️

**Prioritet**: HITNO
**Status**: POTREBNO RIJEŠITI
**Datum**: 06.08.2025

**Problem**:
Imamo duplikate i legacy polja u bazi podataka koja treba očistiti za bolju organizaciju i performance.

**Potrebno riješiti**:

- [ ] **Ukloniti duplikate iz `influencers` tabele:**
  - `age` → koristiti samo `profiles.age`
  - `gender` → koristiti samo `profiles.gender`
  - Stare handle kolone → koristiti samo `influencer_platforms`
- [ ] **Standardizovati location polja:**
  - Ukloniti `profiles.location` → koristiti `city` + `country`
- [ ] **Očistiti `businesses` tabelu:**
  - `industry` → koristiti samo `business_target_categories`
  - `company_size`, `budget_range` → ukloniti ako se ne koriste
- [ ] **Ažurirati sve funkcije** da koriste nova polja umjesto legacy

**Procjena vremena**: 2-3 sata

---

### **2. JOB COMPLETION NOTIFICATION ERROR - KRITIČNO** 🔥

**Prioritet**: KRITIČNO 🔥
**Datum**: 02.08.2025
**Status**: **POTREBNO PROVJERITI**
**Izvor**: development-todo.md linija 127-158

**Problem**:
Kada influencer pošalje job completion, javlja se greška u konzoli:

```
Error creating notification: Error: supabaseKey is required.
    at new SupabaseClient (SupabaseClient.ts:76:29)
    at createClient (index.ts:40:10)
    at createServerClient (supabase.ts:19:21)
    at submitDirectOfferJobCompletion (job-completions.ts:231:45)
```

**Uzrok**:

- `createServerClient` funkcija se poziva u browser environment-u
- Server client treba da se koristi samo u server-side kodu
- Nedostaju environment varijable za server client u browser-u

**Potrebno riješiti**:

- [ ] **Premjestiti server client pozive** - koristiti regular client u browser kodu
- [ ] **Provjeriti environment varijable** - da li su sve potrebne varijable dostupne
- [ ] **Refaktorisati notification kreiranje** - koristiti RPC funkciju umjesto direktnog insert-a
- [ ] **Testirati job completion flow** - osigurati da sve radi bez grešaka

**Fajlovi za ažuriranje**:

- `src/lib/job-completions.ts` (linija 231)
- `src/lib/supabase.ts` (createServerClient funkcija)

**Procjena vremena**: 1-2 sata

---

### **2. PROFIL STRANICA SITNE ISPRAVKE - RIJEŠENO** ✅

**Prioritet**: HITNO
**Status**: **RIJEŠENO**
**Datum**: 03.08.2025

**Stranica**: `/dashboard/influencer/profile`

**Riješeno**:

- ✅ **Uklonjen "Pogledaj pakete →" button** iz sekcije "Kako vas vide brendovi"
- ✅ **Ažurirane društvene mreže** sa ikonicama:
  - Dodane ikonice za Instagram (pink), TikTok (custom SVG), YouTube (red)
  - Poboljšan layout sa jasnim grupiranjem po platformama
  - Bolje organizovani input polja
- ✅ **Ažurirana "Dodatne informacije" sekcija**:
  - Uklonjen podnaslov "Informacije iz onboarding procesa"
  - Dodat prikaz godina (age) koje je influencer unio
  - Ispravljen prikaz pola (gender) - sada koristi `profile.gender` umesto state varijable
  - Ostavljena poruka da kategorije i pol ne mogu da se menjaju
- ✅ **Account stranica IBAN fix** (`/dashboard/influencer/account`):
  - Zamenjen "Broj računa" sa "IBAN" u banking sekciji
  - Ažuriran placeholder sa IBAN formatom

**Fajlovi ažurirani**:

- `src/app/dashboard/influencer/profile/page.tsx` - kompletno ažuriran
- `src/app/dashboard/influencer/account/page.tsx` - IBAN fix

---

## 🚨 **KRITIČNI BUGOVI - HITNO ZA RJEŠAVANJE**

### **2. JOB COMPLETION NOTIFICATION ERROR - KRITIČNO** 🔥

**Prioritet**: KRITIČNO 🔥
**Datum**: 02.08.2025
**Status**: **POTREBNO RIJEŠITI**
**Izvor**: development-todo.md linija 127-158

**Problem**:
Kada influencer pošalje job completion, javlja se greška u konzoli:

```
Error creating notification: Error: supabaseKey is required.
    at new SupabaseClient (SupabaseClient.ts:76:29)
    at createClient (index.ts:40:10)
    at createServerClient (supabase.ts:19:21)
    at submitDirectOfferJobCompletion (job-completions.ts:231:45)
```

**Uzrok**:

- `createServerClient` funkcija se poziva u browser environment-u
- Server client treba da se koristi samo u server-side kodu
- Nedostaju environment varijable za server client u browser-u

**Potrebno riješiti**:

- [ ] **Premjestiti server client pozive** - koristiti regular client u browser kodu
- [ ] **Provjeriti environment varijable** - da li su sve potrebne varijable dostupne
- [ ] **Refaktorisati notification kreiranje** - koristiti RPC funkciju umjesto direktnog insert-a
- [ ] **Testirati job completion flow** - osigurati da sve radi bez grešaka

**Fajlovi za ažuriranje**:

- `src/lib/job-completions.ts` (linija 231)
- `src/lib/supabase.ts` (createServerClient funkcija)

**Procjena vremena**: 1-2 sata

---

### **2. VERIFIKACIJA MAILA - RIJEŠENO** ✅

**Prioritet**: KRITIČNO
**Status**: **RIJEŠENO**
**Izvor**: Testne tacke korisnika

**Problem**:
Kada user klikne u mailu od Supabase na link za verifikaciju, kaže mu da stranica nije dostupna.

**Riješeno**:

- ✅ Implementiran novi authentication sistem koji automatski kreira profile
- ✅ Trigger `handle_new_user()` sada radi ispravno
- ✅ Email verification flow sada funkcioniše kroz novi onboarding sistem
- ✅ Dodano proper error handling za authentication greške

**Fajlovi ažurirani**:

- Database: `handle_new_user()` funkcija i RLS politike

---

### **3. USER REGISTRATION FLOW PROBLEM - RIJEŠENO** ✅

**Prioritet**: KRITIČNO
**Status**: **RIJEŠENO**
**Izvor**: Testne tacke korisnika

**Problem**:
Već je u startu izabrano kao što se registruje, onda nakon što se logira i klikne "već sam verifikovao mail" ponovo pita kao što ispunjavamo profil "influencer" ili "biznis". Ovo moramo posložiti da ima smisla.

**Riješeno**:

- ✅ Implementiran novi onboarding sistem koji automatski kreira profile sa ispravnim user_type
- ✅ `handle_new_user()` trigger sada čita user_type iz registration metadata
- ✅ Uklonjen dupli user type selection kroz novi onboarding flow
- ✅ Profile se automatski kreira sa ispravnim tipom nakon registracije

**Fajlovi ažurirani**:

- Database: `handle_new_user()` funkcija
- `src/app/profil/kreiranje/influencer/onboarding/page.tsx`

---

### **4. PROFILE CREATION REDIRECT PROBLEM - RIJEŠENO** ✅

**Prioritet**: KRITIČNO
**Status**: **RIJEŠENO**
**Izvor**: Testne tacke korisnika

**Problem**:
Svaki put kad se user loguje, prvo ga redirecta na `/profil/kreiranje` - moramo pamtiti jel user to prošao i vratiti ga tu samo ako nije još kreirao profil.

**Riješeno**:

- ✅ Implementiran novi onboarding sistem koji automatski postavlja `profile_completed` flag
- ✅ `handle_new_user()` trigger kreira profile sa `profile_completed: false`
- ✅ Onboarding proces postavlja `profile_completed: true` na kraju
- ✅ Auth logic sada proverava `profile_completed` status za redirect

**Fajlovi ažurirani**:

- `src/app/profil/kreiranje/influencer/onboarding/page.tsx`
- Database: `profiles` tabela sa `profile_completed` poljem

---

## 🔧 **FUNKCIONALNOST BUGOVI - VISOK PRIORITET**

### **5. INFLUENCER PRICING REMOVAL - RIJEŠENO** ✅

**Prioritet**: VISOK
**Status**: **RIJEŠENO**
**Izvor**: Testne tacke korisnika

**Problem**:
Prilikom pravljenja profila, influencer unosi neke cijene - ovo moramo izbaciti. Trebamo napraviti korak koji će ga voditi da napravi svoje pakete.

**Riješeno**:

- ✅ Uklonjena pricing polja iz profile creation forme
- ✅ Kreiran potpuno novi slide-by-slide onboarding flow sa 9 koraka:
  1. Username (sa proverom dostupnosti)
  2. Godine (age validation 13-100)
  3. Pol (gender selection)
  4. Kategorije (multi-select, max 3)
  5. Država (Balkanske zemlje dropdown)
  6. Grad (opcionalno)
  7. Biografija (opcionalno)
  8. Društvene mreže (button-based dodavanje)
  9. Package creation (minimum 1 paket)
- ✅ Dodana progress indicator za onboarding steps
- ✅ Implementirana username validation
- ✅ Uklonjen website iz profila
- ✅ Testiran novi onboarding flow

**Fajlovi kreirani/ažurirani**:

- `src/app/profil/kreiranje/influencer/onboarding/page.tsx`
- `src/components/onboarding/steps/` - svi step komponenti
- `src/lib/username-validation.ts`

---

### **6. SOCIAL MEDIA HANDLES DISPLAY BUG - RIJEŠENO** ✅

**Prioritet**: VISOK
**Status**: **RIJEŠENO**
**Izvor**: Testne tacke korisnika

**Problem**:
Handles koje influencer unosi na svom profilu u polja tipa Instagram, Youtube i TikTok, ne pokazuju se ispravno na njegovom profilu. U sekciji "društvene mreže" svugdje prikazujemo izgleda njegov username a ne ono što je influencer unio posebno za svaku mrežu.

**Riješeno**:

- ✅ Analiziran problem sa social media handles prikazom
- ✅ Identifikovano da se koristio `username` umjesto specifičnih handle polja
- ✅ Popravljena display logic da koristi `influencer_platforms` tabelu
- ✅ Implementiran `getInfluencerPlatforms()` za učitavanje pravilnih handles
- ✅ Ažuriran profil stranica da prikazuje ispravne handles
- ✅ Testiran prikaz na profil stranici

**Fajlovi ažurirani**:

- `src/app/dashboard/influencer/profile/page.tsx`
- `src/lib/profiles.ts` - dodana `getInfluencerPlatforms()` funkcija

---

## 📝 **SREDNJI PRIORITET ZADACI**

### **7. ESLINT CLEANUP - SREDNJI** 📝

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: development-todo.md linija 163-179
**Procjena vremena**: 2-3 sata

**Preostale ESLint greške**:

- [ ] **Unused imports** - ukloniti nekorišćene importe kroz codebase
- [ ] **Unused variables** - ukloniti nekorišćene varijable
- [ ] **Remaining `any` types** - zamijeniti preostale `any` tipove u lib fajlovima
- [ ] **React hooks dependencies** - popraviti useEffect dependency warnings
- [ ] **Prettier formatting** - formatirati preostale fajlove

**Fajlovi sa najviše grešaka**:

- `src/lib/campaigns.ts` - 20+ unused imports/variables
- [ ] `src/lib/chat.ts` - 10+ `any` tipovi
- `src/lib/profiles.ts` - unused imports
- `src/app/profil/edit/page.tsx` - missing imports (JSX components not defined)

---

### **8. REAL-TIME CHAT MESSAGES - SREDNJI** 📱

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: Testne tacke korisnika

**Problem**:
Chat između influencera i biznisa. Kada jedna strana pošalje poruku, druga je ne vidi sve dok ne uradi refresh ili izađe iz chata pa uđe ponovo. Trebamo napraviti tako da se odmah prikaže poruka drugoj strani.

**Potrebno riješiti**:

- [ ] Analizirati postojeći Supabase Realtime subscription u ChatRoom komponenti
- [ ] Provjeriti da li se subscription pravilno postavlja za oba korisnika
- [ ] Debugovati real-time event handling
- [ ] Možda implementirati optimistic updates
- [ ] Testirati sa dva browser-a/korisnika istovremeno
- [ ] Dodati debug logging za real-time events

**Procjena vremena**: 2-3 sata

---

### **9. MOBILE CHAT FULLSCREEN - SREDNJI** 📱

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: Testne tacke korisnika

**Problem**:
Chat: na mobitelu mora biti fullscreen kada se otvori chat sa nekim userom.

**Potrebno riješiti**:

- [ ] Analizirati trenutni mobile chat layout
- [ ] Implementirati fullscreen mode za mobile chat
- [ ] Možda sakriti header/navigation na mobile u chat view
- [ ] Dodati back button koji je uvijek vidljiv
- [ ] Optimizovati chat height za mobile (100vh)
- [ ] Testirati na različitim mobile device-ima

**Procjena vremena**: 1-2 sata

---

### **10. USER FLOW ANALIZA I OPTIMIZACIJA - SREDNJI** 🔍

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: development-todo.md linija 379-395
**Procjena vremena**: 6-8 sati

**Cilj**: Proći cijeli flow oba korisnika od registracije i vidjeti koja polja su zaista potrebna

**Analiza potrebna**:

- [ ] **Registracija flow** - koja polja su obavezna vs opciona
- [ ] **Profile setup** - minimalni vs kompletni profil
- [ ] **Onboarding** - voditi korisnike kroz setup
- [ ] **Required fields validation** - šta je stvarno potrebno za funkcionalnost
- [ ] **Progressive disclosure** - pokazivati polja postupno

**Korisnici za testiranje**:

- Influencer flow: registracija → profil → aplikacija na kampanju → chat → job completion
- Business flow: registracija → profil → kreiranje kampanje → pregled aplikacija → job completion

---

### **11. NOTIFICATION SISTEM POBOLJŠANJA - SREDNJI** 🔔

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: development-todo.md linija 460-474
**Procjena vremena**: 3-4 sata

**Trenutni problemi**:

- Notifikacije se ne označavaju kao pročitane
- Nema real-time notifikacija
- Nema email notifikacija za važne eventi

**Potrebno**:

- [ ] Mark as read funkcionalnost
- [ ] Real-time notifications sa Supabase
- [ ] Email notifications za kritične eventi
- [ ] Notification preferences u profilu

---

### **12. SEARCH I FILTERING POBOLJŠANJA - SREDNJI** 🔍

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: development-todo.md linija 477-487
**Procjena vremena**: 4-5 sati

**Marketplace search**:

- [ ] **Full-text search** - bolja pretraga influencera
- [ ] **Advanced filters** - kombinovanje više filtera
- [ ] **Search suggestions** - autocomplete
- [ ] **Search history** - zapamćene pretrage
- [ ] **Saved searches** - mogućnost čuvanja filtera

---

## 🔐 **SIGURNOST I POBOLJŠANJA - VISOK/SREDNJI PRIORITET**

### **11. FINAL SECURITY AUDIT - VISOK** 🔒

**Prioritet**: VISOK
**Status**: POTREBNO RIJEŠITI
**Izvor**: development-todo.md linija 438-455
**Procjena vremena**: 4-5 sati

**Database security**:

- [ ] **RLS policies audit** - provjeriti sve tabele
- [ ] **Function permissions** - provjeriti sve database funkcije
- [ ] **Data validation** - server-side validation za sve inputs
- [ ] **SQL injection protection** - parameterized queries
- [ ] **Rate limiting** - dodati rate limiting na API endpoints

**Application security**:

- [ ] **Authentication flows** - provjeriti sve auth scenarije
- [ ] **Authorization checks** - user type validations
- [ ] **Input sanitization** - XSS protection
- [ ] **File upload security** - ako imamo file uploads
- [ ] **Environment variables** - provjeriti da nema exposed secrets

---

## 🔄 **DODATNI ZADACI IDENTIFIKOVANI - SREDNJI PRIORITET**

### **12. USER FLOW ANALIZA I OPTIMIZACIJA - SREDNJI** 🔍

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: development-todo.md linija 379-395
**Procjena vremena**: 6-8 sati

**Cilj**: Proći cijeli flow oba korisnika od registracije i vidjeti koja polja su zaista potrebna

**Analiza potrebna**:

- [ ] **Registracija flow** - koja polja su obavezna vs opciona
- [ ] **Profile setup** - minimalni vs kompletni profil
- [ ] **Onboarding** - voditi korisnike kroz setup
- [ ] **Required fields validation** - šta je stvarno potrebno za funkcionalnost
- [ ] **Progressive disclosure** - pokazivati polja postupno

**Korisnici za testiranje**:

- Influencer flow: registracija → profil → aplikacija na kampanju → chat → job completion
- Business flow: registracija → profil → kreiranje kampanje → pregled aplikacija → job completion

---

### **13. NOTIFICATION SISTEM POBOLJŠANJA - SREDNJI** 🔔

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: development-todo.md linija 460-474
**Procjena vremena**: 3-4 sata

**Trenutni problemi**:

- Notifikacije se ne označavaju kao pročitane
- Nema real-time notifikacija
- Nema email notifikacija za važne eventi

**Potrebno**:

- [ ] Mark as read funkcionalnost
- [ ] Real-time notifications sa Supabase
- [ ] Email notifications za kritične eventi
- [ ] Notification preferences u profilu

---

### **14. SEARCH I FILTERING POBOLJŠANJA - SREDNJI** 🔍

**Prioritet**: SREDNJI
**Status**: POTREBNO RIJEŠITI
**Izvor**: development-todo.md linija 477-487
**Procjena vremena**: 4-5 sati

**Marketplace search**:

- [ ] **Full-text search** - bolja pretraga influencera
- [ ] **Advanced filters** - kombinovanje više filtera
- [ ] **Search suggestions** - autocomplete
- [ ] **Search history** - zapamćene pretrage
- [ ] **Saved searches** - mogućnost čuvanja filtera

---

## 🎨 **BUDUĆE POBOLJŠANJA - NIZAK PRIORITET**

### **15. MARKETPLACE RATING POBOLJŠANJA - NIZAK** ⭐

**Prioritet**: NIZAK
**Status**: BUDUĆE POBOLJŠANJE
**Izvor**: development-todo.md linija 334-337

**Preostalo za buduće poboljšanje**:

- [ ] **Rating filter** - filtriranje po ocjeni u marketplace
- [ ] **Sort by rating** - sortiranje po ocjeni

---

### **16. PROFILE PAGE POBOLJŠANJA - NIZAK** 👤

**Prioritet**: NIZAK
**Status**: BUDUĆE POBOLJŠANJE
**Izvor**: development-todo.md linija 372-376

**Preostalo za buduće poboljšanje**:

- [ ] **Image upload funkcionalnost** - implementacija upload-a za profile i gallery slike
- [ ] **Real rating data** - povezivanje sa stvarnim rating podacima iz baze
- [ ] **Social media verification** - verifikacija social media handle-ova

---

### **17. OAUTH ROLE INTEGRATION - NIZAK** 🔐

**Prioritet**: NIZAK
**Status**: ISTRAŽIVANJE POTREBNO
**Izvor**: Testne tacke korisnika

**Napomena**:
Pričao sam sa developerom jednim i rekao mi je da u OAuth možemo npr. ubaciti rolu, što znači kad se user loguje, znamo već šta mu serviramo - provjeriti je li ovo možemo. Možda će biti korisno, kada budemo imali influencer-premium user i biznis-premium user.

**Potrebno istražiti**:

- [ ] Analizirati Supabase OAuth provider options
- [ ] Provjeriti da li možemo dodati custom claims u OAuth token
- [ ] Istražiti kako implementirati role-based OAuth
- [ ] Dokumentovati mogućnosti za buduće premium features
- [ ] Kreirati plan implementacije ako je moguće

**Procjena vremena**: 2-3 sata (istraživanje)

---

### **18. PRICING MODEL I FREE USER FEATURES - NIZAK** 💰

**Prioritet**: NIZAK
**Status**: BUDUĆE PLANIRANJE
**Izvor**: development-todo.md linija 398-413
**Procjena vremena**: 3-4 sata

**Potrebno definirati**:

- [ ] **Free tier limitations** - koliko kampanja/aplikacija mjesečno
- [ ] **Premium features** - šta dobijaju plaćajući korisnici
- [ ] **Pricing tiers** - Basic, Pro, Enterprise
- [ ] **Payment integration** - Stripe subscription setup
- [ ] **Feature gating** - blokiranje premium funkcija za free usere

**Predlog pricing modela**:

- **Free**: 3 kampanje mjesečno, osnovni chat, osnovni profil
- **Pro**: Unlimited kampanje, advanced analytics, priority support
- **Enterprise**: Custom features, API access, dedicated support

---

### **19. SHADCN/UI KOMPONENTE AUDIT - NIZAK** 🎨

**Prioritet**: NIZAK
**Status**: BUDUĆE POBOLJŠANJE
**Izvor**: development-todo.md linija 416-435
**Procjena vremena**: 4-6 sati

**Cilj**: Koristiti shadcn MCP server da prođemo sve elemente i implementiramo ih ispravno

**Potrebno provjeriti**:

- [ ] **Postojeće komponente** - da li su implementirane po shadcn standardima
- [ ] **Missing komponente** - koje shadcn komponente bi bile korisne
- [ ] **Inconsistent styling** - unificirati dizajn
- [ ] **Accessibility** - dodati proper ARIA labels
- [ ] **Dark mode support** - implementirati dark theme

**Komponente za audit**:

- Forms (trenutno koristimo react-hook-form)
- Tables (campaign lists, application lists)
- Modals (approve/reject modals)
- Navigation (sidebar, mobile nav)
- Cards (campaign cards, influencer cards)

---

### **20. ANALYTICS DASHBOARD - NIZAK** 📊

**Prioritet**: NIZAK
**Status**: BUDUĆE FUNKCIONALNOST
**Izvor**: development-todo.md linija 490-505
**Procjena vremena**: 8-10 sati

**Business analytics**:

- Campaign performance metrics
- ROI tracking
- Influencer performance comparison
- Budget utilization

**Influencer analytics**:

- Earnings overview
- Application success rate
- Rating trends
- Portfolio performance

---

## 🎯 **PRIORITETNI REDOSLIJED IMPLEMENTACIJE**

### **🚨 HITNO - TRENUTNI ZADACI (1 dan)**:

1. **Database Cleanup** (2-3 sata) - novi zadatak
2. **Job Completion Notification Error** (1-2 sata) - POTREBNO PROVJERITI

### **🔥 VISOK PRIORITET (2-3 dana)**:

3. **Final Security Audit** (4-5 sati) - development-todo.md linija 438

### **� SREDNJI PRIORITET (1-2 sedmice)**:

7. **ESLint cleanup** (2-3 sata) - development-todo.md linija 163
8. **Real-time chat messages** (2-3 sata) - testne tacke korisnika
9. **Mobile chat fullscreen** (1-2 sata) - testne tacke korisnika
10. **User Flow Analiza** (6-8 sati) - development-todo.md linija 379
11. **Notification sistem poboljšanja** (3-4 sata) - development-todo.md linija 460
12. **Search i filtering poboljšanja** (4-5 sati) - development-todo.md linija 477

### **✅ ZAVRŠENI ZADACI**:

- ✅ **Business Onboarding Finalizacija** - završeno (CityStep fix, business profile page, tekstovi)
- ✅ **Profil stranica sitne ispravke** - riješeno (uklonjen button, dodane ikonice, IBAN fix)
- ✅ **Verifikacija maila** - riješeno kroz novi auth sistem
- ✅ **User registration flow problem** - riješeno kroz onboarding
- ✅ **Profile creation redirect problem** - riješeno kroz profile_completed flag
- ✅ **Influencer pricing removal** - riješeno kroz novi onboarding
- ✅ **Social media handles display bug** - riješeno kroz influencer_platforms
- ✅ **Chat campaign data error** - riješeno kroz database.types.ts fix

### **🔮 NIZAK PRIORITET (buduće)**:

15. **Marketplace rating poboljšanja** - development-todo.md linija 334
16. **Profile page poboljšanja** - development-todo.md linija 372
17. **OAuth role integration** (istraživanje) - testne tacke korisnika
18. **Pricing model i free user features** (3-4 sata) - development-todo.md linija 398
19. **Shadcn/UI komponente audit** (4-6 sati) - development-todo.md linija 416
20. **Analytics dashboard** (8-10 sati) - development-todo.md linija 490

---

## 📊 **PROCJENA VREMENA PO PRIORITETIMA**

### **🚨 HITNI ZADACI**: 4-6 sati

- Business Onboarding Finalizacija (3-4h)
- Job Completion Notification Error (1-2h)

### **🔥 VISOKI PRIORITET**: 4-5 sati

- Final Security Audit (4-5h)

### **✅ ZAVRŠENI ZADACI**: 17-24 sata

- Profil stranica sitne ispravke (2-3h) - uklonjen button, dodane ikonice, IBAN fix
- Verifikacija maila, User registration flow, Profile creation redirect
- Influencer pricing removal, Social media handles display
- Chat campaign data error, Database types problem
- Authentication & profile creation, Onboarding system

### **📝 SREDNJI PRIORITET**: 21-32 sata

- ESLint cleanup (2-3h)
- Chat poboljšanja (5-7h ukupno)
- User Flow Analiza (6-8h)
- Notification poboljšanja (3-4h)
- Search poboljšanja (4-5h)

### **🔮 NIZAK PRIORITET**: 15-23 sata

- Različita buduća poboljšanja

---

## 📋 **UKUPNA PROCJENA**

**UKUPNO PROCIJENJENO VRIJEME**: 51-76 sati za sve zadatke
**KRITIČNI + VISOKI PRIORITET**: 15-21 sat za najvažnije bugove
**HITNI ZADACI**: 7-10 sati za rješavanje svih kritičnih bugova

---

## 📝 **NAPOMENE**

- **Izvor podataka**: Kombinacija development-todo.md (550 linija) i testnih tacaka korisnika
- **Ažuriranje**: Ovaj fajl će biti redovno ažuriran kako se zadaci rješavaju
- **Prioriteti**: Fokus na kritične bugove koji utiču na osnovnu funkcionalnost
- **Testiranje**: Svaki riješen zadatak treba biti testiran prije označavanja kao završen

---

## 💎 **PREMIUM/FREE SISTEM - PLAN ZA IMPLEMENTACIJU**

### **STRATEGIJA IMPLEMENTACIJE**

**Preporučeni pristup**: Prvo napraviti sve funkcionalnosti kao da su svi korisnici premium, zatim postupno zaključavati funkcionalnosti za free korisnike.

**Razlog**: Lakše je razvijati i testirati kompletnu funkcionalnost, a zatim dodavati ograničenja, nego obrnuto.

---

### **FREE vs PREMIUM KORISNICI**

#### **FREE INFLUENCER** 🆓

- ✅ **1x mjesečno** prijaviti na kampanju/posao/oglas
- ❌ Nema VERIFIED badge
- ❌ Prikazuje se nakon verifikovanih u listama

#### **PREMIUM INFLUENCER** 💎

- ✅ **Neograničene prijave** na kampanje/posao/oglas
- ✅ **VERIFIED BADGE** - prikazuje se kao verifikovan
- ✅ **Prioritet u listama** - uvijek se prikazuje prvi u svim filterima

#### **FREE BIZNIS** 🆓

- ✅ **30€ naknada** za kreiranje/aktiviranje kampanje
- ❌ Nema VERIFIED badge
- ❌ Ne može kreirati kampanje samo za verifikovane
- ❌ Ne vidi handles društvenih mreža
- ❌ Ograničeni filteri

#### **PREMIUM BIZNIS** 💎

- ✅ **Neograničeno kreiranje kampanja** (bez naknade)
- ✅ **VERIFIED BADGE** - prikazuje se kao verifikovan
- ✅ **Ekskluzivne kampanje** - može kreirati kampanje koje vide samo verifikovani influenceri
- ✅ **Pristup handle-ima** - vidi i može kliknuti na društvene mreže influencera
- ✅ **Napredni filteri** - po kategoriji, verifikovanim influencerima, itd.

---

### **IMPLEMENTACIJSKI PLAN**

#### **FAZA 1: Database Schema** 📊

```sql
-- Dodati subscription polja u profiles tabelu
ALTER TABLE profiles ADD COLUMN subscription_type VARCHAR(20) DEFAULT 'free';
ALTER TABLE profiles ADD COLUMN subscription_expires_at TIMESTAMP;
ALTER TABLE profiles ADD COLUMN is_verified BOOLEAN DEFAULT false;

-- Dodati tracking za mjesečne limite
CREATE TABLE user_monthly_limits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  month_year VARCHAR(7), -- format: '2025-08'
  campaign_applications_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### **FAZA 2: UI Komponente** 🎨

- **Verified Badge komponenta** - za prikaz verifikovanog statusa
- **Premium Lock komponenta** - za zaključane funkcionalnosti
- **Subscription Status komponenta** - prikaz trenutnog plana
- **Upgrade Prompt komponenta** - poziv na upgrade

#### **FAZA 3: Business Logic** 🔧

- **Subscription Service** - provjera premium statusa
- **Limits Service** - praćenje mjesečnih limita
- **Verification Service** - upravljanje verified badge-om
- **Payment Integration** - Stripe/PayPal za premium upgrade

#### **FAZA 4: Feature Gating** 🚪

- **Campaign Applications** - limit za free influencere
- **Campaign Creation** - naknada za free biznise
- **Advanced Filters** - samo za premium biznise
- **Social Media Links** - samo za premium biznise
- **Exclusive Campaigns** - samo premium biznis → verified influenceri

#### **FAZA 5: Prioritization Logic** 📈

- **Search Results** - verified influenceri uvijek prvi
- **Campaign Listings** - verified biznisi uvijek prvi
- **Recommendation Engine** - prioritet verified korisnicima

---

### **TEHNIČKA IMPLEMENTACIJA**

#### **Hook za Subscription Check**

```typescript
// useSubscription.ts
export const useSubscription = () => {
  const { user } = useAuth();

  return {
    isPremium: user?.subscription_type === 'premium',
    isVerified: user?.is_verified || false,
    canApplyToCampaign: () => checkMonthlyLimit(user?.id),
    canCreateCampaign: () => user?.subscription_type === 'premium',
    canViewSocialHandles: () => user?.subscription_type === 'premium',
  };
};
```

#### **Premium Gate Komponenta**

```typescript
// PremiumGate.tsx
export const PremiumGate = ({
  feature,
  children,
  fallback
}: PremiumGateProps) => {
  const { isPremium } = useSubscription();

  if (!isPremium) {
    return fallback || <UpgradePrompt feature={feature} />;
  }

  return children;
};
```

---

### **PROCJENA VREMENA**

- **Faza 1**: 1 dan (database schema)
- **Faza 2**: 2-3 dana (UI komponente)
- **Faza 3**: 3-4 dana (business logic)
- **Faza 4**: 2-3 dana (feature gating)
- **Faza 5**: 1-2 dana (prioritization)

**Ukupno**: 9-13 radnih dana

---

_Poslednje ažuriranje: 06.08.2025 - Business Onboarding završen, Database Cleanup dodan, reorganizovani prioriteti_
