Failed to compile.

./src/app/api/stripe/webhook/route.ts
626:46 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
662:30 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
665:30 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
704:45 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/business/[username]/page.tsx
61:27 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/campaigns/[id]/edit/page.tsx
67:27 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
122:72 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/applications/[id]/page.tsx
569:36 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/packages/page.tsx
32:52 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
41:6 Warning: React Hook useEffect has a missing dependency: 'loadInfluencer'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
101:21 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/page.tsx
13:48 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
22:6 Warning: React Hook useEffect has missing dependencies: 'checkSubscription' and 'loadInfluencer'. Either include them or remove the dependency array. react-hooks/exhaustive-deps

./src/app/dashboard/influencer/pricing/page.tsx
84:6 Warning: React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/dashboard/influencer/profile/page.tsx
70:48 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
71:42 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
129:6 Warning: React Hook useEffect has missing dependencies: 'checkSubscription' and 'loadData'. Either include them or remove the dependency array. react-hooks/exhaustive-deps

./src/app/dashboard/page.tsx
22:35 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
35:6 Warning: React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/influencer/[username]/InfluencerProfileClient.tsx
62:58 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
63:58 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
83:6 Warning: React Hook useEffect has a missing dependency: 'loadBusinessProfile'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
193:36 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/influencer/[username]/InfluencerProfileWithAccessControl.tsx
31:6 Warning: React Hook useEffect has a missing dependency: 'checkAccess'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/profil/edit/page.tsx
60:62 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
61:42 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
81:6 Warning: React Hook useEffect has a missing dependency: 'loadExistingData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
98:35 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
104:39 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
114:34 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
122:36 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
124:27 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
125:24 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/biznis/onboarding/page.tsx
74:6 Warning: React Hook useEffect has a missing dependency: 'loadOnboardingProgress'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
126:30 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
177:29 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/influencer/onboarding/page.tsx
175:6 Warning: React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
198:25 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
372:59 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
392:29 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/page.tsx
20:35 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
32:6 Warning: React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/registracija/business/page.tsx
81:21 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/registracija/influencer/page.tsx
73:21 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/campaigns/campaign-filters.tsx
87:66 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
345:75 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/campaigns/create-campaign-form.tsx
831:73 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/chat/Chat.tsx
25:6 Warning: React Hook useEffect has missing dependencies: 'loadRoom' and 'onRoomStateChange'. Either include them or remove the dependency array. If 'onRoomStateChange' changes too often, find the parent component that defines it and wrap that definition in useCallback. react-hooks/exhaustive-deps

./src/components/chat/ChatContextBar.tsx
47:6 Warning: React Hook useEffect has a missing dependency: 'loadContextData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/chat/ChatEnableButton.tsx
41:48 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
49:6 Warning: React Hook useEffect has a missing dependency: 'checkChatStatus'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/chat/ChatPermissionStatus.tsx
47:6 Warning: React Hook useEffect has a missing dependency: 'loadPermission'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/chat/ChatRoom.tsx
113:6 Warning: React Hook useEffect has a missing dependency: 'loadMessages'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/dashboard/DashboardLayout.tsx
25:42 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
39:6 Warning: React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/marketplace/filters.tsx
437:54 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/marketplace/horizontal-filters.tsx
84:59 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/navigation/DesktopHeader.tsx
36:12 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
42:9 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
48:9 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/navigation/MobileTopNavbar.tsx
48:12 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
142:6 Warning: React Hook useEffect has missing dependencies: 'loadNotifications' and 'loadUnreadCount'. Either include them or remove the dependency array. react-hooks/exhaustive-deps

./src/components/navigation/ResponsiveNavigation.tsx
8:12 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/notifications/NotificationDropdown.tsx
48:6 Warning: React Hook useEffect has missing dependencies: 'loadNotifications' and 'loadUnreadCount'. Either include them or remove the dependency array. react-hooks/exhaustive-deps

./src/components/offers/DirectOfferForm.tsx
130:38 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/profile/AvatarUpload.tsx
71:6 Warning: React Hook useCallback has a missing dependency: 'handleFileSelection'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/ui/image-cropper.tsx
200:15 Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element @next/next/no-img-element

./src/components/ui/infinite-scroll.tsx
167:44 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
178:38 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/hooks/useInfiniteScroll.ts
47:6 Warning: React Hook useCallback has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
97:6 Warning: React Hook useCallback has a missing dependency: 'isLoading'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
166:6 Warning: React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies. react-hooks/exhaustive-deps
166:6 Warning: React Hook useEffect has missing dependencies: 'dependencies' and 'reset'. Either include them or remove the dependency array. react-hooks/exhaustive-deps  
173:6 Warning: React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/lib/api-auth.ts
326:7 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/lib/subscriptions.ts
505:1 Error: Delete `··` prettier/prettier

./src/lib/types.ts
154:13 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

info - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
