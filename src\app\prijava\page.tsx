'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Eye, EyeOff, Loader2 } from 'lucide-react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';

const loginSchema = z.object({
  email: z.string().email('Unesite validnu email adresu'),
  password: z.string().min(1, 'Unesite lozinku'),
  rememberMe: z.boolean().optional(),
});

type LoginForm = z.infer<typeof loginSchema>;

export default function PrijavaPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signIn } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get redirect URL from search params
  const redirectTo = searchParams.get('redirect') || '/dashboard';

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginForm) => {
    setIsLoading(true);

    try {
      const { data: authData, error } = await signIn(data.email, data.password);

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          setError('root', { message: 'Neispravni podaci za prijavu' });
        } else if (error.message.includes('Email not confirmed')) {
          setError('root', {
            message: 'Molimo potvrdite svoj email prije prijave',
          });
        } else {
          setError('root', { message: error.message });
        }
        return;
      }

      if (authData.user) {
        // Check if user has a profile, if not redirect to profile creation
        // Otherwise redirect to the intended page or dashboard
        if (
          redirectTo === '/dashboard' ||
          redirectTo.startsWith('/dashboard')
        ) {
          router.push('/dashboard'); // This will handle profile check and redirect appropriately
        } else {
          router.push(redirectTo);
        }
      }
    } catch {
      setError('root', { message: 'Došlo je do greške. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-instagram-secondary relative overflow-hidden flex flex-col">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/10"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

      {/* Header */}
      <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/"
            className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad</span>
          </Link>
          <div className="flex items-center space-x-3">
            <Image
              src="/images/influexus_logo_white.webp"
              alt="Influexus Logo"
              width={32}
              height={32}
              className="rounded-lg"
            />
            <span className="text-xl font-bold gradient-text-auth">
              Influexus
            </span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
        <Card className="w-full max-w-md glass-instagram border-white/20 shadow-2xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-white">Prijava</CardTitle>
            <CardDescription className="text-white/70">
              Unesite svoje podatke da biste se prijavili
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">
                  Email adresa
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={`bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20 ${errors.email ? 'border-red-400' : ''}`}
                />
                {errors.email && (
                  <p className="text-sm text-red-300">{errors.email.message}</p>
                )}
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-white">
                  Lozinka
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Unesite lozinku"
                    {...register('password')}
                    className={`bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20 ${errors.password ? 'border-red-400' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-white/10 text-white/60 hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-300">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <input
                    id="rememberMe"
                    type="checkbox"
                    {...register('rememberMe')}
                    className="rounded border-white/30 bg-white/10 text-instagram-purple focus:ring-white/20"
                  />
                  <Label htmlFor="rememberMe" className="text-sm text-white">
                    Zapamti me
                  </Label>
                </div>
                <Link
                  href="/zaboravljena-lozinka"
                  className="text-sm text-white/80 hover:text-white hover:underline transition-colors"
                >
                  Zaboravili ste lozinku?
                </Link>
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Prijavljivanje...
                  </>
                ) : (
                  'Prijavite se'
                )}
              </Button>

              {/* Root Error */}
              {errors.root && (
                <p className="text-sm text-red-300 text-center bg-red-500/20 p-3 rounded-lg border border-red-400/30">
                  {errors.root.message}
                </p>
              )}
            </form>

            {/* Registration Link */}
            <div className="mt-6 text-center">
              <p className="text-sm text-white/70">
                Nemate nalog?{' '}
                <Link
                  href="/registracija"
                  className="text-white hover:underline font-medium transition-colors"
                >
                  Registrujte se
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
