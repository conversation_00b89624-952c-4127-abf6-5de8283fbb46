'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Mail,
  MessageCircle,
  Heart,
  Sparkles,
  ExternalLink,
  Copy,
  Check,
} from 'lucide-react';
import { toast } from 'sonner';

interface SupportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SupportModal({ isOpen, onClose }: SupportModalProps) {
  const [emailCopied, setEmailCopied] = useState(false);
  const supportEmail = '<EMAIL>';

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setEmailCopied(false);
    }
  }, [isOpen]);

  const copyEmailToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(supportEmail);
      setEmailCopied(true);
      toast.success('Email adresa kopirana!');
      setTimeout(() => setEmailCopied(false), 2000);
    } catch {
      toast.error('Gre<PERSON>ka pri kopiranju email adrese');
    }
  };

  const openEmailClient = () => {
    window.location.href = `mailto:${supportEmail}?subject=Upit sa Influexus platforme`;
    // Close modal after opening email client
    setTimeout(() => onClose(), 100);
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md bg-card/95 backdrop-blur-md border border-purple-500/20 shadow-2xl shadow-purple-500/20 rounded-2xl overflow-hidden">
        {/* Decorative background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#7F5BFE]/5 via-[#F35BF6]/3 to-[#f04a13]/5 opacity-50"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-400/10 to-transparent rounded-full blur-2xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-400/10 to-transparent rounded-full blur-xl"></div>

        <div className="relative">
          <DialogHeader className="text-center space-y-4 pb-6">
            {/* Icon */}
            <div className="flex justify-center">
              <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-[#7F5BFE]/20 to-[#F35BF6]/20 border border-purple-500/20">
                <Heart className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
            </div>

            <div className="space-y-2">
              <DialogTitle className="text-2xl font-bold text-foreground flex items-center justify-center gap-2">
                <Sparkles className="h-5 w-5 text-purple-500" />
                Podrška
                <Sparkles className="h-5 w-5 text-purple-500" />
              </DialogTitle>

              <Badge className="bg-gradient-to-r from-[#7F5BFE] to-[#F35BF6] text-white border-0 text-xs">
                Uvek tu za vas
              </Badge>
            </div>

            <DialogDescription className="text-center text-muted-foreground leading-relaxed">
              Kontinuirano razvijamo i unapređujemo Influexus platformu da vam
              pružimo najbolje iskustvo.
            </DialogDescription>
          </DialogHeader>

          {/* Content */}
          <div className="space-y-6">
            {/* Info Card */}
            <div className="relative p-4 rounded-xl bg-gradient-to-r from-purple-500/5 via-pink-500/3 to-orange-500/5 border border-purple-500/20">
              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex-shrink-0 mt-0.5">
                  <MessageCircle className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold text-foreground">
                    Javite nam se za:
                  </h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Upite o funkcionalnostima</li>
                    <li>• Prijedloge za poboljšanja</li>
                    <li>• Tehnička pitanja ili probleme</li>
                    <li>• Povratne informacije</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Email Section */}
            <div className="space-y-3">
              <h4 className="font-semibold text-foreground text-center">
                Kontaktirajte nas
              </h4>

              <div className="flex items-center gap-2 p-3 rounded-xl bg-gradient-to-r from-accent/30 to-accent/20 border border-border/50">
                <Mail className="h-4 w-4 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                <span className="font-mono text-sm text-foreground flex-1">
                  {supportEmail}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyEmailToClipboard}
                  className="h-8 w-8 p-0 hover:bg-purple-500/10 transition-all duration-300 group"
                >
                  {emailCopied ? (
                    <Check className="h-3 w-3 text-green-600 group-hover:scale-110 transition-transform duration-300" />
                  ) : (
                    <Copy className="h-3 w-3 text-purple-600 dark:text-purple-400 group-hover:scale-110 transition-transform duration-300" />
                  )}
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-2">
              <Button
                onClick={openEmailClient}
                className="flex-1 bg-gradient-to-r from-[#7F5BFE] via-[#F35BF6] to-[#f04a13] text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group"
              >
                <Mail className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                Pošaljite email
                <ExternalLink className="h-3 w-3 ml-2 group-hover:scale-110 transition-transform duration-300" />
              </Button>

              <Button
                variant="outline"
                onClick={handleClose}
                className="px-6 hover:bg-gradient-to-r hover:from-accent/80 hover:to-accent/60 transition-all duration-300 hover:scale-105"
              >
                Zatvori
              </Button>
            </div>

            {/* Footer */}
            <div className="text-center pt-2">
              <p className="text-xs text-muted-foreground/70">
                Odgovorićemo vam u najkraćem mogućem roku
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
