---
type: 'manual'
---

UI Rule — Usage of shadcn-ui MCP Server
When a task requires building or modifying a user interface, you must use the tools provided by the shadcn-ui MCP server.

Planning Rule — Before UI Implementation:
Discover Available Assets
 Use list_components() and list_blocks() to explore all available UI elements in the MCP server.

Map the User Request to Assets
 Break down the user request into needed UI elements and match them to the available components or blocks.

Prefer Blocks Over Components
 Use get_block() wherever possible. Blocks are composite and ideal for common structures (e.g., login forms, dashboards, calendars). Use get_component() only when a block is not available or unnecessary.

Implementation Rule — Building the UI:
Always Get a Demo First
 Before using any UI element, call get_component_demo(name) to fully understand its props and structure.

Retrieve the Code Properly

For a single component → use get_component(name)

For a full block → use get_block(name)

Integrate and Customize
 Use the retrieved code in the application and customize it according to the context of the user's request — adjust props, logic, and layout as needed.
