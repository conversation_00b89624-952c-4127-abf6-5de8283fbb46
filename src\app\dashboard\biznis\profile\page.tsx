'use client';

import { useEffect, useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CountrySelector } from '@/components/ui/country-selector';
import { useAuth } from '@/contexts/AuthContext';
import {
  getProfile,
  updateProfile,
  getBusiness,
  updateBusiness,
  getBusinessTargetCategories,
  getBusinessPlatforms,
  updateBusinessPlatforms,
} from '@/lib/profiles';
import { Loader2, Save, Building, Globe, Users } from 'lucide-react';
import { toast } from 'sonner';

const profileSchema = z.object({
  username: z.string().optional(), // Username is read-only, no validation needed
  brand_name: z.string().min(2, 'I<PERSON> brenda mora imati najmanje 2 karaktera'),
  bio: z
    .string()
    .max(500, 'Bio može imati maksimalno 500 karaktera')
    .optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  website_url: z.string().optional().or(z.literal('')),
  // Social media handles
  instagram_handle: z.string().optional(),
  instagram_followers: z.number().min(0).optional(),
  tiktok_handle: z.string().optional(),
  tiktok_followers: z.number().min(0).optional(),
  youtube_handle: z.string().optional(),
  youtube_subscribers: z.number().min(0).optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;

export default function BusinessProfilePage() {
  // Mapiranje između punih imena zemalja i kodova za CountrySelector
  const countryNameToCode = (countryName: string): string => {
    const countryMap: { [key: string]: string } = {
      Albanija: 'albania',
      'Bosna i Hercegovina': 'bosnia-herzegovina',
      Bugarska: 'bulgaria',
      'Crna Gora': 'montenegro',
      Grčka: 'greece',
      Hrvatska: 'croatia',
      Kosovo: 'kosovo',
      'Sjeverna Makedonija': 'north-macedonia',
      Rumunija: 'romania',
      Srbija: 'serbia',
      Slovenija: 'slovenia',
    };
    return countryMap[countryName] || '';
  };

  const countryCodeToName = (countryCode: string): string => {
    const codeMap: { [key: string]: string } = {
      albania: 'Albanija',
      'bosnia-herzegovina': 'Bosna i Hercegovina',
      bulgaria: 'Bugarska',
      montenegro: 'Crna Gora',
      greece: 'Grčka',
      croatia: 'Hrvatska',
      kosovo: 'Kosovo',
      'north-macedonia': 'Sjeverna Makedonija',
      romania: 'Rumunija',
      serbia: 'Srbija',
      slovenia: 'Slovenija',
    };
    return codeMap[countryCode] || countryCode;
  };

  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [, setProfile] = useState<{
    id: string;
    username: string;
    full_name?: string;
    bio?: string;
    city?: string;
    country?: string;
    website_url?: string;
  } | null>(null);
  const [, setBusiness] = useState<{
    company_name: string;
  } | null>(null);
  const [categories, setCategories] = useState<
    {
      category_id: number;
      categories?: {
        icon?: string;
        name: string;
      };
    }[]
  >([]);
  const [, setPlatforms] = useState<
    {
      platform_id: number;
      handle?: string;
      followers_count?: number;
    }[]
  >([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
  });

  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Load profile
      const { data: profileData, error: profileError } = await getProfile(
        user!.id
      );
      if (profileError || !profileData) {
        toast.error('Greška pri učitavanju profila');
        return;
      }
      setProfile({
        ...profileData,
        username: profileData.username || '',
        full_name: profileData.full_name || undefined,
        bio: profileData.bio || undefined,
        city: profileData.city || undefined,
        country: profileData.country || undefined,
        website_url: profileData.website_url || undefined,
      });

      // Load business data
      const { data: businessData, error: businessError } = await getBusiness(
        user!.id
      );
      if (businessError || !businessData) {
        toast.error('Greška pri učitavanju business podataka');
        return;
      }
      setBusiness(businessData);

      // Load business categories
      const { data: categoriesData, error: categoriesError } =
        await getBusinessTargetCategories(user!.id);
      if (categoriesError) {
        console.error('Error loading categories:', categoriesError);
      } else {
        setCategories(
          (categoriesData || []).map((cat) => ({
            category_id: cat.category_id,
            categories: cat.categories
              ? {
                  icon: cat.categories.icon || undefined,
                  name: cat.categories.name,
                }
              : undefined,
          }))
        );
      }

      // Load business platforms
      const { data: platformsData, error: platformsError } =
        await getBusinessPlatforms(user!.id);
      if (platformsError) {
        console.error('Error loading platforms:', platformsError);
      } else {
        setPlatforms(
          (platformsData || [])
            .filter(p => p.platform_id !== null)
            .map(p => ({
              platform_id: p.platform_id!,
              handle: p.handle || undefined,
              followers_count: p.followers_count || undefined,
            }))
        );
      }

      // Find platform data for form
      const instagramPlatform = platformsData?.find(p => p.platform_id === 1);
      const tiktokPlatform = platformsData?.find(p => p.platform_id === 2);
      const youtubePlatform = platformsData?.find(p => p.platform_id === 3);

      // Popuni formu sa postojećim podacima
      reset({
        username: profileData.username || '',
        brand_name: businessData.company_name || profileData.full_name || '', // Prefer business.company_name over profile.full_name
        bio: profileData.bio || '',
        city: profileData.city || '',
        country: countryNameToCode(profileData.country || ''), // Convert country name to code
        website_url: profileData.website_url || '',
        instagram_handle: instagramPlatform?.handle || '',
        instagram_followers: instagramPlatform?.followers_count || 0,
        tiktok_handle: tiktokPlatform?.handle || '',
        tiktok_followers: tiktokPlatform?.followers_count || 0,
        youtube_handle: youtubePlatform?.handle || '',
        youtube_subscribers: youtubePlatform?.followers_count || 0,
      });
    } catch {
      toast.error('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  }, [user, reset]);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user, loadData]);

  const onSubmit = async (data: ProfileForm) => {
    if (!user) return;

    setSaving(true);
    try {
      // Normalize website URL - add https:// if no protocol is specified
      let websiteUrl = data.website_url;
      if (
        websiteUrl &&
        websiteUrl.trim() &&
        !websiteUrl.match(/^https?:\/\//)
      ) {
        websiteUrl = `https://${websiteUrl.trim()}`;
      }

      // Update profile (username is read-only, so we don't update it)
      const { error: profileError } = await updateProfile(user.id, {
        full_name: data.brand_name, // Brand name is stored in full_name for business profiles
        bio: data.bio || null,
        city: data.city || null,
        country: countryCodeToName(data.country || ''), // Convert country code to name
        website_url: websiteUrl || null,
      });

      if (profileError) {
        toast.error('Greška pri ažuriranju profila');
        return;
      }

      // Also update business record with brand name
      const { error: businessError } = await updateBusiness(user.id, {
        company_name: data.brand_name,
      });

      if (businessError) {
        console.error('Error updating business:', businessError);
        toast.error('Greška pri ažuriranju business podataka');
        return;
      }

      // Update business platforms
      const platformUpdates = [];
      if (data.instagram_handle) {
        platformUpdates.push({
          platform_id: 1,
          handle: data.instagram_handle,
          followers_count: data.instagram_followers || 0,
        });
      }
      if (data.tiktok_handle) {
        platformUpdates.push({
          platform_id: 2,
          handle: data.tiktok_handle,
          followers_count: data.tiktok_followers || 0,
        });
      }
      if (data.youtube_handle) {
        platformUpdates.push({
          platform_id: 3,
          handle: data.youtube_handle,
          followers_count: data.youtube_subscribers || 0,
        });
      }

      const { error: platformsError } = await updateBusinessPlatforms(
        user.id,
        platformUpdates
      );
      if (platformsError) {
        console.error('Error updating platforms:', platformsError);
        toast.error('Greška pri ažuriranju social media podataka');
        return;
      }

      toast.success('Profil je uspješno ažuriran');
      loadData(); // Refresh data
    } catch {
      toast.error('Neočekivana greška');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Postavke profila
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Upravljajte svojim javnim profilom koji vide influenceri
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Osnovne informacije */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
            <div className="relative p-6">
              <div className="flex items-center gap-2 mb-4">
                <Building className="h-5 w-5 text-purple-500" />
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Osnovne informacije
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                Ovi podaci se prikazuju na vašem javnom profilu
              </p>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label
                      htmlFor="username"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      Username
                    </Label>
                    <Input
                      id="username"
                      {...register('username')}
                      placeholder="@vasabrand"
                      disabled
                      className="bg-gray-100/60 dark:bg-gray-700/40 border-purple-200/50"
                    />
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      Username se ne može mijenjati
                    </p>
                  </div>

                  <div>
                    <Label
                      htmlFor="brand_name"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      Ime brenda *
                    </Label>
                    <Input
                      id="brand_name"
                      {...register('brand_name')}
                      placeholder="Nike, Coca-Cola, Apple..."
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                    {errors.brand_name && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.brand_name.message}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <Label
                    htmlFor="bio"
                    className="text-gray-900 dark:text-gray-100"
                  >
                    Opis kompanije
                  </Label>
                  <Textarea
                    id="bio"
                    {...register('bio')}
                    placeholder="Opišite vašu kompaniju i čime se bavite..."
                    rows={4}
                    className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                  />
                  {errors.bio && (
                    <p className="text-sm text-red-600 mt-1">
                      {errors.bio.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label
                      htmlFor="city"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      Grad
                    </Label>
                    <Input
                      id="city"
                      {...register('city')}
                      placeholder="Sarajevo"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>

                  <div>
                    <Label
                      htmlFor="country"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      Država
                    </Label>
                    <CountrySelector
                      value={watch('country')}
                      onValueChange={value => setValue('country', value)}
                      placeholder="Izaberite državu..."
                    />
                    {errors.country && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.country.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label
                      htmlFor="website_url"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      Website
                    </Label>
                    <Input
                      id="website_url"
                      {...register('website_url')}
                      placeholder="vaswebsite.com"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                    {errors.website_url && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.website_url.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Kategorije */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
            <div className="relative p-6">
              <div className="flex items-center gap-2 mb-4">
                <Users className="h-5 w-5 text-purple-500" />
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Kategorije/Industrija
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                Kategorije koje ste izabrali tokom onboardinga
              </p>

              {categories.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {categories.map(category => (
                    <div
                      key={category.category_id}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/60 dark:bg-gray-800/40 text-purple-700 dark:text-purple-300 border border-purple-200/50 dark:border-purple-700/50"
                    >
                      {category.categories?.icon && (
                        <span className="mr-1">{category.categories.icon}</span>
                      )}
                      {category.categories?.name}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 dark:text-gray-400">
                  Nema izabranih kategorija
                </p>
              )}
            </div>
          </div>

          {/* Social Media */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
            <div className="relative p-6">
              <div className="flex items-center gap-2 mb-4">
                <Globe className="h-5 w-5 text-purple-500" />
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Društvene mreže
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                Vaši profili na društvenim mrežama
              </p>

              <div className="space-y-4">
                {/* Instagram */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label
                      htmlFor="instagram_handle"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      Instagram handle
                    </Label>
                    <Input
                      id="instagram_handle"
                      {...register('instagram_handle')}
                      placeholder="vasabrand"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor="instagram_followers"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      Instagram pratilaca
                    </Label>
                    <Input
                      id="instagram_followers"
                      type="number"
                      {...register('instagram_followers', {
                        valueAsNumber: true,
                      })}
                      placeholder="0"
                      min="0"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>
                </div>

                {/* TikTok */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label
                      htmlFor="tiktok_handle"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      TikTok handle
                    </Label>
                    <Input
                      id="tiktok_handle"
                      {...register('tiktok_handle')}
                      placeholder="vasabrand"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor="tiktok_followers"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      TikTok pratilaca
                    </Label>
                    <Input
                      id="tiktok_followers"
                      type="number"
                      {...register('tiktok_followers', { valueAsNumber: true })}
                      placeholder="0"
                      min="0"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>
                </div>

                {/* YouTube */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label
                      htmlFor="youtube_handle"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      YouTube handle
                    </Label>
                    <Input
                      id="youtube_handle"
                      {...register('youtube_handle')}
                      placeholder="vasabrand"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor="youtube_subscribers"
                      className="text-gray-900 dark:text-gray-100"
                    >
                      YouTube pretplatnika
                    </Label>
                    <Input
                      id="youtube_subscribers"
                      type="number"
                      {...register('youtube_subscribers', {
                        valueAsNumber: true,
                      })}
                      placeholder="0"
                      min="0"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg disabled:opacity-50"
            >
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              Sačuvaj promjene
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
