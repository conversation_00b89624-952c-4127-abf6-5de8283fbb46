const fs = require('fs');
const path = require('path');

// Lista svih Error grešaka iz build_errors_2.md
const errorFixes = [
  // src/app/promjena-lozinke/page.tsx
  {
    file: 'src/app/promjena-lozinke/page.tsx',
    fixes: [
      { line: 20, type: 'removeImport', import: 'Lock' },
      { line: 51, type: 'removeVariable', variable: 'user' },
      { line: 96, type: 'removeCatchError' },
    ],
  },
  // src/app/registracija/business/page.tsx
  {
    file: 'src/app/registracija/business/page.tsx',
    fixes: [
      { line: 46, type: 'removeVariable', variable: 'signUp' },
      { line: 100, type: 'removeCatchError' },
    ],
  },
  // src/app/registracija/influencer/page.tsx
  {
    file: 'src/app/registracija/influencer/page.tsx',
    fixes: [
      { line: 42, type: 'removeVariable', variable: 'signUp' },
      { line: 92, type: 'removeCatchError' },
    ],
  },
  // src/app/reset-password/page.tsx
  {
    file: 'src/app/reset-password/page.tsx',
    fixes: [
      { line: 76, type: 'removeVariable', variable: 'accessToken' },
      { line: 77, type: 'removeVariable', variable: 'refreshToken' },
      { line: 120, type: 'removeCatchError' },
    ],
  },
  // src/app/zaboravljena-lozinka/page.tsx
  {
    file: 'src/app/zaboravljena-lozinka/page.tsx',
    fixes: [{ line: 62, type: 'removeCatchError' }],
  },
  // src/components/campaigns/BusinessOfferCard.tsx
  {
    file: 'src/components/campaigns/BusinessOfferCard.tsx',
    fixes: [
      { line: 10, type: 'removeImport', import: 'User' },
      { line: 11, type: 'removeImport', import: 'MessageCircle' },
    ],
  },
];

function fixFile(filePath, fixes) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let lines = content.split('\n');

    // Sort fixes by line number in descending order to avoid line number shifts
    fixes.sort((a, b) => b.line - a.line);

    for (const fix of fixes) {
      const lineIndex = fix.line - 1;
      if (lineIndex >= 0 && lineIndex < lines.length) {
        const line = lines[lineIndex];

        switch (fix.type) {
          case 'removeImport':
            // Remove specific import from import statement
            if (line.includes(fix.import)) {
              lines[lineIndex] = line.replace(
                new RegExp(`\\s*,?\\s*${fix.import}\\s*,?`, 'g'),
                ''
              );
              // Clean up empty import statements
              if (lines[lineIndex].match(/import\s*{\s*}\s*from/)) {
                lines.splice(lineIndex, 1);
              }
            }
            break;

          case 'removeVariable':
            // Replace variable with underscore
            if (line.includes(fix.variable)) {
              lines[lineIndex] = line.replace(
                new RegExp(`\\b${fix.variable}\\b`),
                '_'
              );
            }
            break;

          case 'removeCatchError':
            // Remove error parameter from catch block
            if (line.includes('catch (error)')) {
              lines[lineIndex] = line.replace('catch (error)', 'catch');
            } else if (line.includes('} catch (error) {')) {
              lines[lineIndex] = line.replace('} catch (error) {', '} catch {');
            }
            break;
        }
      }
    }

    const newContent = lines.join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✅ Fixed ${filePath}`);
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
}

// Apply fixes
for (const errorFix of errorFixes) {
  fixFile(errorFix.file, errorFix.fixes);
}

console.log('🎉 All error fixes applied!');
