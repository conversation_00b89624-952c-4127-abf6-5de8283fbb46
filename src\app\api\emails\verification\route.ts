import { NextRequest, NextResponse } from 'next/server';
import {
  sendEmail,
  generateEmailVerificationTemplate,
} from '@/lib/email-service';

export async function POST(request: NextRequest) {
  try {
    const { email, verificationUrl } = await request.json();

    if (!email || !verificationUrl) {
      return NextResponse.json(
        { error: 'Email and verification URL are required' },
        { status: 400 }
      );
    }

    const { htmlContent, textContent } = generateEmailVerificationTemplate(
      verificationUrl,
      email
    );

    const result = await sendEmail({
      to: email,
      subject: 'Potvrdite svoj email - INFLUEXUS',
      htmlContent,
      textContent,
    });

    if (result.success) {
      return NextResponse.json(
        { message: 'Verification email sent successfully' },
        { status: 200 }
      );
    } else {
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
