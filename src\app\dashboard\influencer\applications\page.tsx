'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Clock } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { supabase } from '@/lib/supabase';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import InfluencerApplicationCard from '@/components/campaigns/InfluencerApplicationCard';
import { withdrawCampaignApplication } from '@/lib/campaigns';
import { toast } from 'sonner';
import { WithdrawApplicationModal } from '@/components/applications/WithdrawApplicationModal';

interface Application {
  id: string;
  campaign_id: string;
  status: 'pending' | 'accepted' | 'rejected' | 'completed';
  proposed_rate: string;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaign_title: string;
  campaign_budget: number;
  campaign_business_id: string;
  business_username: string;
}

export default function InfluencerApplicationsPage() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [withdrawingId, setWithdrawingId] = useState<string | null>(null);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [applicationToWithdraw, setApplicationToWithdraw] = useState<
    string | null
  >(null);

  const fetchApplications = useCallback(async () => {
    try {
      const { data, error } = await supabase.rpc(
        'get_influencer_applications',
        {
          p_influencer_id: user?.id || '',
          p_limit: 50,
          p_offset: 0,
          p_status: undefined,
        }
      );

      if (error) throw error;
      setApplications((data || []) as Application[]);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    if (user?.id) {
      fetchApplications();
    }
  }, [user?.id, fetchApplications]);

  const handleWithdrawApplication = (applicationId: string) => {
    setApplicationToWithdraw(applicationId);
    setShowWithdrawModal(true);
  };

  const confirmWithdrawApplication = async () => {
    if (!applicationToWithdraw) return;

    setWithdrawingId(applicationToWithdraw);
    setShowWithdrawModal(false);
    try {
      const { error } = await withdrawCampaignApplication(
        applicationToWithdraw
      );

      if (error) {
        toast.error(
          (error as { message?: string })?.message ||
            'Greška pri povlačenju aplikacije'
        );
        return;
      }

      // Remove the application from the list
      setApplications(prev =>
        prev.filter(app => app.id !== applicationToWithdraw)
      );
      toast.success('Aplikacija je uspješno povučena');
    } catch (error) {
      console.error('Error withdrawing application:', error);
      toast.error('Neočekivana greška pri povlačenju aplikacije');
    } finally {
      setWithdrawingId(null);
      setApplicationToWithdraw(null);
    }
  };

  const filteredApplications = applications.filter(app => {
    if (activeTab === 'all') return true;
    return app.status === activeTab;
  });

  const stats = {
    total: applications.length,
    pending: applications.filter(app => app.status === 'pending').length,
    accepted: applications.filter(app => app.status === 'accepted').length,
    rejected: applications.filter(app => app.status === 'rejected').length,
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Učitavanje...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Moje aplikacije</h1>
          <p className="text-muted-foreground mt-1">
            Pregledajte status vaših aplikacija na kampanje
          </p>
        </div>

        {/* Gradient Tabs */}
        <GradientTabs
          tabs={[
            { name: 'Sve', value: 'all', count: stats.total },
            { name: 'Na čekanju', value: 'pending', count: stats.pending },
            { name: 'Prihvaćeno', value: 'accepted', count: stats.accepted },
            { name: 'Odbačeno', value: 'rejected', count: stats.rejected },
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-6"
        />

        {/* Applications List */}
        <div className="space-y-4">
          {filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {activeTab === 'all'
                  ? 'Nema aplikacija'
                  : `Nema aplikacija sa statusom "${activeTab}"`}
              </h3>
              <p className="text-gray-600">
                {activeTab === 'all'
                  ? 'Još uvijek niste aplicirali na nijednu kampanju.'
                  : 'Nema aplikacija sa ovim statusom.'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredApplications.map(application => (
                <InfluencerApplicationCard
                  key={application.id}
                  application={application}
                  onWithdraw={handleWithdrawApplication}
                  isWithdrawing={withdrawingId === application.id}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      <WithdrawApplicationModal
        isOpen={showWithdrawModal}
        onClose={() => {
          setShowWithdrawModal(false);
          setApplicationToWithdraw(null);
        }}
        onConfirm={confirmWithdrawApplication}
        isLoading={withdrawingId !== null}
      />
    </DashboardLayout>
  );
}
