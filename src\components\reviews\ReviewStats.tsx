'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Star, TrendingUp, Users, MessageSquare } from 'lucide-react';
import { ReviewStats as ReviewStatsType } from '@/lib/job-reviews';

interface ReviewStatsProps {
  stats: ReviewStatsType;
  className?: string;
}

export function ReviewStats({ stats, className }: ReviewStatsProps) {
  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= Math.round(rating)
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 4.0) return 'text-green-500';
    if (rating >= 3.5) return 'text-yellow-600';
    if (rating >= 3.0) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getMaxRatingCount = () => {
    return Math.max(
      stats.rating_distribution[5] || 0,
      stats.rating_distribution[4] || 0,
      stats.rating_distribution[3] || 0,
      stats.rating_distribution[2] || 0,
      stats.rating_distribution[1] || 0
    );
  };

  const maxCount = getMaxRatingCount();

  return (
    <div className={className}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Average Rating */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Average Rating</p>
                <div className="flex items-center gap-2 mt-1">
                  <p
                    className={`text-2xl font-bold ${getRatingColor(stats.average_rating)}`}
                  >
                    {stats.average_rating.toFixed(1)}
                  </p>
                  {renderStars(stats.average_rating)}
                </div>
              </div>
              <Star className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        {/* Total Reviews */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Reviews</p>
                <p className="text-2xl font-bold">{stats.total_reviews}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        {/* Recent Reviews */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">
                  {(stats as any).recent_reviews_count}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        {/* Positive Rate */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Positive Rate</p>
                <p className="text-2xl font-bold">
                  {stats.total_reviews > 0
                    ? Math.round(
                        (((stats.rating_distribution[4] || 0) +
                          (stats.rating_distribution[5] || 0)) /
                          stats.total_reviews) *
                          100
                      )
                    : 0}
                  %
                </p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Rating Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Rating Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[5, 4, 3, 2, 1].map(rating => {
              const count = stats.rating_distribution[rating] || 0;
              const percentage =
                stats.total_reviews > 0
                  ? (count / stats.total_reviews) * 100
                  : 0;
              const barWidth = maxCount > 0 ? (count / maxCount) * 100 : 0;

              return (
                <div key={rating} className="flex items-center gap-4">
                  <div className="flex items-center gap-1 w-16">
                    <span className="text-sm font-medium">{rating}</span>
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  </div>

                  <div className="flex-1">
                    <Progress value={barWidth} className="h-2" />
                  </div>

                  <div className="flex items-center gap-2 w-20 text-right">
                    <span className="text-sm text-muted-foreground">
                      {count}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      ({percentage.toFixed(0)}%)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>

          {stats.total_reviews === 0 && (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No reviews yet</h3>
              <p className="text-muted-foreground">
                Complete some campaigns to start receiving reviews.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
