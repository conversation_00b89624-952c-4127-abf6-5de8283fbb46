import React from 'react';
import { cn } from '@/lib/utils';
import { ArrowLeft } from 'lucide-react';

interface BackButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode;
  showIcon?: boolean;
  className?: string;
}

const BackButton = React.forwardRef<HTMLButtonElement, BackButtonProps>(
  ({ children = 'Nazad', showIcon = true, className, ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={cn(
          'inline-flex items-center gap-2 px-3 py-2 text-sm font-medium',
          'bg-transparent border-0',
          'text-foreground/80 hover:text-foreground',
          'hover:bg-accent/50 dark:hover:bg-accent/30',
          'rounded-lg transition-all duration-300 ease-in-out',
          'hover:scale-105 hover:shadow-sm',
          'focus:outline-none focus:ring-2 focus:ring-purple-500/20 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'group',
          className
        )}
        {...props}
      >
        {showIcon && (
          <ArrowLeft className="w-4 h-4 transition-transform duration-300 group-hover:scale-110 group-hover:-translate-x-0.5" />
        )}
        <span className="transition-all duration-300 group-hover:font-semibold">
          {children}
        </span>
      </button>
    );
  }
);

BackButton.displayName = 'BackButton';

export { BackButton };
