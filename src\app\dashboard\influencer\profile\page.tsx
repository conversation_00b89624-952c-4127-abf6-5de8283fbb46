'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CountrySelector } from '@/components/ui/country-selector';
import { useAuth } from '@/contexts/AuthContext';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import {
  getProfile,
  updateProfile,
  getInfluencer,
  getInfluencerPlatforms,
  getInfluencerCategories,
} from '@/lib/profiles';
import { supabase } from '@/lib/supabase';
import {
  Loader2,
  Save,
  User,
  Globe,
  Package,
  Eye,
  Plus,
  Crown,
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import {
  getInfluencerPackages,
  type PricingPackage,
} from '@/lib/pricing-packages';
import { AvatarUpload } from '@/components/profile/AvatarUpload';
import { InfluencerCard } from '@/components/marketplace/InfluencerCard';
import type { InfluencerSearchResult } from '@/lib/marketplace';
import { UpgradeRequiredModal } from '@/components/modals/UpgradeRequiredModal';

const profileSchema = z.object({
  username: z.string().min(3, 'Username mora imati najmanje 3 karaktera'),
  public_display_name: z
    .string()
    .max(100, 'Ime može imati maksimalno 100 karaktera')
    .optional(),
  bio: z
    .string()
    .max(500, 'Bio može imati maksimalno 500 karaktera')
    .optional(),
  location: z.string().optional(),
  country: z.string().optional(),
  instagram_handle: z.string().optional(),
  instagram_followers: z.number().min(0).optional(),
  tiktok_handle: z.string().optional(),
  tiktok_followers: z.number().min(0).optional(),
  youtube_handle: z.string().optional(),
  youtube_subscribers: z.number().min(0).optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;

export default function InfluencerProfilePage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [profile, setProfile] = useState<any>(null);
  const [packages, setPackages] = useState<PricingPackage[]>([]);
  const [packagesLoading, setPackagesLoading] = useState(true);
  const [customOffersEnabled, setCustomOffersEnabled] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);

  // Mapiranje između punih imena zemalja i kodova za CountrySelector
  const countryNameToCode = (countryName: string): string => {
    const countryMap: { [key: string]: string } = {
      Albanija: 'albania',
      'Bosna i Hercegovina': 'bosnia-herzegovina',
      Bugarska: 'bulgaria',
      'Crna Gora': 'montenegro',
      Grčka: 'greece',
      Hrvatska: 'croatia',
      Kosovo: 'kosovo',
      'Sjeverna Makedonija': 'north-macedonia',
      Rumunija: 'romania',
      Srbija: 'serbia',
      Slovenija: 'slovenia',
    };
    return countryMap[countryName] || '';
  };

  const countryCodeToName = (countryCode: string): string => {
    const codeMap: { [key: string]: string } = {
      albania: 'Albanija',
      'bosnia-herzegovina': 'Bosna i Hercegovina',
      bulgaria: 'Bugarska',
      montenegro: 'Crna Gora',
      greece: 'Grčka',
      croatia: 'Hrvatska',
      kosovo: 'Kosovo',
      'north-macedonia': 'Sjeverna Makedonija',
      romania: 'Rumunija',
      serbia: 'Srbija',
      slovenia: 'Slovenija',
    };
    return codeMap[countryCode] || countryCode;
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
  });

  useEffect(() => {
    if (user) {
      loadData();
      checkSubscription();
    }
  }, [user]);

  // Watch form values for preview
  const watchedValues = watch();

  // Create preview data for InfluencerCard
  const createPreviewData = (): InfluencerSearchResult => {
    const totalFollowers =
      (watchedValues.instagram_followers || 0) +
      (watchedValues.tiktok_followers || 0) +
      (watchedValues.youtube_subscribers || 0);

    const platforms = [];
    if (
      watchedValues.instagram_followers &&
      watchedValues.instagram_followers > 0
    ) {
      platforms.push({
        platform_id: 1,
        platform_name: 'Instagram',
        platform_icon: 'Instagram',
        handle: watchedValues.instagram_handle || '@username',
        followers_count: watchedValues.instagram_followers,
        is_verified: false,
      });
    }
    if (watchedValues.tiktok_followers && watchedValues.tiktok_followers > 0) {
      platforms.push({
        platform_id: 2,
        platform_name: 'TikTok',
        platform_icon: 'TikTok',
        handle: watchedValues.tiktok_handle || '@username',
        followers_count: watchedValues.tiktok_followers,
        is_verified: false,
      });
    }
    if (
      watchedValues.youtube_subscribers &&
      watchedValues.youtube_subscribers > 0
    ) {
      platforms.push({
        platform_id: 3,
        platform_name: 'YouTube',
        platform_icon: 'YouTube',
        handle: watchedValues.youtube_handle || '@username',
        followers_count: watchedValues.youtube_subscribers,
        is_verified: false,
      });
    }

    return {
      id: user?.id || 'preview',
      username: watchedValues.username || 'username',
      full_name: watchedValues.public_display_name || 'Vaše ime',
      avatar_url: profile?.avatar_url || '',
      navbar_avatar_url: profile?.navbar_avatar_url || '',
      card_avatar_url: profile?.card_avatar_url || '',
      profile_avatar_url: profile?.profile_avatar_url || '',
      preview_avatar_url: profile?.preview_avatar_url || '',
      bio: watchedValues.bio || 'Vaš bio...',
      location: watchedValues.location || 'Lokacija',
      gender: 'prefer_not_to_say',
      age: 0,
      subscription_type: hasActiveSubscription ? 'premium' : 'free',
      custom_offers_enabled: customOffersEnabled,
      categories: [],
      platforms,
      pricing: [],
      min_price: 0,
      max_price: 0,
      total_followers: totalFollowers,
      relevance_score: 1.0,
      average_rating: 0,
      total_reviews: 0,
    };
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const { data: profileData, error: profileError } = await getProfile(
        user!.id
      );
      if (profileError) {
        toast.error('Greška pri učitavanju profila');
        return;
      }

      // Load influencer data
      const { data: influencerData, error: influencerError } =
        await getInfluencer(user!.id);
      if (influencerError) {
        toast.error('Greška pri učitavanju influencer podataka');
        return;
      }

      // Load influencer platforms
      const { data: platformsData } = await getInfluencerPlatforms(user!.id);

      // Extract platform data
      const instagramPlatform = platformsData?.find(p => p.platform_id === 1);
      const tiktokPlatform = platformsData?.find(p => p.platform_id === 2);
      const youtubePlatform = platformsData?.find(p => p.platform_id === 3);

      // Load influencer categories
      const { data: categoriesData, error: categoriesError } =
        await getInfluencerCategories(user!.id);
      if (!categoriesError && categoriesData) {
        setCategories(categoriesData);
      }

      // Load pricing packages
      loadPackages();

      // Set profile data for additional info section (combine profile and influencer data)
      setProfile({
        ...profileData,
        ...influencerData,
      });

      // Set custom offers enabled state from influencer data
      setCustomOffersEnabled(influencerData?.custom_offers_enabled || false);

      // Popuni formu sa postojećim podacima
      reset({
        username: profileData?.username || '',
        public_display_name: profileData?.public_display_name || '',
        bio: profileData?.bio || '',
        location: profileData?.city || '', // Use city instead of location
        country: countryNameToCode(profileData?.country || ''), // Convert country name to code
        instagram_handle: instagramPlatform?.handle || '',
        instagram_followers: instagramPlatform?.followers_count || 0,
        tiktok_handle: tiktokPlatform?.handle || '',
        tiktok_followers: tiktokPlatform?.followers_count || 0,
        youtube_handle: youtubePlatform?.handle || '',
        youtube_subscribers: youtubePlatform?.followers_count || 0,
      });
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Greška pri učitavanju podataka');
    } finally {
      setLoading(false);
    }
  };

  const loadPackages = async () => {
    try {
      setPackagesLoading(true);
      const { data: packagesData, error: packagesError } =
        await getInfluencerPackages(user!.id);

      if (packagesError) {
        console.error('Error loading packages:', packagesError);
        setPackages([]);
      } else {
        setPackages(packagesData || []);
      }
    } catch (error) {
      console.error('Error loading packages:', error);
      setPackages([]);
    } finally {
      setPackagesLoading(false);
    }
  };

  const checkSubscription = async () => {
    if (user) {
      try {
        const { data: subscription } = await supabase
          .from('user_subscriptions')
          .select('status')
          .eq('user_id', user.id)
          .eq('user_type', 'influencer')
          .eq('status', 'active')
          .single();

        setHasActiveSubscription(!!subscription);
      } catch (error) {
        console.error('Error checking subscription:', error);
        setHasActiveSubscription(false);
      }
    }
  };

  const handleCustomOffersToggle = async (enabled: boolean) => {
    if (enabled && !hasActiveSubscription) {
      setShowUpgradeModal(true);
      return;
    }

    try {
      // Update in database
      const { error } = await supabase
        .from('influencers')
        .update({ custom_offers_enabled: enabled })
        .eq('id', user!.id);

      if (error) {
        console.error('Error updating custom offers setting:', error);
        toast.error('Greška pri čuvanju postavke');
        return;
      }

      setCustomOffersEnabled(enabled);
      toast.success(
        enabled
          ? 'Uspješno ste aktivirali primanje custom ponuda'
          : 'Uspješno ste deaktivirali primanje custom ponuda'
      );
    } catch (error) {
      console.error('Error updating custom offers setting:', error);
      toast.error('Greška pri čuvanju postavke');
    }
  };

  const onSubmit = async (data: ProfileForm) => {
    try {
      setSaving(true);

      // Update profile
      const { error: profileError } = await updateProfile(user!.id, {
        username: data.username,
        public_display_name: data.public_display_name || null,
        bio: data.bio || null,
        city: data.location || null, // Use city instead of location
        country: countryCodeToName(data.country || ''), // Convert country code to name
      });

      if (profileError) {
        toast.error('Greška pri ažuriranju profila');
        return;
      }

      // Update platform data using the new platform system
      const platformUpdates = [
        {
          platform_id: 1,
          handle: data.instagram_handle,
          followers_count: data.instagram_followers,
        },
        {
          platform_id: 2,
          handle: data.tiktok_handle,
          followers_count: data.tiktok_followers,
        },
        {
          platform_id: 3,
          handle: data.youtube_handle,
          followers_count: data.youtube_subscribers,
        },
      ];

      for (const platform of platformUpdates) {
        if (platform.handle) {
          // Upsert platform data
          const { error: platformError } = await supabase
            .from('influencer_platforms')
            .upsert({
              influencer_id: user!.id,
              platform_id: platform.platform_id,
              handle: platform.handle,
              followers_count: platform.followers_count || 0,
              is_active: true,
            });

          if (platformError) {
            console.error('Error updating platform:', platformError);
          }
        }
      }

      toast.success('Profil je uspješno ažuriran!');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Greška pri ažuriranju profila');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Profil
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Upravljajte vašim profilom i informacijama
          </p>
        </div>

        {/* New Layout with smaller cards */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          {/* Main Content - Left Side */}
          <div className="lg:col-span-3">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Mobile: Stack vertically, Desktop: Profile Picture and Additional Info in same row */}

              {/* Profile Picture - Always first */}
              <div className="bg-white/70 dark:bg-gray-800/70 border border-gray-200 dark:border-gray-700 rounded-lg p-4 lg:hidden">
                <div className="flex items-center gap-2 mb-3">
                  <User className="h-4 w-4 text-purple-500" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Profilna slika
                  </h3>
                </div>

                <AvatarUpload
                  userId={user!.id}
                  currentAvatarUrl={profile?.avatar_url || undefined}
                  onUploadComplete={() => {
                    toast.success('Profilna slika je uspješno upload-ovana!');
                    loadData();
                  }}
                  onUploadError={error => {
                    toast.error(`Greška pri upload-u: ${error}`);
                  }}
                />
              </div>

              {/* Desktop layout - Profile Picture and Additional Info in same row */}
              <div className="hidden lg:grid lg:grid-cols-3 gap-4">
                {/* Profile Picture - Left side */}
                <div className="bg-white/70 dark:bg-gray-800/70 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <User className="h-4 w-4 text-purple-500" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      Profilna slika
                    </h3>
                  </div>

                  <AvatarUpload
                    userId={user!.id}
                    currentAvatarUrl={profile?.avatar_url || undefined}
                    onUploadComplete={() => {
                      toast.success('Profilna slika je uspješno upload-ovana!');
                      loadData();
                    }}
                    onUploadError={error => {
                      toast.error(`Greška pri upload-u: ${error}`);
                    }}
                  />
                </div>

                {/* Additional Profile Information - Right side (Desktop only) */}
                <div className="lg:col-span-2 bg-white/70 dark:bg-gray-800/70 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <User className="h-4 w-4 text-purple-500" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      Dodatne informacije
                    </h3>
                  </div>

                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {profile?.age && (
                        <div>
                          <Label className="text-xs font-medium text-gray-500">
                            Godine
                          </Label>
                          <div className="text-gray-900 dark:text-gray-100">
                            {profile.age}
                          </div>
                        </div>
                      )}

                      {profile?.gender && (
                        <div>
                          <Label className="text-xs font-medium text-gray-500">
                            Pol
                          </Label>
                          <div className="text-gray-900 dark:text-gray-100">
                            {profile.gender === 'male'
                              ? 'Muško'
                              : profile.gender === 'female'
                                ? 'Žensko'
                                : 'Ostalo'}
                          </div>
                        </div>
                      )}
                    </div>

                    {categories && categories.length > 0 && (
                      <div>
                        <Label className="text-xs font-medium text-gray-500 mb-2 block">
                          Kategorije
                        </Label>
                        <div className="flex flex-wrap gap-1">
                          {categories.map((category, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="text-xs"
                            >
                              {category.categories?.name ||
                                category.category_name}
                            </Badge>
                          ))}
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          Za izmenu kontaktirajte podršku.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Two-column layout for main sections */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Basic Information */}
                <div className="bg-white/70 dark:bg-gray-800/70 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <User className="h-4 w-4 text-purple-500" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      Osnovne informacije
                    </h3>
                  </div>
                  <div className="space-y-3">
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Label
                          htmlFor="username"
                          className="text-sm font-medium"
                        >
                          Username
                        </Label>
                        <Input
                          id="username"
                          {...register('username')}
                          placeholder="Unesite username"
                          disabled
                          className="bg-gray-100 dark:bg-gray-700 cursor-not-allowed"
                        />
                        <p className="text-xs text-gray-500">
                          Username se ne može mijenjati
                        </p>
                        {errors.username && (
                          <p className="text-sm text-red-600">
                            {errors.username.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="public_display_name"
                          className="text-sm font-medium"
                        >
                          Javno ime
                        </Label>
                        <Input
                          id="public_display_name"
                          {...register('public_display_name')}
                          placeholder="Ime i prezime"
                        />
                        {errors.public_display_name && (
                          <p className="text-sm text-red-600">
                            {errors.public_display_name.message}
                          </p>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div className="space-y-2">
                          <Label
                            htmlFor="location"
                            className="text-sm font-medium"
                          >
                            Grad
                          </Label>
                          <Input
                            id="location"
                            {...register('location')}
                            placeholder="Grad"
                          />
                          {errors.location && (
                            <p className="text-sm text-red-600">
                              {errors.location.message}
                            </p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label
                            htmlFor="country"
                            className="text-sm font-medium"
                          >
                            Država
                          </Label>
                          <CountrySelector
                            value={watch('country')}
                            onValueChange={value => setValue('country', value)}
                            placeholder="Izaberite državu..."
                          />
                          {errors.country && (
                            <p className="text-sm text-red-600">
                              {errors.country.message}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bio" className="text-sm font-medium">
                          Bio
                        </Label>
                        <Textarea
                          id="bio"
                          {...register('bio')}
                          placeholder="Opišite sebe..."
                          rows={3}
                        />
                        {errors.bio && (
                          <p className="text-sm text-red-600">
                            {errors.bio.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Mobile: Additional Profile Information - After Basic Info */}
                <div className="lg:hidden bg-white/70 dark:bg-gray-800/70 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <User className="h-4 w-4 text-purple-500" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      Dodatne informacije
                    </h3>
                  </div>

                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {profile?.age && (
                        <div>
                          <Label className="text-xs font-medium text-gray-500">
                            Godine
                          </Label>
                          <div className="text-gray-900 dark:text-gray-100">
                            {profile.age}
                          </div>
                        </div>
                      )}

                      {profile?.gender && (
                        <div>
                          <Label className="text-xs font-medium text-gray-500">
                            Pol
                          </Label>
                          <div className="text-gray-900 dark:text-gray-100">
                            {profile.gender === 'male'
                              ? 'Muško'
                              : profile.gender === 'female'
                                ? 'Žensko'
                                : 'Ostalo'}
                          </div>
                        </div>
                      )}
                    </div>

                    {categories && categories.length > 0 && (
                      <div>
                        <Label className="text-xs font-medium text-gray-500 mb-2 block">
                          Kategorije
                        </Label>
                        <div className="flex flex-wrap gap-1">
                          {categories.map((category, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="text-xs"
                            >
                              {category.categories?.name ||
                                category.category_name}
                            </Badge>
                          ))}
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          Za izmenu kontaktirajte podršku.
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Social Media */}
                <div className="bg-white/70 dark:bg-gray-800/70 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Globe className="h-4 w-4 text-purple-500" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      Društvene mreže
                    </h3>
                  </div>

                  <div className="space-y-3">
                    {/* Instagram */}
                    <div className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <PlatformIconSimple platform="Instagram" size="md" />
                        <Label className="text-sm font-medium">Instagram</Label>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          {...register('instagram_handle')}
                          placeholder="@username"
                          className="text-sm"
                        />
                        <Input
                          type="number"
                          {...register('instagram_followers', {
                            valueAsNumber: true,
                          })}
                          placeholder="Pratilaca"
                          className="text-sm"
                        />
                      </div>
                    </div>

                    {/* TikTok */}
                    <div className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <PlatformIconSimple platform="TikTok" size="md" />
                        <Label className="text-sm font-medium">TikTok</Label>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          {...register('tiktok_handle')}
                          placeholder="@username"
                          className="text-sm"
                        />
                        <Input
                          type="number"
                          {...register('tiktok_followers', {
                            valueAsNumber: true,
                          })}
                          placeholder="Pratilaca"
                          className="text-sm"
                        />
                      </div>
                    </div>

                    {/* YouTube */}
                    <div className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <PlatformIconSimple platform="YouTube" size="md" />
                        <Label className="text-sm font-medium">YouTube</Label>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          {...register('youtube_handle')}
                          placeholder="@username"
                          className="text-sm"
                        />
                        <Input
                          type="number"
                          {...register('youtube_subscribers', {
                            valueAsNumber: true,
                          })}
                          placeholder="Pretplatnika"
                          className="text-sm"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end mt-4">
                <button
                  type="submit"
                  disabled={saving}
                  className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-purple-500 hover:bg-purple-600 rounded-lg transition-colors disabled:opacity-50"
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Čuvanje...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      Sačuvaj
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Preview Sidebar - Right Side */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-6 space-y-4">
              {/* Pricing Packages - First */}
              <div className="bg-white/70 dark:bg-gray-800/70 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Package className="h-4 w-4 text-purple-500" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Paketi i cijene
                  </h3>
                </div>

                {packagesLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      Učitavanje...
                    </span>
                  </div>
                ) : packages.length > 0 ? (
                  <div className="space-y-2">
                    {packages.slice(0, 2).map(pkg => (
                      <div
                        key={pkg.id}
                        className="flex items-center justify-between p-2 border rounded text-xs hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                      >
                        <div className="flex items-center space-x-2">
                          <PlatformIconSimple
                            platform={pkg.platform_name}
                            size="sm"
                          />
                          <div>
                            <p className="font-medium">
                              {pkg.auto_generated_name}
                            </p>
                            <p className="text-gray-500">{pkg.platform_name}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">
                            {pkg.price} {pkg.currency}
                          </p>
                        </div>
                      </div>
                    ))}
                    {packages.length > 2 && (
                      <p className="text-xs text-gray-500 text-center py-1">
                        +{packages.length - 2} više paketa
                      </p>
                    )}
                    <div className="flex justify-center pt-2">
                      <Link href="/dashboard/influencer/pricing">
                        <button className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg">
                          <Package className="h-4 w-4" />
                          Upravljaj paketima
                        </button>
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <Package className="h-6 w-6 mx-auto text-gray-400 mb-2" />
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      Još nema paketa
                    </p>
                    <Link href="/dashboard/influencer/pricing">
                      <button className="inline-flex items-center gap-1 px-3 py-1 text-xs font-medium text-white bg-purple-500 hover:bg-purple-600 rounded transition-colors">
                        <Plus className="h-3 w-3" />
                        Kreiraj
                      </button>
                    </Link>
                  </div>
                )}

                {/* Custom Offers Toggle - Prominent */}
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600 bg-gradient-to-r from-purple-50/50 via-pink-50/30 to-purple-50/50 dark:from-purple-900/20 dark:via-pink-900/10 dark:to-purple-900/20 rounded-lg p-3 -mx-1">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                        <Crown className="w-4 h-4 text-purple-500" />
                        Custom ponude
                      </h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Omogućite brendovima personalizovane ponude
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={customOffersEnabled}
                        onChange={e =>
                          handleCustomOffersToggle(e.target.checked)
                        }
                        className="sr-only peer"
                      />
                      <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-gradient-to-r peer-checked:from-purple-500 peer-checked:to-pink-500"></div>
                    </label>
                  </div>
                  {!hasActiveSubscription && (
                    <div className="flex items-center gap-2 p-2 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/30 dark:to-yellow-900/30 border border-amber-200 dark:border-amber-700 rounded-lg text-xs">
                      <Crown className="w-4 h-4 text-amber-500" />
                      <div>
                        <p className="font-medium text-amber-800 dark:text-amber-200">
                          Premium funkcija
                        </p>
                        <p className="text-amber-700 dark:text-amber-300">
                          Potreban Premium plan za custom ponude
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Preview Card - Second */}
              <div className="bg-white/70 dark:bg-gray-800/70 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Eye className="h-4 w-4 text-purple-500" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Preview
                  </h3>
                  <Badge variant="secondary" className="text-xs">
                    Live
                  </Badge>
                </div>
                <p className="text-xs text-gray-500 mb-4">
                  Kako vas vide brendovi u marketplace-u
                </p>

                <div className="max-w-sm mx-auto">
                  <InfluencerCard
                    influencer={createPreviewData()}
                    disableClick={true}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Required Modal */}
      <UpgradeRequiredModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        feature="custom_offers_receiving"
      />
    </DashboardLayout>
  );
}
