import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Star, Instagram, Filter } from 'lucide-react';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import Image from 'next/image';

interface Influencer {
  id: number;
  bio: string;
  price: string;
  rating: number;
  instagram: string;
  tiktok: string;
  instagramFollowers: string;
  tiktokFollowers: string;
  city: string;
  country: string;
  image: string;
  category: string;
  verified: boolean;
  packages: { name: string; price: string }[];
}

const influencers: Influencer[] = [
  {
    id: 1,
    bio: 'Fashion & lifestyle content creator passionate about sustainable fashion and wellness.',
    price: 'from 200€',
    rating: 4.9,
    instagram: '@sarahj',
    tiktok: '@sarahj',
    instagramFollowers: '25k',
    tiktokFollowers: '11.5k',
    city: 'Berlin',
    country: 'Germany',
    image: '/professional-influencer-portrait.png',
    category: 'Fashion',
    verified: true,
    packages: [
      { name: 'Instagram Photo Feed', price: '300€' },
      { name: 'Story Package', price: '150€' },
    ],
  },
  {
    id: 2,
    bio: 'Tech reviewer and gadget enthusiast. Helping people make smart tech decisions.',
    price: 'from 350€',
    rating: 4.8,
    instagram: '@miketech',
    tiktok: '@miketech',
    instagramFollowers: '45k',
    tiktokFollowers: '32k',
    city: 'Amsterdam',
    country: 'Netherlands',
    image: '/professional-influencer-portrait.png',
    category: 'Tech',
    verified: true,
    packages: [
      { name: 'Product Review Video', price: '500€' },
      { name: 'Tech Unboxing', price: '350€' },
    ],
  },
  {
    id: 3,
    bio: 'Food blogger sharing delicious recipes and restaurant reviews across Europe.',
    price: 'from 150€',
    rating: 4.7,
    instagram: '@emmaeats',
    tiktok: '@emmaeats',
    instagramFollowers: '18k',
    tiktokFollowers: '8.2k',
    city: 'Paris',
    country: 'France',
    image: '/professional-influencer-portrait.png',
    category: 'Food',
    verified: false,
    packages: [
      { name: 'Restaurant Review', price: '200€' },
      { name: 'Recipe Creation', price: '150€' },
    ],
  },
  {
    id: 4,
    bio: 'Fitness coach and wellness advocate. Inspiring healthy lifestyle changes daily.',
    price: 'from 280€',
    rating: 4.9,
    instagram: '@alexfitness',
    tiktok: '@alexfitness',
    instagramFollowers: '67k',
    tiktokFollowers: '89k',
    city: 'Barcelona',
    country: 'Spain',
    image: '/professional-influencer-portrait.png',
    category: 'Fitness',
    verified: true,
    packages: [
      { name: 'Workout Tutorial', price: '400€' },
      { name: 'Fitness Challenge', price: '280€' },
    ],
  },
  {
    id: 5,
    bio: 'Travel photographer capturing hidden gems and authentic cultural experiences.',
    price: 'from 320€',
    rating: 4.6,
    instagram: '@lunatravel',
    tiktok: '@lunatravel',
    instagramFollowers: '52k',
    tiktokFollowers: '28k',
    city: 'Milan',
    country: 'Italy',
    image: '/professional-influencer-portrait.png',
    category: 'Travel',
    verified: true,
    packages: [
      { name: 'Destination Feature', price: '450€' },
      { name: 'Travel Story Series', price: '320€' },
    ],
  },
];

function InfluencerCard({ influencer }: { influencer: Influencer }) {
  return (
    <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-xl">
      <Card className="w-full overflow-hidden bg-white h-full">
        <CardContent className="p-0">
          {/* Image section */}
          <div className="relative aspect-[4/3] max-h-48">
            <Image
              src={influencer.image || '/placeholder.svg'}
              alt={`${influencer.instagram} profile`}
              fill
              className="object-cover"
            />

            {/* Verified badge - top left */}
            {influencer.verified && (
              <div className="absolute top-3 left-3">
                <Badge
                  variant="secondary"
                  className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0 px-2 py-1"
                >
                  <Image
                    src="/images/influexus_logo_white_small_300x300.webp"
                    alt="Verified"
                    width={12}
                    height={12}
                    className="w-3 h-3 mr-1"
                  />
                  Verified
                </Badge>
              </div>
            )}

            {/* Star rating - top right */}
            <div className="absolute top-3 right-3">
              <Badge
                variant="secondary"
                className="bg-black/70 text-white border-0 px-2 py-1"
              >
                <Star className="w-3 h-3 mr-1 fill-yellow-400 text-yellow-400" />
                {influencer.rating}
              </Badge>
            </div>

            {/* Category badge - bottom left */}
            <div className="absolute bottom-3 left-3">
              <Badge variant="outline" className="bg-white/90 text-xs">
                {influencer.category}
              </Badge>
            </div>
          </div>

          {/* Info section */}
          <div className="p-4 space-y-3">
            {/* Username and location */}
            <div className="text-left">
              <h3 className="text-sm font-semibold">{influencer.instagram}</h3>
              <p className="text-xs text-gray-500">
                {influencer.city}, {influencer.country}
              </p>
            </div>

            {/* Bio */}
            <p className="text-xs text-gray-600 line-clamp-2">
              {influencer.bio}
            </p>

            {/* Social handles with follower counts */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-full">
                    <PlatformIconSimple
                      platform="Instagram"
                      size="sm"
                      variant="monochrome"
                      className="text-white"
                    />
                  </div>
                  <span>{influencer.instagram}</span>
                </div>
                <span className="text-xs font-medium text-gray-700">
                  {influencer.instagramFollowers}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-full">
                    <PlatformIconSimple
                      platform="TikTok"
                      size="sm"
                      variant="monochrome"
                      className="text-white"
                    />
                  </div>
                  <span>{influencer.tiktok}</span>
                </div>
                <span className="text-xs font-medium text-gray-700">
                  {influencer.tiktokFollowers}
                </span>
              </div>
            </div>

            {/* Separator */}
            <hr className="border-gray-200" />

            {/* Packages */}
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-gray-700">Packages</h4>
              {influencer.packages.map((pkg, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">{pkg.name}</span>
                  <span className="text-xs font-medium text-gradient bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent">
                    {pkg.price}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function InfluencerMarketplace() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Influencer Marketplace
          </h1>
          <p className="text-gray-600">
            Discover and connect with top content creators
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Filters:
              </span>
            </div>

            <Select>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="fashion">Fashion</SelectItem>
                <SelectItem value="tech">Tech</SelectItem>
                <SelectItem value="food">Food</SelectItem>
                <SelectItem value="fitness">Fitness</SelectItem>
                <SelectItem value="travel">Travel</SelectItem>
              </SelectContent>
            </Select>

            <Select>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Price Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Prices</SelectItem>
                <SelectItem value="0-200">€0 - €200</SelectItem>
                <SelectItem value="200-300">€200 - €300</SelectItem>
                <SelectItem value="300-500">€300 - €500</SelectItem>
                <SelectItem value="500+">€500+</SelectItem>
              </SelectContent>
            </Select>

            <Select>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Platform" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Platforms</SelectItem>
                <SelectItem value="instagram">Instagram</SelectItem>
                <SelectItem value="tiktok">TikTok</SelectItem>
                <SelectItem value="both">Both</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm">
              Clear Filters
            </Button>
          </div>
        </div>

        {/* Influencer Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
          {influencers.map(influencer => (
            <InfluencerCard key={influencer.id} influencer={influencer} />
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button
            size="lg"
            className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600"
          >
            Load More Influencers
          </Button>
        </div>
      </div>
    </div>
  );
}
