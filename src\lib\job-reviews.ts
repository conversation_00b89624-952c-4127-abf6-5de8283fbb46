import { supabase } from './supabase';
import { Database } from './database.types';

type JobReview = Database['public']['Tables']['job_reviews']['Row'];
type JobReviewInsert = Database['public']['Tables']['job_reviews']['Insert'];
type JobReviewUpdate = Database['public']['Tables']['job_reviews']['Update'];

export interface JobReviewWithDetails extends JobReview {
  job_completion?: {
    id: string;
    status: string;
    campaign_application: {
      campaign: {
        title: string;
        business: {
          company_name: string;
        };
      };
    };
  };
  reviewer_profile?: {
    username: string;
    full_name: string;
    avatar_url: string | null;
    user_type: string;
  };
  reviewee_profile?: {
    username: string;
    full_name: string;
    avatar_url: string | null;
    user_type: string;
  };
}

export interface ReviewStats {
  average_rating: number;
  total_reviews: number;
  rating_distribution: {
    [key: number]: number; // rating -> count
  };
}

/**
 * Create a new review
 */
export async function createJobReview(
  jobCompletionId: string,
  revieweeId: string,
  rating: number,
  comment: string,
  reviewType: 'influencer_to_business' | 'business_to_influencer'
) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Validate that the job completion exists and is completed
    const { data: jobCompletion } = await supabase
      .from('job_completions')
      .select('status, influencer_id, business_id')
      .eq('id', jobCompletionId)
      .single();

    if (!jobCompletion) {
      throw new Error('Job completion not found');
    }

    if (jobCompletion.status !== 'completed') {
      throw new Error('Can only review completed jobs');
    }

    // Validate review type and permissions
    if (
      reviewType === 'influencer_to_business' &&
      user.user.id !== jobCompletion.influencer_id
    ) {
      throw new Error('Only influencer can review business');
    }

    if (
      reviewType === 'business_to_influencer' &&
      user.user.id !== jobCompletion.business_id
    ) {
      throw new Error('Only business can review influencer');
    }

    const reviewData: JobReviewInsert = {
      job_completion_id: jobCompletionId,
      reviewer_id: user.user.id,
      reviewee_id: revieweeId,
      rating,
      comment,
      review_type: reviewType,
    };

    const { data, error } = await supabase
      .from('job_reviews')
      .insert(reviewData)
      .select()
      .single();

    if (error) {
      console.error('Error creating review:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error creating review:', error);
    return { data: null, error };
  }
}

/**
 * Get pending reviews for current user
 */
export async function getPendingReviews() {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data: profile } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', user.user.id)
      .single();

    if (!profile) throw new Error('Profile not found');

    // Get completed job completions where user hasn't left a review yet
    const { data: completedJobs, error } = await supabase
      .from('job_completions')
      .select(
        `
        id,
        influencer_id,
        business_id,
        campaign_application:campaign_applications!inner(
          campaign:campaigns!inner(
            title,
            business:businesses!inner(
              company_name
            )
          )
        )
      `
      )
      .eq('status', 'completed')
      .or(
        profile.user_type === 'influencer'
          ? `influencer_id.eq.${user.user.id}`
          : `business_id.eq.${user.user.id}`
      );

    if (error) {
      console.error('Error fetching completed jobs:', error);
      return { data: null, error };
    }

    if (!completedJobs || completedJobs.length === 0) {
      return { data: [], error: null };
    }

    // Filter out jobs where user has already left a review
    const jobIds = completedJobs.map(job => job.id);

    const { data: existingReviews } = await supabase
      .from('job_reviews')
      .select('job_completion_id')
      .in('job_completion_id', jobIds)
      .eq('reviewer_id', user.user.id);

    const reviewedJobIds = new Set(
      existingReviews?.map(r => r.job_completion_id) || []
    );

    const pendingJobs = completedJobs.filter(
      job => !reviewedJobIds.has(job.id)
    );

    return { data: pendingJobs, error: null };
  } catch (error) {
    console.error('Unexpected error fetching pending reviews:', error);
    return { data: null, error };
  }
}

/**
 * Get reviews for a specific profile
 */
export async function getProfileReviews(
  profileId: string,
  limit = 10,
  offset = 0
) {
  try {
    const { data, error } = await supabase
      .from('job_reviews')
      .select(
        `
        *,
        job_completion:job_completions!inner(
          id,
          status,
          campaign_application:campaign_applications!inner(
            campaign:campaigns!inner(
              title,
              business:businesses!inner(
                company_name
              )
            )
          )
        ),
        reviewer_profile:profiles!job_reviews_reviewer_id_fkey(
          username,
          full_name,
          avatar_url,
          user_type
        ),
        reviewee_profile:profiles!job_reviews_reviewee_id_fkey(
          username,
          full_name,
          avatar_url,
          user_type
        )
      `
      )
      .eq('reviewee_id', profileId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching profile reviews:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching profile reviews:', error);
    return { data: null, error };
  }
}

/**
 * Get review statistics for a profile
 */
export async function getProfileReviewStats(
  profileId: string
): Promise<{ data: ReviewStats | null; error: unknown }> {
  try {
    const { data: profile } = await supabase
      .from('profiles')
      .select('average_rating, total_reviews')
      .eq('id', profileId)
      .single();

    if (!profile) {
      return { data: null, error: 'Profile not found' };
    }

    // Get rating distribution
    const { data: reviews } = await supabase
      .from('job_reviews')
      .select('rating')
      .eq('reviewee_id', profileId)
      .not('rating', 'is', null);

    const ratingDistribution: { [key: number]: number } = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0,
    };

    reviews?.forEach(review => {
      if (review.rating) {
        ratingDistribution[review.rating] =
          (ratingDistribution[review.rating] || 0) + 1;
      }
    });

    const stats: ReviewStats = {
      average_rating: Number(profile.average_rating) || 0,
      total_reviews: profile.total_reviews || 0,
      rating_distribution: ratingDistribution,
    };

    return { data: stats, error: null };
  } catch (error) {
    console.error('Unexpected error fetching review stats:', error);
    return { data: null, error };
  }
}

/**
 * Update an existing review (within 24 hours)
 */
export async function updateJobReview(
  reviewId: string,
  rating: number,
  comment: string
) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const updateData: JobReviewUpdate = {
      rating,
      comment,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('job_reviews')
      .update(updateData)
      .eq('id', reviewId)
      .eq('reviewer_id', user.user.id) // Ensure only reviewer can update
      .gte(
        'created_at',
        new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      ) // Within 24 hours
      .select()
      .single();

    if (error) {
      console.error('Error updating review:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error updating review:', error);
    return { data: null, error };
  }
}

/**
 * Check if user can review a specific job completion
 */
export async function canUserReviewJob(jobCompletionId: string): Promise<{
  canReview: boolean;
  reviewType?: string;
  revieweeId?: string;
  error?: unknown;
}> {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      return { canReview: false, error: 'User not authenticated' };
    }

    // Get job completion details
    const { data: jobCompletion } = await supabase
      .from('job_completions')
      .select('status, influencer_id, business_id')
      .eq('id', jobCompletionId)
      .single();

    if (!jobCompletion) {
      return { canReview: false, error: 'Job completion not found' };
    }

    if (jobCompletion.status !== 'completed') {
      return {
        canReview: false,
        error: 'Job must be completed to leave a review',
      };
    }

    // Check if user has already reviewed this job
    const { data: existingReview } = await supabase
      .from('job_reviews')
      .select('id')
      .eq('job_completion_id', jobCompletionId)
      .eq('reviewer_id', user.user.id)
      .single();

    if (existingReview) {
      return { canReview: false, error: 'You have already reviewed this job' };
    }

    // Determine review type and reviewee
    let reviewType: string;
    let revieweeId: string;

    if (user.user.id === jobCompletion.influencer_id) {
      reviewType = 'influencer_to_business';
      revieweeId = jobCompletion.business_id || '';
    } else if (user.user.id === jobCompletion.business_id) {
      reviewType = 'business_to_influencer';
      revieweeId = jobCompletion.influencer_id || '';
    } else {
      return {
        canReview: false,
        error: 'You are not authorized to review this job',
      };
    }

    return { canReview: true, reviewType, revieweeId };
  } catch (error) {
    console.error('Error checking review permissions:', error);
    return { canReview: false, error };
  }
}
