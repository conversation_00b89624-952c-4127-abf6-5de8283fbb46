import { supabase } from './supabase';
import { sendNotificationEmail } from './email-helpers';

// Helper function to get user email and username
async function getUserEmailAndUsername(userId: string) {
  // Use secure RPC function to get user email
  const { data: email, error: emailError } = await supabase.rpc(
    'get_user_email',
    { user_id: userId }
  );

  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('username')
    .eq('id', userId)
    .single();

  if (emailError) {
    console.error('Error fetching user email:', emailError);
  }

  if (profileError) {
    console.error('Error fetching user profile:', profileError);
  }

  return {
    email: email,
    username: profile?.username,
  };
}

export interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message: string;
  data: Record<string, unknown>;
  read: boolean;
  created_at: string;
  updated_at: string;
}

export type NotificationType =
  | 'offer_received'
  | 'offer_accepted'
  | 'offer_rejected'
  | 'campaign_application'
  | 'campaign_accepted'
  | 'campaign_rejected'
  | 'job_completion_submitted'
  | 'job_completion_approved'
  | 'job_completion_rejected'
  | 'message_received'
  | 'payment_received'
  | 'payment_required_offer_accepted'
  | 'payment_required_order_accepted'
  | 'order_received'
  | 'application_accepted_payment_pending'
  | 'payment_completed_work_ready';

// Create a new notification
export async function createNotification(
  userId: string,
  type: NotificationType,
  title: string,
  message: string,
  data: Record<string, unknown> = {}
) {
  const { data: notification, error } = await supabase.rpc(
    'create_notification',
    {
      p_user_id: userId,
      p_type: type,
      p_title: title,
      p_message: message,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      p_data: data as any,
    }
  );

  if (error) {
    console.error('Error creating notification:', error);
    return { data: null, error };
  }

  return { data: notification, error: null };
}

// Get user notifications
export async function getUserNotifications(
  userId?: string,
  limit = 50,
  offset = 0,
  readStatus?: boolean | null
) {
  let query = supabase
    .from('notifications')
    .select('*')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (userId) {
    query = query.eq('user_id', userId);
  }

  if (readStatus !== undefined && readStatus !== null) {
    query = query.eq('read', readStatus);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching notifications:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string) {
  const { data, error } = await supabase
    .from('notifications')
    .update({ read: true })
    .eq('id', notificationId)
    .select()
    .single();

  if (error) {
    console.error('Error marking notification as read:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

// Mark all notifications as read for user
export async function markAllNotificationsAsRead(userId: string) {
  const { data, error } = await supabase
    .from('notifications')
    .update({ read: true })
    .eq('user_id', userId)
    .eq('read', false);

  if (error) {
    console.error('Error marking all notifications as read:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

// Get unread notification count
export async function getUnreadNotificationCount(userId: string) {
  const { count, error } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
    .eq('read', false);

  if (error) {
    console.error('Error getting unread count:', error);
    return { count: 0, error };
  }

  return { count: count || 0, error: null };
}

// Delete notification
export async function deleteNotification(notificationId: string) {
  const { error } = await supabase
    .from('notifications')
    .delete()
    .eq('id', notificationId);

  if (error) {
    console.error('Error deleting notification:', error);
    return { error };
  }

  return { error: null };
}

// Helper functions for specific notification types

export async function notifyOfferReceived(
  influencerId: string,
  businessUsername: string,
  offerTitle: string,
  offerId: string
) {
  const notificationResult = await createNotification(
    influencerId,
    'offer_received',
    'Nova direktna ponuda',
    `"${businessUsername}" vam je poslao direktnu ponudu: "${offerTitle}"`,
    { offer_id: offerId, business_username: businessUsername }
  );

  // Send email notification
  try {
    const { email, username } = await getUserEmailAndUsername(influencerId);
    if (email && username) {
      await sendNotificationEmail(email, username, 'new_offer', {
        Biznis: businessUsername,
        Ponuda: offerTitle,
        'ID Ponude': offerId,
      });
    }
  } catch (error) {
    console.error('Failed to send offer received email:', error);
  }

  return notificationResult;
}

export async function notifyOfferAccepted(
  businessId: string,
  influencerUsername: string,
  offerTitle: string,
  offerId: string
) {
  return createNotification(
    businessId,
    'offer_accepted',
    'Direktna ponuda prihvaćena',
    `"${influencerUsername}" je prihvatio vašu direktnu ponudu: "${offerTitle}"`,
    { offer_id: offerId, influencer_username: influencerUsername }
  );
}

export async function notifyOfferRejected(
  businessId: string,
  influencerUsername: string,
  offerTitle: string,
  offerId: string
) {
  return createNotification(
    businessId,
    'offer_rejected',
    'Direktna ponuda odbijena',
    `"${influencerUsername}" je odbio vašu direktnu ponudu: "${offerTitle}"`,
    { offer_id: offerId, influencer_username: influencerUsername }
  );
}

export async function notifyCampaignApplication(
  businessId: string,
  influencerUsername: string,
  campaignTitle: string,
  applicationId: string
) {
  return createNotification(
    businessId,
    'campaign_application',
    'Nova prijava na kampanju',
    `"${influencerUsername}" se prijavio na vašu kampanju: "${campaignTitle}"`,
    { application_id: applicationId, influencer_username: influencerUsername }
  );
}

export async function notifyCampaignAccepted(
  influencerId: string,
  businessUsername: string,
  campaignTitle: string,
  applicationId: string
) {
  return createNotification(
    influencerId,
    'campaign_accepted',
    'Prijava prihvaćena',
    `"${businessUsername}" je prihvatio vašu prijavu za kampanju: "${campaignTitle}"`,
    { application_id: applicationId, business_username: businessUsername }
  );
}

export async function notifyCampaignRejected(
  influencerId: string,
  businessUsername: string,
  campaignTitle: string,
  applicationId: string
) {
  return createNotification(
    influencerId,
    'campaign_rejected',
    'Prijava odbijena',
    `"${businessUsername}" je odbio vašu prijavu za kampanju: "${campaignTitle}"`,
    { application_id: applicationId, business_username: businessUsername }
  );
}

export async function notifyMessageReceived(
  userId: string,
  senderUsername: string,
  conversationId: string
) {
  return createNotification(
    userId,
    'message_received',
    'Nova poruka',
    `"${senderUsername}" vam je poslao novu poruku`,
    { conversation_id: conversationId, sender_username: senderUsername }
  );
}

export async function notifyPaymentReceived(
  influencerId: string,
  amount: number,
  currency: string,
  projectTitle: string,
  businessUsername: string
) {
  return createNotification(
    influencerId,
    'payment_received',
    'Plaćanje primljeno',
    `"${businessUsername}" je uplatio ${amount} ${currency} za projekt: "${projectTitle}"`,
    {
      amount,
      currency,
      project_title: projectTitle,
      business_username: businessUsername,
    }
  );
}

// NEW NOTIFICATION FUNCTIONS

export async function notifyPaymentRequiredOfferAccepted(
  businessId: string,
  influencerUsername: string,
  offerTitle: string,
  offerId: string,
  amount: number,
  currency: string
) {
  const notificationResult = await createNotification(
    businessId,
    'payment_required_offer_accepted',
    'Izvršite plaćanje',
    `"${influencerUsername}" je prihvatio vašu direktnu ponudu "${offerTitle}". Izvršite plaćanje od ${amount} ${currency} da biste pokrenuli posao.`,
    {
      offer_id: offerId,
      influencer_username: influencerUsername,
      amount,
      currency,
    }
  );

  // Send email notification
  try {
    const { email, username } = await getUserEmailAndUsername(businessId);
    if (email && username) {
      await sendNotificationEmail(email, username, 'payment_required_offer', {
        Influencer: influencerUsername,
        Ponuda: offerTitle,
        Iznos: `${amount} ${currency}`,
        'ID Ponude': offerId,
      });
    }
  } catch (error) {
    console.error('Failed to send payment required offer email:', error);
  }

  return notificationResult;
}

export async function notifyPaymentRequiredOrderAccepted(
  businessId: string,
  influencerUsername: string,
  packageName: string,
  orderId: string,
  amount: number,
  currency: string
) {
  const notificationResult = await createNotification(
    businessId,
    'payment_required_order_accepted',
    'Izvršite plaćanje',
    `"${influencerUsername}" je prihvatio vašu narudžbu "${packageName}". Izvršite plaćanje od ${amount} ${currency} da biste pokrenuli posao.`,
    {
      order_id: orderId,
      influencer_username: influencerUsername,
      amount,
      currency,
      package_name: packageName,
    }
  );

  // Send email notification
  try {
    const { email, username } = await getUserEmailAndUsername(businessId);
    if (email && username) {
      await sendNotificationEmail(email, username, 'payment_required_order', {
        Influencer: influencerUsername,
        Paket: packageName,
        Iznos: `${amount} ${currency}`,
        'ID Narudžbe': orderId,
      });
    }
  } catch (error) {
    console.error('Failed to send payment required order email:', error);
  }

  return notificationResult;
}

export async function notifyOrderReceived(
  influencerId: string,
  businessUsername: string,
  packageName: string,
  orderId: string,
  amount: number,
  currency: string
) {
  const notificationResult = await createNotification(
    influencerId,
    'order_received',
    'Nova narudžba paketa',
    `"${businessUsername}" je naručio vaš paket "${packageName}" za ${amount} ${currency}`,
    {
      order_id: orderId,
      business_username: businessUsername,
      amount,
      currency,
      package_name: packageName,
    }
  );

  // Send email notification
  try {
    const { email, username } = await getUserEmailAndUsername(influencerId);
    if (email && username) {
      await sendNotificationEmail(email, username, 'new_order', {
        Biznis: businessUsername,
        Paket: packageName,
        Iznos: `${amount} ${currency}`,
        'ID Narudžbe': orderId,
      });
    }
  } catch (error) {
    console.error('Failed to send order received email:', error);
  }

  return notificationResult;
}

export async function notifyApplicationAcceptedPaymentPending(
  influencerId: string,
  businessUsername: string,
  campaignTitle: string,
  applicationId: string
) {
  const notificationResult = await createNotification(
    influencerId,
    'application_accepted_payment_pending',
    'Prijava prihvaćena - čeka se plaćanje',
    `"${businessUsername}" je prihvatio vašu prijavu za kampanju "${campaignTitle}". Čeka se da biznis izvrši plaćanje.`,
    {
      application_id: applicationId,
      business_username: businessUsername,
      campaign_title: campaignTitle,
    }
  );

  // Send email notification
  try {
    const { email, username } = await getUserEmailAndUsername(influencerId);
    if (email && username) {
      await sendNotificationEmail(email, username, 'application_approved', {
        Biznis: businessUsername,
        Kampanja: campaignTitle,
        'ID Aplikacije': applicationId,
        Status: 'Čeka se plaćanje',
      });
    }
  } catch (error) {
    console.error('Failed to send application accepted email:', error);
  }

  return notificationResult;
}

export async function notifyPaymentCompletedWorkReady(
  influencerId: string,
  businessUsername: string,
  projectTitle: string,
  projectType: 'campaign' | 'direct_offer' | 'package_order',
  projectId: string,
  amount: number,
  currency: string
) {
  const notificationResult = await createNotification(
    influencerId,
    'payment_completed_work_ready',
    'Možete početi s radom',
    `"${businessUsername}" je platio ${amount} ${currency} za "${projectTitle}". Možete početi s radom!`,
    {
      project_id: projectId,
      project_type: projectType,
      business_username: businessUsername,
      project_title: projectTitle,
      amount,
      currency,
    }
  );

  // Send email notification
  try {
    const { email, username } = await getUserEmailAndUsername(influencerId);
    if (email && username) {
      await sendNotificationEmail(email, username, 'payment_completed', {
        Biznis: businessUsername,
        Projekat: projectTitle,
        Tip:
          projectType === 'campaign'
            ? 'Kampanja'
            : projectType === 'direct_offer'
              ? 'Direktna ponuda'
              : 'Paket narudžba',
        Iznos: `${amount} ${currency}`,
        'ID Projekta': projectId,
      });
    }
  } catch (error) {
    console.error('Failed to send payment completed email:', error);
  }

  return notificationResult;
}

export async function notifyJobCompletionSubmitted(
  businessId: string,
  influencerUsername: string,
  projectTitle: string,
  completionId: string,
  projectType: 'campaign' | 'direct_offer' | 'package_order'
) {
  const notificationResult = await createNotification(
    businessId,
    'job_completion_submitted',
    'Rad završen - pregled potreban',
    `"${influencerUsername}" je poslao završeni rad za "${projectTitle}". Molimo pregledajte i odobrite.`,
    {
      completion_id: completionId,
      influencer_username: influencerUsername,
      project_title: projectTitle,
      project_type: projectType,
    }
  );

  // Send email notification
  try {
    const { email, username } = await getUserEmailAndUsername(businessId);
    if (email && username) {
      await sendNotificationEmail(email, username, 'work_submitted', {
        Influencer: influencerUsername,
        Projekat: projectTitle,
        Tip:
          projectType === 'campaign'
            ? 'Kampanja'
            : projectType === 'direct_offer'
              ? 'Direktna ponuda'
              : 'Paket narudžba',
        'ID Završetka': completionId,
      });
    }
  } catch (error) {
    console.error('Failed to send job completion submitted email:', error);
  }

  return notificationResult;
}

export async function notifyJobCompletionApproved(
  influencerId: string,
  businessUsername: string,
  projectTitle: string,
  completionId: string
) {
  const notificationResult = await createNotification(
    influencerId,
    'job_completion_approved',
    'Rad odobren - čestitamo!',
    `"${businessUsername}" je odobrio vaš rad za "${projectTitle}". Odličan posao!`,
    {
      completion_id: completionId,
      business_username: businessUsername,
      project_title: projectTitle,
    }
  );

  // Send email notification
  try {
    const { email, username } = await getUserEmailAndUsername(influencerId);
    if (email && username) {
      await sendNotificationEmail(email, username, 'work_approved', {
        Biznis: businessUsername,
        Projekat: projectTitle,
        'ID Završetka': completionId,
        Status: 'Odobren - Odličan posao!',
      });
    }
  } catch (error) {
    console.error('Failed to send job completion approved email:', error);
  }

  return notificationResult;
}

export async function notifyJobCompletionRejected(
  influencerId: string,
  businessUsername: string,
  projectTitle: string,
  completionId: string,
  rejectionReason?: string
) {
  return createNotification(
    influencerId,
    'job_completion_rejected',
    'Rad treba doraditi',
    `"${businessUsername}" je zatražio doradu rada za "${projectTitle}". ${rejectionReason ? `Razlog: ${rejectionReason}` : ''}`,
    {
      completion_id: completionId,
      business_username: businessUsername,
      project_title: projectTitle,
      rejection_reason: rejectionReason,
    }
  );
}
