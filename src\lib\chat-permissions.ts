import { supabase } from '@/lib/supabase';

export interface ChatPermission {
  id: string;
  business_id: string;
  influencer_id: string;
  offer_id: string | null;
  campaign_application_id: string | null;
  business_approved: boolean | null;
  influencer_approved: boolean | null;
  chat_enabled: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

/**
 * Create or update chat permission for direct offer
 */
export async function upsertOfferChatPermission(
  businessId: string,
  influencerId: string,
  offerId: string,
  businessApproved: boolean = false,
  influencerApproved: boolean = false
) {
  try {
    // First try to find existing permission
    const { data: existing } = await supabase
      .from('chat_permissions')
      .select('id')
      .eq('business_id', businessId)
      .eq('influencer_id', influencerId)
      .eq('offer_id', offerId)
      .is('campaign_application_id', null)
      .maybeSingle();

    const permissionData = {
      business_id: businessId,
      influencer_id: influencerId,
      offer_id: offerId,
      campaign_application_id: null,
      business_approved: businessApproved,
      influencer_approved: influencerApproved,
      // chat_enabled is auto-generated by database, don't set it manually
    };

    let result;
    if (existing) {
      // Update existing permission
      result = await supabase
        .from('chat_permissions')
        .update(permissionData)
        .eq('id', existing.id)
        .select('id')
        .single();
    } else {
      // Create new permission
      result = await supabase
        .from('chat_permissions')
        .insert(permissionData)
        .select('id')
        .single();
    }

    if (result.error) {
      throw result.error;
    }

    return result.data?.id;
  } catch (error) {
    console.error('Error upserting offer chat permission:', error);
    throw error;
  }
}

/**
 * Create or update chat permission for campaign application
 */
export async function upsertApplicationChatPermission(
  businessId: string,
  influencerId: string,
  applicationId: string,
  businessApproved: boolean = false,
  influencerApproved: boolean = false
) {
  try {
    // First try to find existing permission
    const { data: existing } = await supabase
      .from('chat_permissions')
      .select('id')
      .eq('business_id', businessId)
      .eq('influencer_id', influencerId)
      .eq('campaign_application_id', applicationId)
      .is('offer_id', null)
      .maybeSingle();

    const permissionData = {
      business_id: businessId,
      influencer_id: influencerId,
      campaign_application_id: applicationId,
      offer_id: null,
      business_approved: businessApproved,
      influencer_approved: influencerApproved,
      // chat_enabled is auto-generated by database, don't set it manually
    };

    let result;
    if (existing) {
      // Update existing permission
      result = await supabase
        .from('chat_permissions')
        .update(permissionData)
        .eq('id', existing.id)
        .select('id')
        .single();
    } else {
      // Create new application permission
      result = await supabase
        .from('chat_permissions')
        .insert(permissionData)
        .select('id')
        .single();
    }

    if (result.error) {
      throw result.error;
    }

    return result.data?.id;
  } catch (error) {
    console.error('Error upserting application chat permission:', error);
    throw error;
  }
}

/**
 * Check if chat is enabled between business and influencer for specific offer/application
 */
export async function isChatEnabled(
  businessId: string,
  influencerId: string,
  offerId?: string,
  applicationId?: string
): Promise<boolean> {
  let query = supabase
    .from('chat_permissions')
    .select('chat_enabled')
    .eq('business_id', businessId)
    .eq('influencer_id', influencerId);

  if (offerId) {
    query = query.eq('offer_id', offerId);
  } else if (applicationId) {
    query = query.eq('campaign_application_id', applicationId);
  } else {
    throw new Error('Either offerId or applicationId must be provided');
  }

  const { data, error } = await query.single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No permission record found, chat not enabled
      return false;
    }
    console.error('Error checking chat permission:', error);
    throw error;
  }

  return data?.chat_enabled || false;
}

/**
 * Get chat permission details
 */
export async function getChatPermission(
  businessId: string,
  influencerId: string,
  offerId?: string,
  applicationId?: string
): Promise<ChatPermission | null> {
  let query = supabase
    .from('chat_permissions')
    .select('*')
    .eq('business_id', businessId)
    .eq('influencer_id', influencerId);

  if (offerId) {
    query = query.eq('offer_id', offerId);
  } else if (applicationId) {
    query = query.eq('campaign_application_id', applicationId);
  } else {
    throw new Error('Either offerId or applicationId must be provided');
  }

  const { data, error } = await query.single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No permission record found
      return null;
    }
    console.error('Error getting chat permission:', error);
    throw error;
  }

  return data;
}

/**
 * Approve chat from business side
 */
export async function approveBusinessChat(
  businessId: string,
  influencerId: string,
  offerId?: string,
  applicationId?: string
) {
  let query = supabase
    .from('chat_permissions')
    .update({ business_approved: true })
    .eq('business_id', businessId)
    .eq('influencer_id', influencerId);

  if (offerId) {
    query = query.eq('offer_id', offerId);
  } else if (applicationId) {
    query = query.eq('campaign_application_id', applicationId);
  } else {
    throw new Error('Either offerId or applicationId must be provided');
  }

  const { error } = await query;

  if (error) {
    console.error('Error approving business chat:', error);
    throw error;
  }
}

/**
 * Approve chat from influencer side
 */
export async function approveInfluencerChat(
  businessId: string,
  influencerId: string,
  offerId?: string,
  applicationId?: string
) {
  let query = supabase
    .from('chat_permissions')
    .update({ influencer_approved: true })
    .eq('business_id', businessId)
    .eq('influencer_id', influencerId);

  if (offerId) {
    query = query.eq('offer_id', offerId);
  } else if (applicationId) {
    query = query.eq('campaign_application_id', applicationId);
  } else {
    throw new Error('Either offerId or applicationId must be provided');
  }

  const { error } = await query;

  if (error) {
    console.error('Error approving influencer chat:', error);
    throw error;
  }
}

/**
 * Get all chat permissions for a user (business or influencer)
 */
export async function getUserChatPermissions(
  userId: string
): Promise<ChatPermission[]> {
  const { data, error } = await supabase
    .from('chat_permissions')
    .select('*')
    .or(`business_id.eq.${userId},influencer_id.eq.${userId}`)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error getting user chat permissions:', error);
    throw error;
  }

  return data || [];
}
