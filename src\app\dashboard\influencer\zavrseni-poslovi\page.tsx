'use client';

import { useEffect, useState } from 'react';
import { FileText, Loader2 } from 'lucide-react';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import { JobCompletionCard } from '@/components/job-completion/JobCompletionCard';
import {
  getUserJobCompletions,
  JobCompletionWithDetails,
} from '@/lib/job-completions';
import { getCurrentUser } from '@/lib/profiles';
import { toast } from 'sonner';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';

export default function InfluencerZavrseniPosloviPage() {
  const [jobCompletions, setJobCompletions] = useState<
    JobCompletionWithDetails[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userType, setUserType] = useState<'influencer' | 'business'>(
    'influencer'
  );
  const [activeTab, setActiveTab] = useState('all');

  const loadJobCompletions = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await getUserJobCompletions();

      if (error) {
        toast.error('Neuspješno učitavanje završenih poslova');
        return;
      }

      setJobCompletions(data || []);
    } catch {
      toast.error('Dogodila se neočekivana greška');
    } finally {
      setIsLoading(false);
    }
  };

  const loadUserProfile = async () => {
    try {
      const { data: user, error } = await getCurrentUser();
      if (error || !user) {
        toast.error('Neuspješno učitavanje korisničkog profila');
        return;
      }
      setUserType(user.user_type);
    } catch {
      toast.error('Neuspješno učitavanje korisničkog profila');
    }
  };

  useEffect(() => {
    loadUserProfile();
    loadJobCompletions();
  }, []);

  const handleJobCompletionUpdate = () => {
    loadJobCompletions();
  };

  // Filter job completions by status
  const filterByStatus = (status?: string) => {
    if (!status || status === 'all') return jobCompletions;
    return jobCompletions.filter(jc => jc.status === status);
  };

  const getStatusCounts = () => {
    return {
      all: jobCompletions.length,
      pending: jobCompletions.filter(jc => jc.status === 'pending').length,
      submitted: jobCompletions.filter(jc => jc.status === 'submitted').length,
      approved: jobCompletions.filter(jc => jc.status === 'approved').length,
      rejected: jobCompletions.filter(jc => jc.status === 'rejected').length,
      completed: jobCompletions.filter(jc => jc.status === 'completed').length,
    };
  };

  const statusCounts = getStatusCounts();
  const filteredJobCompletions = filterByStatus(activeTab);

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="min-h-screen bg-gradient-to-br from-purple-50/30 via-pink-50/20 to-purple-100/40 dark:from-purple-950/10 dark:via-pink-950/5 dark:to-purple-900/20">
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Završeni poslovi</h1>
            <p className="text-muted-foreground mt-1">
              Pregled vaših završenih poslova
            </p>
          </div>

          {/* Gradient Tabs */}
          <GradientTabs
            tabs={[
              { name: 'Svi', value: 'all', count: statusCounts.all },
              {
                name: 'Predati',
                value: 'submitted',
                count: statusCounts.submitted,
              },
              {
                name: 'Odobreni',
                value: 'approved',
                count: statusCounts.approved,
              },
              {
                name: 'Odbačeni',
                value: 'rejected',
                count: statusCounts.rejected,
              },
            ]}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            className="mb-6"
          />

          {/* Job Completions List */}
          <div className="space-y-4">
            {filteredJobCompletions.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {activeTab === 'all'
                    ? 'Nema poslova'
                    : `Nema poslova sa statusom "${activeTab}"`}
                </h3>
                <p className="text-gray-600">
                  {activeTab === 'all'
                    ? 'Još uvijek nemate završenih poslova.'
                    : 'Nema poslova sa ovim statusom.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredJobCompletions.map(jobCompletion => (
                  <JobCompletionCard
                    key={jobCompletion.id}
                    jobCompletion={jobCompletion}
                    userType={userType}
                    onUpdate={handleJobCompletionUpdate}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
