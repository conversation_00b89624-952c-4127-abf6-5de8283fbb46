import React from 'react';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';

interface GradientCardProps extends React.ComponentProps<'div'> {
  variant?: 'primary' | 'secondary' | 'story' | 'subtle' | 'warm';
  glassEffect?: boolean;
}

const GradientCard = React.forwardRef<HTMLDivElement, GradientCardProps>(
  ({ className, variant = 'primary', glassEffect = true, ...props }, ref) => {
    const gradientClasses = {
      primary: 'bg-instagram-primary',
      secondary: 'bg-instagram-secondary',
      story: 'bg-instagram-story',
      subtle: 'bg-instagram-subtle',
      warm: 'bg-instagram-warm',
    };

    return (
      <Card
        ref={ref}
        className={cn(
          gradientClasses[variant],
          glassEffect && 'glass-instagram border-white/20 shadow-2xl',
          className
        )}
        {...props}
      />
    );
  }
);

GradientCard.displayName = 'GradientCard';

export { GradientCard };
