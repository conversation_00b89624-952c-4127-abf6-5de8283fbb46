import { NextResponse } from 'next/server';
import {
  sendEmail,
  generatePasswordResetTemplate,
  generateEmailVerificationTemplate,
} from '@/lib/email-service';
import { withWebhookMiddleware, ApiContext } from '@/lib/api-middleware';

// Define validation schema for the request body
const emailHookSchema = {
  type: 'object',
  required: ['user', 'email_data', 'email_action_type'],
  properties: {
    user: {
      type: 'object',
      required: ['email'],
      properties: {
        email: {
          type: 'string',
          format: 'email',
        },
      },
    },
    email_data: {
      type: 'object',
      required: ['token_url'],
      properties: {
        token_url: {
          type: 'string',
          minLength: 1,
        },
      },
    },
    email_action_type: {
      type: 'string',
      pattern: '^(recovery|signup|email_change)$',
    },
  },
};

async function handleEmailHook(context: ApiContext) {
  try {
    const body = await context.request.json();
    const { user, email_data, email_action_type } = body;

    let htmlContent: string;
    let textContent: string;
    let subject: string;

    // Generate appropriate email based on action type
    switch (email_action_type) {
      case 'recovery':
        const resetResult = generatePasswordResetTemplate(
          email_data.token_url,
          user.email
        );
        htmlContent = resetResult.htmlContent;
        textContent = resetResult.textContent;
        subject = 'Resetovanje lozinke - INFLUEXUS';
        break;

      case 'signup':
      case 'email_change':
        const verificationResult = generateEmailVerificationTemplate(
          email_data.token_url,
          user.email
        );
        htmlContent = verificationResult.htmlContent;
        textContent = verificationResult.textContent;
        subject = 'Potvrdite svoj email - INFLUEXUS';
        break;

      default:
        return NextResponse.json(
          {
            error: `Unsupported email action type: ${email_action_type}`,
            supportedTypes: ['recovery', 'signup', 'email_change'],
          },
          { status: 400 }
        );
    }

    const result = await sendEmail({
      to: user.email,
      subject,
      htmlContent,
      textContent,
    });

    if (result.success) {
      return NextResponse.json({
        message: `${email_action_type} email sent successfully`,
        timestamp: new Date().toISOString(),
      });
    } else {
      console.error(`Failed to send ${email_action_type} email:`, result.error);
      return NextResponse.json(
        {
          error: 'Failed to send email',
          details:
            process.env.NODE_ENV === 'development' ? result.error : undefined,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Auth hook error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Export the handler with comprehensive middleware
export const POST = withWebhookMiddleware(
  handleEmailHook,
  process.env.SUPABASE_WEBHOOK_SECRET ||
    process.env.SUPABASE_SERVICE_ROLE_KEY ||
    '',
  {
    validation: {
      enabled: true,
      bodySchema: emailHookSchema,
    },
    rateLimit: {
      enabled: true,
      strict: true,
      customLimit: { maxRequests: 30, windowMs: 60 * 1000 }, // 30 requests per minute for email hooks
    },
  }
);
