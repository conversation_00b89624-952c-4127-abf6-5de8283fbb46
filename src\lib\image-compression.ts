import imageCompression from 'browser-image-compression';

// Single high-quality image configuration for all use cases
export const IMAGE_CONFIG = {
  width: 1080,
  height: 1080,
  quality: 1.0,
} as const;

// Compression options
const DEFAULT_COMPRESSION_OPTIONS = {
  maxSizeMB: 1, // Maximum file size in MB
  useWebWorker: true, // Use web worker for better performance
  fileType: 'image/webp' as const, // Convert to WebP format
  initialQuality: 0.9,
};

/**
 * Compress and resize image to single optimal size
 */
export async function compressImageToOptimalSize(file: File): Promise<File> {
  const config = IMAGE_CONFIG;

  const options = {
    ...DEFAULT_COMPRESSION_OPTIONS,
    maxWidthOrHeight: Math.max(config.width, config.height),
    initialQuality: config.quality,
  };

  try {
    const compressedFile = await imageCompression(file, options);

    // Create a new file with proper naming
    const fileName = `avatar-${Date.now()}.webp`;
    return new File([compressedFile], fileName, {
      type: 'image/webp',
      lastModified: Date.now(),
    });
  } catch (error) {
    console.error('Error compressing image:', error);
    throw new Error('Failed to compress image');
  }
}

/**
 * Legacy function - kept for backward compatibility
 * @deprecated Use compressImageToOptimalSize instead
 */
export async function compressImageToSize(file: File): Promise<File> {
  return compressImageToOptimalSize(file);
}

/**
 * Validate image file before processing
 */
export function validateImageFile(file: File): {
  isValid: boolean;
  error?: string;
} {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Dozvoljen je samo JPEG, PNG ili WebP format.',
    };
  }

  // Check file size (max 10MB)
  const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSizeInBytes) {
    return {
      isValid: false,
      error: 'Slika ne smije biti veća od 10MB.',
    };
  }

  return { isValid: true };
}

/**
 * Create image preview URL
 */
export function createImagePreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Clean up image preview URL
 */
export function cleanupImagePreview(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * Get file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Calculate compression ratio
 */
export function calculateCompressionRatio(
  originalSize: number,
  compressedSize: number
): number {
  return Math.round(((originalSize - compressedSize) / originalSize) * 100);
}

/**
 * Progress callback type for compression
 */
export type CompressionProgressCallback = (progress: {
  stage: 'validating' | 'compressing' | 'uploading' | 'complete';
  percentage: number;
}) => void;

/**
 * Compress image with progress tracking (single optimized version)
 */
export async function compressImageWithProgress(
  file: File,
  onProgress?: CompressionProgressCallback
): Promise<{
  compressedFile: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
}> {
  const originalSize = file.size;

  // Validation stage
  onProgress?.({ stage: 'validating', percentage: 0 });

  const validation = validateImageFile(file);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  // Compression stage
  onProgress?.({ stage: 'compressing', percentage: 20 });

  const compressedFile = await compressImageToOptimalSize(file);

  onProgress?.({ stage: 'compressing', percentage: 80 });

  const compressionRatio = calculateCompressionRatio(
    originalSize,
    compressedFile.size
  );

  onProgress?.({ stage: 'complete', percentage: 100 });

  return {
    compressedFile,
    originalSize,
    compressedSize: compressedFile.size,
    compressionRatio,
  };
}
