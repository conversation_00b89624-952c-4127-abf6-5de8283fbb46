'use client';

import { useState, useEffect } from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';

interface Category {
  id: number;
  name: string;
  icon: string;
  description: string;
}

interface CategoryGridSelectorProps {
  selectedCategories: number[];
  onCategoriesChange: (categories: number[]) => void;
  maxCategories?: number;
  className?: string;
  disabled?: boolean;
}

export function CategoryGridSelector({
  selectedCategories,
  onCategoriesChange,
  maxCategories = 3,
  className,
  disabled = false,
}: CategoryGridSelectorProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error loading categories:', error);
        return;
      }

      setCategories((data as any) || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryToggle = (categoryId: number) => {
    if (disabled) return;

    if (selectedCategories.includes(categoryId)) {
      // Remove category
      onCategoriesChange(selectedCategories.filter(id => id !== categoryId));
    } else {
      // Add category if under limit
      if (selectedCategories.length < maxCategories) {
        onCategoriesChange([...selectedCategories, categoryId]);
      }
    }
  };

  const getSelectedCategoryNames = () => {
    return categories
      .filter(cat => selectedCategories.includes(cat.id))
      .map(cat => cat.name);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className="h-20 bg-gray-200 rounded-lg animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Selected categories display */}
      {selectedCategories.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm font-medium text-gray-700">
            Izabrane kategorije ({selectedCategories.length}/{maxCategories}):
          </p>
          <div className="flex flex-wrap gap-2">
            {getSelectedCategoryNames().map(name => (
              <Badge key={name} variant="secondary">
                {name}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Categories grid */}
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Kakav sadržaj kreirate/postavljate? Izaberite do {maxCategories}{' '}
          kategorije koje najbolje opisuju vaš sadržaj:
        </p>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {categories.map(category => {
            const isSelected = selectedCategories.includes(category.id);
            const isDisabled =
              disabled ||
              (!isSelected && selectedCategories.length >= maxCategories);

            return (
              <Button
                key={category.id}
                variant="outline"
                className={cn(
                  'h-auto p-4 flex flex-col items-center space-y-2 relative transition-all duration-200',
                  isSelected && 'border-blue-500 bg-blue-50 text-blue-700',
                  isDisabled && 'opacity-50 cursor-not-allowed',
                  !isDisabled &&
                    !isSelected &&
                    'hover:border-gray-400 hover:bg-gray-50'
                )}
                onClick={() => handleCategoryToggle(category.id)}
                disabled={isDisabled}
              >
                {/* Selection indicator */}
                {isSelected && (
                  <div className="absolute top-2 right-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                    <Check className="h-3 w-3 text-white" />
                  </div>
                )}

                {/* Category icon */}
                <div className="text-2xl">{category.icon}</div>

                {/* Category name */}
                <div className="text-sm font-medium text-center leading-tight">
                  {category.name}
                </div>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Help text */}
      <p className="text-xs text-gray-500">
        Možete izabrati maksimalno {maxCategories} kategorije. Ovo će pomoći
        biznisima da vas lakše pronađu.
      </p>
    </div>
  );
}
