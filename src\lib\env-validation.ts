/**
 * Environment Variables Validation
 * Validates required environment variables on application startup
 * Provides type-safe access to environment variables
 */

export interface EnvConfig {
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY: string;

  // MCP Configuration
  SUPABASE_ACCESS_TOKEN: string;

  // Webhook Secrets
  SUPABASE_WEBHOOK_SECRET: string;

  // Stripe Configuration
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;

  // App Configuration
  NEXT_PUBLIC_APP_URL: string;
  NODE_ENV: 'development' | 'production' | 'staging';

  // Optional Configuration
  EMAIL_SERVICE_API_KEY?: string;
  EMAIL_FROM_ADDRESS?: string;
  EMAIL_FROM_NAME?: string;
  NEXT_PUBLIC_GA_TRACKING_ID?: string;
  SENTRY_DSN?: string;
  NEXT_PUBLIC_LOGROCKET_APP_ID?: string;
  JWT_SECRET?: string;
  SESSION_SECRET?: string;
  CORS_ALLOWED_ORIGINS?: string;
  REDIS_URL?: string;
  REDIS_PASSWORD?: string;
  DEBUG?: string;
  DISABLE_EMAILS?: string;
}

interface ValidationRule {
  required: boolean;
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'jwt';
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  description?: string;
  allowedValues?: string[];
}

const ENV_VALIDATION_RULES: Record<keyof EnvConfig, ValidationRule> = {
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: {
    required: true,
    type: 'url',
    pattern: /^https:\/\/[a-z0-9]+\.supabase\.co$/,
    description: 'Supabase project URL',
  },
  NEXT_PUBLIC_SUPABASE_ANON_KEY: {
    required: true,
    type: 'jwt',
    description: 'Supabase anonymous key (JWT format)',
  },
  SUPABASE_SERVICE_ROLE_KEY: {
    required: true,
    type: 'jwt',
    description: 'Supabase service role key (JWT format) - KEEP SECRET!',
  },

  // MCP Configuration
  SUPABASE_ACCESS_TOKEN: {
    required: true,
    type: 'string',
    pattern: /^sbp_[a-f0-9]+$/,
    minLength: 20,
    description: 'Supabase access token for CLI operations',
  },

  // Webhook Secrets
  SUPABASE_WEBHOOK_SECRET: {
    required: true,
    type: 'string',
    minLength: 32,
    description: 'Secure random string for webhook verification',
  },

  // Stripe Configuration
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: {
    required: true,
    type: 'string',
    pattern: /^pk_(test|live)_[a-zA-Z0-9]+$/,
    description: 'Stripe publishable key',
  },
  STRIPE_SECRET_KEY: {
    required: true,
    type: 'string',
    pattern: /^sk_(test|live)_[a-zA-Z0-9]+$/,
    description: 'Stripe secret key - KEEP SECRET!',
  },
  STRIPE_WEBHOOK_SECRET: {
    required: true,
    type: 'string',
    pattern: /^whsec_[a-zA-Z0-9]+$/,
    description: 'Stripe webhook endpoint secret',
  },

  // App Configuration
  NEXT_PUBLIC_APP_URL: {
    required: true,
    type: 'url',
    description: 'Base URL for your application',
  },
  NODE_ENV: {
    required: true,
    type: 'string',
    allowedValues: ['development', 'production', 'staging'],
    description: 'Application environment',
  },

  // Optional Configuration
  EMAIL_SERVICE_API_KEY: {
    required: false,
    type: 'string',
    minLength: 10,
    description: 'Email service API key',
  },
  EMAIL_FROM_ADDRESS: {
    required: false,
    type: 'email',
    description: 'From email address for notifications',
  },
  EMAIL_FROM_NAME: {
    required: false,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    description: 'From name for email notifications',
  },
  NEXT_PUBLIC_GA_TRACKING_ID: {
    required: false,
    type: 'string',
    pattern: /^G-[A-Z0-9]+$/,
    description: 'Google Analytics measurement ID',
  },
  SENTRY_DSN: {
    required: false,
    type: 'url',
    description: 'Sentry DSN for error tracking',
  },
  NEXT_PUBLIC_LOGROCKET_APP_ID: {
    required: false,
    type: 'string',
    description: 'LogRocket app ID for session replay',
  },
  JWT_SECRET: {
    required: false,
    type: 'string',
    minLength: 32,
    description: 'JWT signing secret',
  },
  SESSION_SECRET: {
    required: false,
    type: 'string',
    minLength: 32,
    description: 'Session signing secret',
  },
  CORS_ALLOWED_ORIGINS: {
    required: false,
    type: 'string',
    description: 'Comma-separated list of allowed CORS origins',
  },
  REDIS_URL: {
    required: false,
    type: 'string',
    pattern: /^redis:\/\/.*$/,
    description: 'Redis connection URL',
  },
  REDIS_PASSWORD: {
    required: false,
    type: 'string',
    description: 'Redis password',
  },
  DEBUG: {
    required: false,
    type: 'string',
    allowedValues: ['true', 'false'],
    description: 'Enable debug logging',
  },
  DISABLE_EMAILS: {
    required: false,
    type: 'string',
    allowedValues: ['true', 'false'],
    description: 'Disable email sending in development',
  },
};

class EnvironmentValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'EnvironmentValidationError';
  }
}

/**
 * Validates a single environment variable
 */
function validateEnvVar(
  key: string,
  value: string | undefined,
  rule: ValidationRule
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check if required variable is missing
  if (rule.required && (!value || value.trim() === '')) {
    errors.push(
      `${key} is required but not provided. ${rule.description || ''}`
    );
    return { valid: false, errors };
  }

  // Skip further validation if optional and not provided
  if (!rule.required && (!value || value.trim() === '')) {
    return { valid: true, errors: [] };
  }

  const trimmedValue = value?.trim() || '';

  // Type validation
  switch (rule.type) {
    case 'url':
      try {
        new URL(trimmedValue);
      } catch {
        errors.push(`${key} must be a valid URL`);
      }
      break;

    case 'email':
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmedValue)) {
        errors.push(`${key} must be a valid email address`);
      }
      break;

    case 'jwt':
      if (
        !/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/.test(trimmedValue)
      ) {
        errors.push(`${key} must be a valid JWT token`);
      }
      break;

    case 'number':
      if (isNaN(Number(trimmedValue))) {
        errors.push(`${key} must be a valid number`);
      }
      break;

    case 'boolean':
      if (!['true', 'false', '1', '0'].includes(trimmedValue.toLowerCase())) {
        errors.push(`${key} must be a boolean value (true/false)`);
      }
      break;
  }

  // Length validation
  if (rule.minLength && trimmedValue.length < rule.minLength) {
    errors.push(`${key} must be at least ${rule.minLength} characters long`);
  }

  if (rule.maxLength && trimmedValue.length > rule.maxLength) {
    errors.push(`${key} must be at most ${rule.maxLength} characters long`);
  }

  // Pattern validation
  if (rule.pattern && !rule.pattern.test(trimmedValue)) {
    errors.push(
      `${key} does not match required format. ${rule.description || ''}`
    );
  }

  // Allowed values validation
  if (rule.allowedValues && !rule.allowedValues.includes(trimmedValue)) {
    errors.push(`${key} must be one of: ${rule.allowedValues.join(', ')}`);
  }

  return { valid: errors.length === 0, errors };
}

/**
 * Validates all environment variables according to the rules
 */
export function validateEnvironmentVariables(): {
  valid: boolean;
  config: EnvConfig;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const config: Partial<EnvConfig> = {};

  // Validate each environment variable
  for (const [key, rule] of Object.entries(ENV_VALIDATION_RULES)) {
    const envKey = key as keyof EnvConfig;
    const value = process.env[envKey];

    const validation = validateEnvVar(key, value, rule);

    if (!validation.valid) {
      errors.push(...validation.errors);
    } else if (value) {
      // Type conversion for config
      switch (rule.type) {
        case 'number':
          (config as Record<string, unknown>)[envKey] = Number(value);
          break;
        case 'boolean':
          (config as Record<string, unknown>)[envKey] = ['true', '1'].includes(
            value.toLowerCase()
          );
          break;
        default:
          (config as Record<string, unknown>)[envKey] = value.trim();
      }
    }

    // Add warnings for missing optional but recommended variables
    if (!rule.required && !value && key.includes('SECRET')) {
      warnings.push(
        `${key} is not set. This may reduce security in production.`
      );
    }
  }

  // Environment-specific validations
  const nodeEnv = process.env.NODE_ENV;

  if (nodeEnv === 'production') {
    // Production-specific warnings
    if (process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY?.includes('test')) {
      warnings.push('Using test Stripe keys in production environment');
    }

    if (process.env.DEBUG === 'true') {
      warnings.push('Debug mode is enabled in production');
    }

    if (!process.env.SENTRY_DSN) {
      warnings.push(
        'SENTRY_DSN not set - error tracking disabled in production'
      );
    }
  }

  if (nodeEnv === 'development') {
    // Development-specific warnings
    if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY?.includes('test')) {
      warnings.push('Using live Stripe keys in development environment');
    }
  }

  return {
    valid: errors.length === 0,
    config: config as EnvConfig,
    errors,
    warnings,
  };
}

/**
 * Validates environment on startup and throws if invalid
 */
export function validateEnvironmentOnStartup(): EnvConfig {
  console.log('🔍 Validating environment variables...');

  const validation = validateEnvironmentVariables();

  // Log warnings
  if (validation.warnings.length > 0) {
    console.warn('⚠️  Environment warnings:');
    validation.warnings.forEach(warning => console.warn(`   - ${warning}`));
  }

  // Throw if validation failed
  if (!validation.valid) {
    console.error('❌ Environment validation failed:');
    validation.errors.forEach(error => console.error(`   - ${error}`));
    console.error(
      '\n💡 Please check your .env.local file and compare with .env.example'
    );
    throw new EnvironmentValidationError(
      `Environment validation failed:\n${validation.errors.join('\n')}`
    );
  }

  console.log('✅ Environment validation passed');
  return validation.config;
}

/**
 * Get validated environment config (cached after first validation)
 */
let cachedConfig: EnvConfig | null = null;

export function getEnvConfig(): EnvConfig {
  if (!cachedConfig) {
    cachedConfig = validateEnvironmentOnStartup();
  }
  return cachedConfig;
}

/**
 * Utility function to check if we're in development
 */
export function isDevelopment(): boolean {
  return getEnvConfig().NODE_ENV === 'development';
}

/**
 * Utility function to check if we're in production
 */
export function isProduction(): boolean {
  return getEnvConfig().NODE_ENV === 'production';
}

/**
 * Utility function to check if we're in staging
 */
export function isStaging(): boolean {
  return getEnvConfig().NODE_ENV === 'staging';
}

/**
 * Get CORS allowed origins as array
 */
export function getCORSAllowedOrigins(): string[] {
  const config = getEnvConfig();
  if (!config.CORS_ALLOWED_ORIGINS) {
    return [config.NEXT_PUBLIC_APP_URL];
  }
  return config.CORS_ALLOWED_ORIGINS.split(',').map(origin => origin.trim());
}

/**
 * Check if emails should be disabled (useful for development)
 */
export function shouldDisableEmails(): boolean {
  const config = getEnvConfig();
  return (
    config.DISABLE_EMAILS === 'true' ||
    (isDevelopment() && !config.EMAIL_SERVICE_API_KEY)
  );
}

/**
 * Check if debug mode is enabled
 */
export function isDebugMode(): boolean {
  const config = getEnvConfig();
  return config.DEBUG === 'true';
}
