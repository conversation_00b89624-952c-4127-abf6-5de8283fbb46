'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getOrCreateProfile } from '@/lib/profiles';
import { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';
import { DesktopHeader } from '@/components/navigation/DesktopHeader';
import { useBackButton } from '@/hooks/useBackButton';
import { Loader2 } from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  requiredUserType?: 'influencer' | 'business';
  hideHeader?: boolean;
}

export function DashboardLayout({
  children,
  requiredUserType,
  hideHeader = false,
}: DashboardLayoutProps) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { shouldShowBackButton } = useBackButton();

  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/prijava');
      return;
    }

    loadProfile();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, authLoading, router]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const { data, error } = await getOrCreateProfile(user!.id);

      if (error) {
        console.error('Profile loading error:', error);
        setError('Greška pri učitavanju profila');
        return;
      }

      if (!data) {
        router.push('/profil/kreiranje');
        return;
      }

      // Check if profile is completed
      if (!data.profile_completed) {
        if (data.user_type === 'influencer') {
          router.push('/profil/kreiranje/influencer/onboarding');
        } else if (data.user_type === 'business') {
          router.push('/profil/kreiranje/biznis/onboarding');
        } else {
          router.push('/profil/kreiranje');
        }
        return;
      }

      setProfile(data);

      // Provjeri da li korisnik ima pravo pristupa ovoj stranici
      if (requiredUserType && data.user_type !== requiredUserType) {
        // Preusmjeri na odgovarajući dashboard
        if (data.user_type === 'influencer') {
          router.push('/dashboard/influencer');
        } else if (data.user_type === 'business') {
          router.push('/dashboard/biznis');
        }
        return;
      }
    } catch (err) {
      console.error('Unexpected error in loadProfile:', err);
      setError('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Učitavanje...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">Greška</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Pokušaj ponovo
          </button>
        </div>
      </div>
    );
  }

  // No profile state
  if (!profile) {
    return null; // Router redirect will handle this
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Desktop Header - visible only on desktop */}
      {!hideHeader && (
        <div className="hidden md:block">
          <DesktopHeader userType={profile.user_type} profile={profile} />
        </div>
      )}

      {/* Mobile Navigation */}
      <ResponsiveNavigation
        userType={profile.user_type}
        profile={profile}
        showBackButton={shouldShowBackButton}
        onBackClick={() => router.back()}
      />

      {/* Main Content */}
      <div className="flex flex-col min-h-screen">
        {/* Page Content */}
        <main className="flex-1 overflow-auto pb-16 pt-2 md:pb-0 md:pt-0">
          <div className="px-6 pt-4 pb-6">{children}</div>
        </main>
      </div>
    </div>
  );
}
