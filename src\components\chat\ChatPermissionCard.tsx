'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChatButton } from '@/components/ui/chat-button';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Check, Clock } from 'lucide-react';
import { ChatPermission } from '@/lib/chat-permissions';
import {
  approveBusinessChat,
  approveInfluencerChat,
} from '@/lib/chat-permissions';
import { useAuth } from '@/contexts/AuthContext';

interface ChatPermissionCardProps {
  permission: ChatPermission;
  onUpdate: () => void;
}

export function ChatPermissionCard({
  permission,
  onUpdate,
}: ChatPermissionCardProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  const userType = user?.user_metadata?.user_type || 'influencer';
  const isBusiness = userType === 'business';
  const isInfluencer = userType === 'influencer';

  const canApprove = () => {
    if (isBusiness && !permission.business_approved) return true;
    if (isInfluencer && !permission.influencer_approved) return true;
    return false;
  };

  const getStatusBadge = () => {
    if (permission.chat_enabled) {
      return (
        <Badge variant="default" className="bg-green-500">
          <Check className="h-3 w-3 mr-1" />
          Omogućen
        </Badge>
      );
    }

    if (canApprove()) {
      return (
        <Badge variant="secondary">
          <Clock className="h-3 w-3 mr-1" />
          Čeka odobrenje
        </Badge>
      );
    }

    return (
      <Badge variant="outline">
        <Clock className="h-3 w-3 mr-1" />
        Čeka partnera
      </Badge>
    );
  };

  const handleApprove = async () => {
    if (!user || loading) return;

    setLoading(true);
    try {
      if (isBusiness) {
        await approveBusinessChat(
          permission.business_id,
          permission.influencer_id,
          permission.offer_id || undefined,
          permission.campaign_application_id || undefined
        );
      } else {
        await approveInfluencerChat(
          permission.business_id,
          permission.influencer_id,
          permission.offer_id || undefined,
          permission.campaign_application_id || undefined
        );
      }
      onUpdate();
    } catch (error) {
      console.error('Error approving chat:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTitle = () => {
    if (permission.offer_id) {
      return 'Direktna ponuda';
    } else if (permission.campaign_application_id) {
      return 'Kampanja aplikacija';
    }
    return 'Chat dozvola';
  };

  const getDescription = () => {
    if (permission.chat_enabled) {
      return 'Možete komunicirati sa partnerom putem chat-a.';
    }

    if (canApprove()) {
      return 'Odobrite chat da biste mogli komunicirati sa partnerom.';
    }

    return 'Čeka se odobrenje partnera za omogućavanje chat-a.';
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            {getTitle()}
          </CardTitle>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">{getDescription()}</p>

        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between text-sm">
            <span>Biznis odobrenje:</span>
            {permission.business_approved ? (
              <Badge variant="outline" className="text-green-600">
                <Check className="h-3 w-3 mr-1" />
                Odobreno
              </Badge>
            ) : (
              <Badge variant="outline" className="text-orange-600">
                <Clock className="h-3 w-3 mr-1" />
                Čeka
              </Badge>
            )}
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>Influencer odobrenje:</span>
            {permission.influencer_approved ? (
              <Badge variant="outline" className="text-green-600">
                <Check className="h-3 w-3 mr-1" />
                Odobreno
              </Badge>
            ) : (
              <Badge variant="outline" className="text-orange-600">
                <Clock className="h-3 w-3 mr-1" />
                Čeka
              </Badge>
            )}
          </div>
        </div>

        {canApprove() && (
          <Button onClick={handleApprove} disabled={loading} className="w-full">
            <Check className="h-4 w-4 mr-2" />
            {loading ? 'Odobrava se...' : 'Odobri chat'}
          </Button>
        )}

        {permission.chat_enabled && (
          <ChatButton
            variant="primary"
            className="w-full"
            onClick={() => {
              // Navigate to chat - this will be implemented when we integrate with the main chat
              console.log('Navigate to chat room');
            }}
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            Otvori chat
          </ChatButton>
        )}
      </CardContent>
    </Card>
  );
}
