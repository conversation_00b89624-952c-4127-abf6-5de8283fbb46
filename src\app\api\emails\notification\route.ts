import { NextRequest, NextResponse } from 'next/server';
import { sendEmail, generateNotificationTemplate } from '@/lib/email-service';

export async function POST(request: NextRequest) {
  try {
    const { email, userName, notificationType, details } = await request.json();

    if (!email || !userName || !notificationType || !details) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    const validTypes = [
      'new_offer',
      'new_order',
      'application_approved',
      'payment_required_offer',
      'payment_required_order',
      'work_submitted',
      'payment_completed',
      'work_approved',
    ];
    if (!validTypes.includes(notificationType)) {
      return NextResponse.json(
        { error: 'Invalid notification type' },
        { status: 400 }
      );
    }

    const { htmlContent, textContent } = generateNotificationTemplate(
      userName,
      email,
      notificationType,
      details
    );

    const subjectMap = {
      new_offer: 'Nova direktna ponuda - INFLUEXUS',
      new_order: 'Nova narudžba paketa - INFLUEXUS',
      application_approved: 'Aplikacija prihvaćena - INFLUEXUS',
      payment_required_offer:
        'Izvršite plaćanje - Ponuda prihvaćena - INFLUEXUS',
      payment_required_order:
        'Izvršite plaćanje - Narudžba prihvaćena - INFLUEXUS',
      work_submitted: 'Rad završen - Potreban pregled - INFLUEXUS',
      payment_completed: 'Plaćanje izvršeno - Možete početi - INFLUEXUS',
      work_approved: 'Rad odobren - Čestitamo! - INFLUEXUS',
    };

    const result = await sendEmail({
      to: email,
      subject: subjectMap[notificationType as keyof typeof subjectMap],
      htmlContent,
      textContent,
    });

    if (result.success) {
      return NextResponse.json(
        { message: 'Notification email sent successfully' },
        { status: 200 }
      );
    } else {
      return NextResponse.json(
        { error: 'Failed to send notification email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Notification email error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
