// Type guard utilities and error handling helpers
export function isErrorWithMessage(
  error: unknown
): error is { message: string } {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as { message: unknown }).message === 'string'
  );
}

export function getErrorMessage(error: unknown): string {
  if (isErrorWithMessage(error)) {
    return error.message;
  }
  return String(error);
}

// Null/undefined guards
export function isNotNull<T>(value: T | null): value is T {
  return value !== null;
}

export function isNotUndefined<T>(value: T | undefined): value is T {
  return value !== undefined;
}

export function isNotNullish<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

// Safe string conversion
export function safeString(
  value: string | null | undefined,
  defaultValue = ''
): string {
  return isNotNullish(value) ? value : defaultValue;
}

export function safeNumber(
  value: number | null | undefined,
  defaultValue = 0
): number {
  return isNotNullish(value) ? value : defaultValue;
}

// Convert string to number safely
export function stringToNumber(
  value: string | null | undefined,
  defaultValue = 0
): number {
  if (!isNotNullish(value)) return defaultValue;
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

// Array helpers
export function safeArray<T>(value: T[] | null | undefined): T[] {
  return value || [];
}

// Convert null to undefined (for compatibility)
export function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

// Convert undefined to null (for database compatibility)
export function undefinedToNull<T>(value: T | undefined): T | null {
  return value === undefined ? null : value;
}

// Type assertion helpers for common patterns
export function assertString(value: unknown, context?: string): string {
  if (typeof value === 'string') {
    return value;
  }
  throw new Error(
    `Expected string${context ? ` for ${context}` : ''}, got ${typeof value}`
  );
}

export function assertNumber(value: unknown, context?: string): number {
  if (typeof value === 'number') {
    return value;
  }
  throw new Error(
    `Expected number${context ? ` for ${context}` : ''}, got ${typeof value}`
  );
}

// Database result helpers
export interface DatabaseResult<T> {
  data: T | null;
  error: unknown;
}

export function createSuccessResult<T>(data: T): DatabaseResult<T> {
  return { data, error: null };
}

export function createErrorResult<T>(error: unknown): DatabaseResult<T> {
  return { data: null, error };
}

// Safe JSON handling
export function safeJsonParse<T>(
  jsonString: string | null | undefined,
  defaultValue: T
): T {
  if (!isNotNullish(jsonString)) {
    return defaultValue;
  }

  try {
    return JSON.parse(jsonString) as T;
  } catch {
    return defaultValue;
  }
}
