'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Mail, CheckCircle, ArrowLeft } from 'lucide-react';
import Image from 'next/image';

export default function PotvrdaEmailPage() {
  const [emailSent, setEmailSent] = useState(false);

  const handleResendEmail = () => {
    // TODO: Implement resend email functionality
    setEmailSent(true);
    setTimeout(() => setEmailSent(false), 3000);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/"
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Početna</span>
          </Link>
          <div className="flex items-center space-x-3">
            <Image
              src="/images/influexus_logo_white.webp"
              alt="Influexus Logo"
              width={32}
              height={32}
              className="rounded-lg"
            />
            <span className="text-xl font-bold gradient-text-auth">
              Influexus
            </span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="h-8 w-8 text-primary" />
            </div>
            <CardTitle className="text-2xl">Potvrdite svoj email</CardTitle>
            <CardDescription>
              Poslali smo vam email sa linkom za potvrdu naloga
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                Provjerite svoj email i kliknite na link za potvrdu da biste
                aktivirali svoj nalog.
              </p>

              <div className="bg-muted/50 rounded-lg p-4">
                <h4 className="font-medium mb-2">Šta dalje?</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>1. Provjerite svoj email (uključujući spam folder)</li>
                  <li>2. Kliknite na link za potvrdu</li>
                  <li>3. Vratite se ovdje da završite setup profila</li>
                </ul>
              </div>
            </div>

            <div className="space-y-3">
              <Button
                onClick={handleResendEmail}
                variant="outline"
                className="w-full"
                disabled={emailSent}
              >
                {emailSent ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Email poslat!
                  </>
                ) : (
                  'Pošalji ponovo email'
                )}
              </Button>

              <Button asChild className="w-full">
                <Link href="/prijava">Već sam potvrdio email</Link>
              </Button>
            </div>

            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                Niste dobili email? Provjerite spam folder ili kontaktirajte{' '}
                <Link href="/kontakt" className="text-primary hover:underline">
                  podršku
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
