'use client';

import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Users, Clock, CheckCircle2, Sparkles } from 'lucide-react';
import { ChatRoom as ChatRoomType } from '@/lib/chat';
import { getUserChatRooms } from '@/lib/chat';
import { useAuth } from '@/contexts/AuthContext';
import { formatChatDate } from '@/lib/date-utils';
import { getDisplayName, getInitials } from '@/lib/utils';

interface ChatListProps {
  onSelectRoom: (room: ChatRoomType) => void;
}

export function ChatList({ onSelectRoom }: ChatListProps) {
  const { user } = useAuth();
  const [rooms, setRooms] = useState<ChatRoomType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadChatRooms();
    }
  }, [user]);

  const loadChatRooms = async () => {
    setLoading(true);
    try {
      const { data, error } = await getUserChatRooms();
      if (error) {
        console.error('Error loading chat rooms:', error);
      } else {
        setRooms(data || []);
      }
    } catch (error) {
      console.error('Error loading chat rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatLastMessageTime = (timestamp: string | null) => {
    return formatChatDate(timestamp);
  };

  const getOtherParticipant = (room: ChatRoomType) => {
    if (!user) return null;
    const userType = user.user_metadata?.user_type || 'influencer';

    if (userType === 'business') {
      return room.influencer_profile;
    } else {
      return room.business_profile;
    }
  };

  const getRoomTypeLabel = (roomType: string) => {
    switch (roomType) {
      case 'campaign_application':
        return 'Kampanja';
      case 'direct_offer':
        return 'Direktna ponuda';
      default:
        return 'Chat';
    }
  };

  if (loading) {
    return (
      <div className="h-full px-4 space-y-4">
        {[1, 2, 3, 4].map(i => (
          <div
            key={i}
            className="flex items-center gap-3 p-3 rounded-lg border"
          >
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="flex-1 space-y-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-12" />
              </div>
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-3 w-full" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (rooms.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-muted-foreground py-12">
          <div className="relative mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto">
              <Users className="h-10 w-10 text-blue-500" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
              <Sparkles className="h-3 w-3 text-yellow-600" />
            </div>
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">
            Nemate aktivne razgovore
          </h3>
          <p className="text-sm mb-4">
            Razgovori će se pojaviti kada prihvatite ponude ili aplikacije.
          </p>
          <div className="flex items-center justify-center gap-4 text-xs">
            <Badge
              variant="secondary"
              className="bg-blue-50 text-blue-700 border-blue-200"
            >
              <CheckCircle2 className="h-3 w-3" />
              Sigurno
            </Badge>
            <Badge
              variant="secondary"
              className="bg-green-50 text-green-700 border-green-200"
            >
              <Clock className="h-3 w-3" />
              Trenutno
            </Badge>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto overflow-x-hidden px-4 space-y-1">
      {rooms.map(room => {
        const otherParticipant = getOtherParticipant(room);
        const hasUnread = room.unread_count && room.unread_count > 0;

        return (
          <Button
            key={room.id}
            variant="ghost"
            className={`w-full justify-start p-4 h-auto transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:shadow-md rounded-lg min-w-0 ${
              hasUnread ? 'bg-blue-50/50 border border-blue-200' : ''
            }`}
            onClick={() => onSelectRoom(room)}
          >
            <div className="flex items-center gap-3 w-full min-w-0">
              <div className="relative flex-shrink-0">
                <Avatar className="h-12 w-12 ring-2 ring-white shadow-sm">
                  <AvatarImage src={otherParticipant?.avatar_url || ''} />
                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white">
                    {otherParticipant
                      ? getInitials(
                          getDisplayName(otherParticipant) !==
                            'Ime i prezime skriveno'
                            ? getDisplayName(otherParticipant)
                            : otherParticipant.username
                        )
                      : '?'}
                  </AvatarFallback>
                </Avatar>
                {hasUnread && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-white">
                      {room.unread_count}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex-1 text-left min-w-0">
                <div className="flex items-center justify-between min-w-0">
                  <h4
                    className={`font-medium truncate ${hasUnread ? 'font-semibold text-gray-900' : 'text-gray-700'}`}
                  >
                    {getDisplayName(otherParticipant) !==
                    'Ime i prezime skriveno'
                      ? getDisplayName(otherParticipant)
                      : otherParticipant?.username || 'Nepoznato'}
                  </h4>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <span className="text-xs text-muted-foreground">
                      {formatLastMessageTime(room.last_message_at)}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-1 min-w-0">
                  <p className="text-sm text-muted-foreground truncate">
                    @{otherParticipant?.username || 'nepoznato'}
                  </p>
                  <Badge
                    variant="outline"
                    className={`text-xs flex-shrink-0 ${
                      room.room_type === 'campaign_application'
                        ? 'border-green-200 bg-green-50 text-green-700'
                        : 'border-blue-200 bg-blue-50 text-blue-700'
                    }`}
                  >
                    {getRoomTypeLabel(room.room_type)}
                  </Badge>
                </div>

                <p
                  className={`text-sm mt-1 truncate ${hasUnread ? 'font-medium text-gray-800' : 'text-muted-foreground'}`}
                >
                  {room.room_title}
                </p>
              </div>
            </div>
          </Button>
        );
      })}
    </div>
  );
}
