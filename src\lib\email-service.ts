const BREVO_API_KEY =
  process.env.BREVO_API_KEY ||
  '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';
const BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email';

export interface EmailOptions {
  to: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
}

export async function sendEmail({
  to,
  subject,
  htmlContent,
  textContent,
}: EmailOptions) {
  const emailData = {
    sender: {
      name: 'INFLUEXUS',
      email: '<EMAIL>',
    },
    to: [{ email: to }],
    subject: subject,
    htmlContent: htmlContent,
    textContent: textContent || '',
  };

  try {
    const response = await fetch(BREVO_API_URL, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'api-key': BREVO_API_KEY,
      },
      body: JSON.stringify(emailData),
    });

    const result = await response.json();

    if (response.ok) {
      return { success: true, response: result };
    } else {
      console.error('Failed to send email:', result);
      return { success: false, error: result };
    }
  } catch (error) {
    console.error('Failed to send email:', error);
    return { success: false, error };
  }
}

export function generateEmailVerificationTemplate(
  verificationUrl: string,
  userEmail: string
) {
  const htmlContent = `
<!DOCTYPE html>
<html lang="sr" style="margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Potvrda Email Adrese - INFLUEXUS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .header {
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(219, 39, 119, 0.6) 50%, rgba(147, 51, 234, 0.8) 100%);
            padding: 32px 24px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(219, 39, 119, 0.05) 50%, rgba(147, 51, 234, 0.1) 100%);
        }
        
        .logo {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: bold;
            color: white;
        }
        
        .main-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.05) 0%, rgba(236, 72, 153, 0.03) 30%, rgba(147, 51, 234, 0.08) 100%);
            margin: 24px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(147, 51, 234, 0.1);
        }
        
        .main-card::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.02) 0%, rgba(236, 72, 153, 0.01) 50%, rgba(147, 51, 234, 0.04) 100%);
        }
        
        .content {
            position: relative;
            padding: 40px 32px;
            text-align: center;
        }
        
        
        .greeting {
            font-size: 32px;
            font-weight: bold;
            background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 16px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        
        .verification-button {
            display: inline-block;
            background: linear-gradient(135deg, #7c3aed 0%, #ec4899 50%, #7c3aed 100%);
            color: white !important;
            padding: 16px 32px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 16px rgba(147, 51, 234, 0.2);
            transition: all 0.3s ease;
            margin-bottom: 32px;
        }
        
        .verification-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(147, 51, 234, 0.3);
        }
        
        .alternative-section {
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid rgba(147, 51, 234, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-top: 24px;
        }
        
        .alternative-text {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 12px;
        }
        
        .alternative-link {
            background: white;
            border: 1px solid rgba(147, 51, 234, 0.1);
            border-radius: 8px;
            padding: 12px;
            font-size: 12px;
            color: #64748b;
            word-break: break-all;
            font-family: 'Courier New', monospace;
        }
        
        .footer {
            background: rgba(248, 250, 252, 0.5);
            padding: 24px 32px;
            text-align: center;
            border-top: 1px solid rgba(147, 51, 234, 0.05);
        }
        
        .footer-text {
            font-size: 12px;
            color: #94a3b8;
            line-height: 1.6;
        }
        
        .footer-link {
            color: #7c3aed;
            text-decoration: none;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            .email-container {
                border-radius: 0;
                margin: -20px;
            }
            
            .main-card {
                margin: 16px;
            }
            
            .content {
                padding: 32px 24px;
            }
            
            .greeting {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <div class="logo-text">INFLUEXUS</div>
            </div>
        </div>
        
        <div class="main-card">
            <div class="content">
                <h1 class="greeting">Dobrodošli!</h1>
                <p class="subtitle">
                    Hvala što ste se registrovali na INFLUEXUS platformu.<br>
                    Da biste aktivirali svoj nalog, molimo vas da potvrdite svoju email adresu.
                </p>
                
                <a href="${verificationUrl}" class="verification-button">
                    Potvrdi Email Adresu
                </a>
                
                <div class="alternative-section">
                    <p class="alternative-text">
                        Ako dugme ne radi, kopirajte i zalepite ovaj link u vaš browser:
                    </p>
                    <div class="alternative-link">
                        ${verificationUrl}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Ovaj email je poslat na <strong>${userEmail}</strong><br>
                Ako niste kreirali nalog na INFLUEXUS platformi, molimo vas da ignorišete ovaj email.<br><br>
                <a href="#" class="footer-link">Uslovi korišćenja</a> | 
                <a href="#" class="footer-link">Politika privatnosti</a><br><br>
                © 2024 INFLUEXUS. Sva prava zadržana.
            </p>
        </div>
    </div>
</body>
</html>
  `;

  const textContent = `
INFLUEXUS - Potvrda Email Adrese

Dobrodošli!

Hvala što ste se registrovali na INFLUEXUS platformu.
Da biste aktivirali svoj nalog, molimo vas da potvrdite svoju email adresu.

Kliknite na sledeći link da biste potvrdili svoju email adresu:
${verificationUrl}

Ovaj email je poslat na ${userEmail}
Ako niste kreirali nalog na INFLUEXUS platformi, molimo vas da ignorišete ovaj email.

© 2024 INFLUEXUS. Sva prava zadržana.
  `;

  return { htmlContent, textContent };
}

// Import the redesigned templates
export {
  generatePasswordResetTemplate,
  generateWelcomeTemplate,
  generateNotificationTemplate,
} from './email-templates';
