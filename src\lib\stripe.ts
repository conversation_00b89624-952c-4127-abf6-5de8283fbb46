import { loadStripe } from '@stripe/stripe-js';
import Stripe from 'stripe';

// Client-side Stripe
export const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

// Server-side Stripe (only initialize on server)
export const stripe = process.env.STRIPE_SECRET_KEY
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-08-27.basil',
    })
  : null;

// Featured campaign price mapping
export const FEATURED_CAMPAIGN_STRIPE_PRICES = {
  7: 'price_1S4qTIAkyndIVMJtAEpAZlpa', // €30.00 for 7 days
  14: 'price_1S4qTkAkyndIVMJt4KVlnWs5', // €50.00 for 14 days
} as const;

// Subscription price mapping
export const SUBSCRIPTION_STRIPE_PRICES = {
  business: {
    monthly: 'price_1S5oCBAkyndIVMJtm3aOs0V7', // €29.99/month
  },
  influencer: {
    monthly: 'price_1S5oCHAkyndIVMJt9wQXDVGp', // €19.99/month
  },
} as const;
