import { supabase } from './supabase';
import {
  hasActivePremiumSubscription,
  SUBSCRIPTION_LIMITS,
} from './subscriptions';

export interface CampaignApplicationLimitResult {
  canApply: boolean;
  remainingApplications: number;
  totalApplicationsThisMonth: number;
  resetDate: string;
  isFreePlan: boolean;
}

/**
 * Računa mjesečni period od datuma kada je korisnik završio onboarding
 * @param onboardingCompletedDate Datum kada je završen onboarding
 * @returns Početni i završni datum trenutnog mjeseca od onboarding datuma
 */
function getMonthlyPeriodFromOnboarding(onboardingCompletedDate: string): {
  periodStart: Date;
  periodEnd: Date;
} {
  const onboardingDate = new Date(onboardingCompletedDate);
  const now = new Date();

  // Uzimamo dan u mjesecu kada je završen onboarding
  const onboardingDay = onboardingDate.getDate();

  // Kreiramo datum početka trenutnog mjeseca (isti dan kao onboarding)
  const periodStart = new Date(
    now.getFullYear(),
    now.getMonth(),
    onboardingDay
  );

  // Ako je trenutni datum prije onboarding dana u mjesecu,
  // onda je period počeo prošlog mjeseca
  if (now.getDate() < onboardingDay) {
    periodStart.setMonth(periodStart.getMonth() - 1);
  }

  // Kraj perioda je jedan dan prije sljedećeg onboarding dana
  const periodEnd = new Date(periodStart);
  periodEnd.setMonth(periodEnd.getMonth() + 1);
  periodEnd.setDate(periodEnd.getDate() - 1);
  periodEnd.setHours(23, 59, 59, 999);

  return { periodStart, periodEnd };
}

/**
 * Provjerava da li free influencer korisnik može aplicirati na kampanju
 * @param influencerId ID influencer korisnika
 * @returns Objekt sa informacijama o limitu
 */
export async function checkCampaignApplicationLimit(
  influencerId: string
): Promise<CampaignApplicationLimitResult> {
  const FREE_PLAN_MONTHLY_LIMIT =
    SUBSCRIPTION_LIMITS.free.influencer.monthlyApplications;

  try {
    // Check if user has premium subscription using new system
    const hasActiveSubscription = await hasActivePremiumSubscription(
      influencerId,
      'influencer'
    );

    // Dohvatamo influencer podatke i profile podatke
    const { data: influencerData, error: influencerError } = await supabase
      .from('influencers')
      .select(
        `
        subscription_type,
        profiles!inner(
          onboarding_completed,
          created_at,
          onboarding_completed_at
        )
      `
      )
      .eq('id', influencerId)
      .single();

    if (influencerError || !influencerData) {
      throw new Error('Influencer profil nije pronađen');
    }

    // Use new subscription system (no fallback for development)
    const isFreePlan = !hasActiveSubscription;

    // Ako je premium plan, nema ograničenja
    if (!isFreePlan) {
      return {
        canApply: true,
        remainingApplications: Infinity,
        totalApplicationsThisMonth: 0,
        resetDate: '',
        isFreePlan: false,
      };
    }

    // Za free plan, provjeravamo da li je onboarding završen
    const profile = influencerData.profiles;
    if (!profile.onboarding_completed) {
      throw new Error('Onboarding nije završen');
    }

    // Koristimo onboarding_completed_at ako postoji, inače fallback na created_at
    const onboardingDate =
      profile.onboarding_completed_at || profile.created_at;
    if (!onboardingDate) {
      throw new Error('Datum onboarding-a nije pronađen');
    }

    // Računamo trenutni mjesečni period
    const { periodStart, periodEnd } =
      getMonthlyPeriodFromOnboarding(onboardingDate);

    // Brojimo aplikacije u trenutnom mjesečnom periodu
    const { data: applications, error: applicationsError } = await supabase
      .from('campaign_applications')
      .select('id, applied_at')
      .eq('influencer_id', influencerId)
      .gte('applied_at', periodStart.toISOString())
      .lte('applied_at', periodEnd.toISOString());

    if (applicationsError) {
      throw new Error('Greška pri dohvaćanju aplikacija');
    }

    const totalApplicationsThisMonth = applications?.length || 0;
    const remainingApplications = Math.max(
      0,
      FREE_PLAN_MONTHLY_LIMIT - totalApplicationsThisMonth
    );
    const canApply = remainingApplications > 0;

    // Sljedeći reset datum
    const nextResetDate = new Date(periodEnd);
    nextResetDate.setDate(nextResetDate.getDate() + 1);
    nextResetDate.setHours(0, 0, 0, 0);

    return {
      canApply,
      remainingApplications,
      totalApplicationsThisMonth,
      resetDate: nextResetDate.toISOString(),
      isFreePlan: true,
    };
  } catch (error) {
    console.error('Greška pri provjeri limita aplikacija na kampanje:', error);
    // U slučaju greške, dozvoljavamo aplikaciju da ne blokiramo korisnika
    return {
      canApply: true,
      remainingApplications: 0,
      totalApplicationsThisMonth: 0,
      resetDate: '',
      isFreePlan: true,
    };
  }
}
