import { NextRequest, NextResponse } from 'next/server';
import { stripe, FEATURED_CAMPAIGN_STRIPE_PRICES } from '@/lib/stripe';
import { canPromoteCampaign } from '@/lib/featured-campaigns';

export async function POST(req: NextRequest) {
  try {
    if (!stripe) {
      return NextResponse.json(
        { error: 'Stripe not configured' },
        { status: 500 }
      );
    }

    const { campaignId, businessId, durationDays } = await req.json();
    console.log('Received checkout request:', {
      campaignId,
      businessId,
      durationDays,
    });

    // Validate input
    if (!campaignId || !businessId || !durationDays) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (![7, 14].includes(durationDays)) {
      return NextResponse.json(
        { error: 'Invalid duration days' },
        { status: 400 }
      );
    }

    // Check if campaign can be promoted
    const { canPromote, reason } = await canPromoteCampaign(
      campaignId,
      businessId
    );

    if (!canPromote) {
      return NextResponse.json({ error: reason }, { status: 400 });
    }

    // Get the Stripe price ID
    const priceId = FEATURED_CAMPAIGN_STRIPE_PRICES[durationDays as 7 | 14];

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/campaigns?payment=success&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/campaigns/${campaignId}?payment=cancelled`,
      metadata: {
        campaignId,
        businessId,
        durationDays: durationDays.toString(),
        type: 'featured_campaign',
      },
      client_reference_id: businessId,
    });

    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
