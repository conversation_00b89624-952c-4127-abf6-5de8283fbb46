'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2, Plus, Package, Trash2, Edit } from 'lucide-react';
import { toast } from 'sonner';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import {
  getPlatforms,
  getContentTypes,
  getInfluencerPackages,
  createPricingPackage,
  deletePricingPackage,
  VIDEO_DURATIONS,
  type Platform,
  type ContentType,
  type PricingPackage,
  type PackageCreationData,
} from '@/lib/pricing-packages';

// Video duration options imported from lib

// Package creation form schema
const packageSchema = z.object({
  platform_id: z.number().min(1, 'Izaberite platformu'),
  content_type_id: z.number().min(1, 'Izaberite tip sadržaja'),
  quantity: z.number().min(1, 'Količina mora biti najmanje 1'),
  video_duration: z.string().optional().or(z.literal('')),
  price: z.number().min(0.01, 'Cijena mora biti veća od 0'),
});

type PackageForm = z.infer<typeof packageSchema>;

export default function PricingPackagesPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [contentTypes, setContentTypes] = useState<ContentType[]>([]);
  const [packages, setPackages] = useState<PricingPackage[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<number | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<PackageForm>({
    resolver: zodResolver(packageSchema),
  });

  const watchedPlatform = watch('platform_id');
  const watchedContentType = watch('content_type_id');
  const watchedQuantity = watch('quantity');
  const watchedVideoDuration = watch('video_duration');

  useEffect(() => {
    if (user) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  // Update content types when platform changes
  useEffect(() => {
    if (watchedPlatform) {
      setSelectedPlatform(watchedPlatform);
      setValue('content_type_id', 0); // Reset content type
      setValue('video_duration', ''); // Reset video duration
    }
  }, [watchedPlatform, setValue]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load platforms
      const { data: platformsData, error: platformsError } =
        await getPlatforms();
      if (platformsError) {
        console.error('Error loading platforms:', platformsError);
        toast.error('Greška pri učitavanju platformi');
        return;
      }
      setPlatforms(platformsData || []);

      // Load content types
      const { data: contentTypesData, error: contentTypesError } =
        await getContentTypes();
      if (contentTypesError) {
        console.error('Error loading content types:', contentTypesError);
        toast.error('Greška pri učitavanju tipova sadržaja');
        return;
      }
      setContentTypes(contentTypesData || []);

      // Load existing packages
      const { data: packagesData, error: packagesError } =
        await getInfluencerPackages(user!.id);
      if (packagesError) {
        console.error('Error loading packages:', packagesError);
        toast.error('Greška pri učitavanju paketa');
        return;
      }
      setPackages(packagesData || []);
    } catch (err) {
      console.error('Error in loadData:', err);
      toast.error('Greška pri učitavanju podataka');
    } finally {
      setLoading(false);
    }
  };

  // generatePackageName is now imported from lib

  const getFilteredContentTypes = () => {
    return contentTypes.filter(ct => ct.platform_id === selectedPlatform);
  };

  const getSelectedPlatform = () => {
    return platforms.find(p => p.id === watchedPlatform);
  };

  const getSelectedContentType = () => {
    return contentTypes.find(ct => ct.id === watchedContentType);
  };

  const shouldShowVideoDuration = () => {
    const contentType = getSelectedContentType();
    return contentType?.name.toLowerCase().includes('video');
  };

  const getPreviewPackageName = () => {
    const platform = getSelectedPlatform();
    const contentType = getSelectedContentType();

    if (!platform || !contentType || !watchedQuantity) {
      return '';
    }

    // Use imported generatePackageName function
    let name = `${watchedQuantity}x ${platform.name} ${contentType.name}`;

    if (
      watchedVideoDuration &&
      contentType.name.toLowerCase().includes('video')
    ) {
      name += ` ${watchedVideoDuration}`;
    }

    return name;
  };

  const onSubmit = async (data: PackageForm) => {
    if (!user) return;

    setSaving(true);
    try {
      console.log('Form data submitted:', data);

      // Clean video_duration - only include if it's a video content type and has a value
      const contentType = getSelectedContentType();
      const isVideoContent = contentType?.name.toLowerCase().includes('video');
      const cleanVideoDuration =
        isVideoContent && data.video_duration ? data.video_duration : undefined;

      const packageData: PackageCreationData = {
        platform_id: data.platform_id,
        content_type_id: data.content_type_id,
        quantity: data.quantity,
        video_duration: cleanVideoDuration,
        price: data.price,
      };

      console.log('Package data prepared:', packageData);

      const { data: createdPackage, error } = await createPricingPackage(
        user.id,
        packageData
      );

      if (error) {
        console.error('Error creating package:', error);
        const errorMessage =
          error.message || 'Nepoznata greška pri kreiranju paketa';
        toast.error(errorMessage);
        return;
      }

      console.log('Package created successfully:', createdPackage);
      toast.success('Paket je uspješno kreiran');
      reset();
      loadData(); // Refresh packages
    } catch (err) {
      console.error('Error in onSubmit:', err);
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Neočekivana greška pri kreiranju paketa';
      toast.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleDeletePackage = async (
    packageId: number,
    packageName: string
  ) => {
    if (
      !confirm(`Da li ste sigurni da želite obrisati paket "${packageName}"?`)
    ) {
      return;
    }

    try {
      const { error } = await deletePricingPackage(packageId);

      if (error) {
        console.error('Error deleting package:', error);
        toast.error('Greška pri brisanju paketa');
        return;
      }

      toast.success('Paket je uspješno obrisan');
      loadData(); // Refresh packages
    } catch (err) {
      console.error('Error in handleDeletePackage:', err);
      toast.error('Greška pri brisanju paketa');
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Paketi i cijene</h1>
          <p className="text-muted-foreground mt-2">
            Kreirajte pakete za različite platforme i tipove sadržaja
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Package Creation Form */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Plus className="h-5 w-5" />
                <CardTitle>Kreiraj novi paket</CardTitle>
              </div>
              <CardDescription>
                Dodajte novi paket sa platformom, tipom sadržaja i cijenom
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                {/* Platform Selection */}
                <div>
                  <Label htmlFor="platform">Platforma *</Label>
                  <Select
                    onValueChange={value =>
                      setValue('platform_id', parseInt(value))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Izaberite platformu" />
                    </SelectTrigger>
                    <SelectContent>
                      {platforms.map(platform => (
                        <SelectItem
                          key={platform.id}
                          value={platform.id.toString()}
                        >
                          <div className="flex items-center gap-2">
                            <PlatformIconSimple
                              platform={platform.name}
                              size="sm"
                            />
                            <span>{platform.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.platform_id && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.platform_id.message}
                    </p>
                  )}
                </div>

                {/* Content Type Selection */}
                <div>
                  <Label htmlFor="content_type">Tip sadržaja *</Label>
                  <Select
                    onValueChange={value =>
                      setValue('content_type_id', parseInt(value))
                    }
                    disabled={!selectedPlatform}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Izaberite tip sadržaja" />
                    </SelectTrigger>
                    <SelectContent>
                      {getFilteredContentTypes().map(contentType => (
                        <SelectItem
                          key={contentType.id}
                          value={contentType.id.toString()}
                        >
                          {contentType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.content_type_id && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.content_type_id.message}
                    </p>
                  )}
                </div>

                {/* Video Duration (conditional) */}
                {shouldShowVideoDuration() && (
                  <div>
                    <Label htmlFor="video_duration">Trajanje videa</Label>
                    <Select
                      onValueChange={value => setValue('video_duration', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Izaberite trajanje" />
                      </SelectTrigger>
                      <SelectContent>
                        {VIDEO_DURATIONS.map(duration => (
                          <SelectItem
                            key={duration.value}
                            value={duration.value}
                          >
                            {duration.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Quantity */}
                <div>
                  <Label htmlFor="quantity">Količina *</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    {...register('quantity', { valueAsNumber: true })}
                    placeholder="1"
                  />
                  {errors.quantity && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.quantity.message}
                    </p>
                  )}
                </div>

                {/* Price */}
                <div>
                  <Label htmlFor="price">Cijena (€) *</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    min="0.01"
                    {...register('price', { valueAsNumber: true })}
                    placeholder="100.00"
                  />
                  {errors.price && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.price.message}
                    </p>
                  )}
                </div>

                {/* Package Preview */}
                {getPreviewPackageName() && (
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      Naziv paketa:
                    </p>
                    <p className="font-medium">{getPreviewPackageName()}</p>
                  </div>
                )}

                {/* Submit Button */}
                <Button type="submit" disabled={saving} className="w-full">
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Plus className="h-4 w-4 mr-2" />
                  )}
                  Kreiraj paket
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Existing Packages */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <CardTitle>Vaši paketi</CardTitle>
              </div>
              <CardDescription>Upravljajte postojećim paketima</CardDescription>
            </CardHeader>
            <CardContent>
              {packages.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Nemate kreiran nijedan paket
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Kreirajte prvi paket koristeći formu lijevo
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {packages.map(pkg => (
                    <div
                      key={pkg.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <PlatformIconSimple
                          platform={pkg.platform_name}
                          size="lg"
                        />
                        <div>
                          <p className="font-medium">
                            {pkg.auto_generated_name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {pkg.price} €
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleDeletePackage(pkg.id, pkg.auto_generated_name)
                          }
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
