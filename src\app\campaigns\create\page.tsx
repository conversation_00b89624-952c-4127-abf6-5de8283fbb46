'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { CreateCampaignForm } from '@/components/campaigns/create-campaign-form';

export default function CreateCampaignPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  // Redirect if not authenticated
  if (!authLoading && !user) {
    router.push('/prijava');
    return null;
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-instagram-subtle relative overflow-hidden flex flex-col">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

      {/* Header */}
      <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/dashboard/biznis"
            className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad na dashboard</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-white">INFLUEXUS</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex-1 px-4 py-8">
        <div className="container mx-auto max-w-4xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              Kreiraj novu kampanju
            </h1>
            <p className="text-white/80">
              Popunite informacije o vašoj kampanji da biste privukli
              odgovarajuće influencere
            </p>
          </div>

          <CreateCampaignForm
            onSuccess={campaignId => {
              router.push(`/campaigns/${campaignId}`);
            }}
            onCancel={() => {
              router.push('/dashboard/biznis');
            }}
          />
        </div>
      </main>
    </div>
  );
}
