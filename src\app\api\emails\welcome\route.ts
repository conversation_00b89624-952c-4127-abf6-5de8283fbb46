import { NextRequest, NextResponse } from 'next/server';
import { sendEmail, generateWelcomeTemplate } from '@/lib/email-service';

export async function POST(request: NextRequest) {
  try {
    const { email, userName, userType } = await request.json();

    if (!email || !userName || !userType) {
      return NextResponse.json(
        { error: 'Email, user name, and user type are required' },
        { status: 400 }
      );
    }

    if (!['influencer', 'business'].includes(userType)) {
      return NextResponse.json(
        { error: 'User type must be either "influencer" or "business"' },
        { status: 400 }
      );
    }

    const { htmlContent, textContent } = generateWelcomeTemplate(
      userName,
      email,
      userType
    );

    const result = await sendEmail({
      to: email,
      subject: `Dobrodošli u INFLUEXUS ${userType === 'influencer' ? 'zajednicu influensera' : 'zajednicu brendova'}!`,
      htmlContent,
      textContent,
    });

    if (result.success) {
      return NextResponse.json(
        { message: 'Welcome email sent successfully' },
        { status: 200 }
      );
    } else {
      return NextResponse.json(
        { error: 'Failed to send welcome email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Welcome email error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
