export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '13.0.5';
  };
  public: {
    Tables: {
      business_platforms: {
        Row: {
          business_id: string | null;
          created_at: string | null;
          followers_count: number | null;
          handle: string;
          id: string;
          platform_id: number | null;
          updated_at: string | null;
        };
        Insert: {
          business_id?: string | null;
          created_at?: string | null;
          followers_count?: number | null;
          handle: string;
          id?: string;
          platform_id?: number | null;
          updated_at?: string | null;
        };
        Update: {
          business_id?: string | null;
          created_at?: string | null;
          followers_count?: number | null;
          handle?: string;
          id?: string;
          platform_id?: number | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'business_platforms_business_id_fkey';
            columns: ['business_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'business_platforms_platform_id_fkey';
            columns: ['platform_id'];
            isOneToOne: false;
            referencedRelation: 'platforms';
            referencedColumns: ['id'];
          },
        ];
      };
      business_target_categories: {
        Row: {
          business_id: string;
          category_id: number;
          created_at: string | null;
        };
        Insert: {
          business_id: string;
          category_id: number;
          created_at?: string | null;
        };
        Update: {
          business_id?: string;
          category_id?: number;
          created_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'business_target_categories_business_id_fkey';
            columns: ['business_id'];
            isOneToOne: false;
            referencedRelation: 'businesses';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'business_target_categories_category_id_fkey';
            columns: ['category_id'];
            isOneToOne: false;
            referencedRelation: 'categories';
            referencedColumns: ['id'];
          },
        ];
      };
      businesses: {
        Row: {
          active_campaigns_count: number | null;
          budget_range: string | null;
          company_name: string;
          company_size: string | null;
          contact_person_name: string | null;
          created_at: string | null;
          id: string;
          industry: string | null;
          is_verified: boolean | null;
          official_company_name: string | null;
          subscription_type: string | null;
          updated_at: string | null;
        };
        Insert: {
          active_campaigns_count?: number | null;
          budget_range?: string | null;
          company_name: string;
          company_size?: string | null;
          contact_person_name?: string | null;
          created_at?: string | null;
          id: string;
          industry?: string | null;
          is_verified?: boolean | null;
          official_company_name?: string | null;
          subscription_type?: string | null;
          updated_at?: string | null;
        };
        Update: {
          active_campaigns_count?: number | null;
          budget_range?: string | null;
          company_name?: string;
          company_size?: string | null;
          contact_person_name?: string | null;
          created_at?: string | null;
          id?: string;
          industry?: string | null;
          is_verified?: boolean | null;
          official_company_name?: string | null;
          subscription_type?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'businesses_id_fkey';
            columns: ['id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      campaign_applications: {
        Row: {
          additional_services: string | null;
          applied_at: string | null;
          audience_insights: string | null;
          available_start_date: string | null;
          campaign_id: string;
          delivery_timeframe: string | null;
          experience_relevant: string | null;
          id: string;
          influencer_id: string;
          portfolio_links: string[] | null;
          proposal_text: string | null;
          proposed_rate: number;
          responded_at: string | null;
          status: Database['public']['Enums']['application_status'] | null;
        };
        Insert: {
          additional_services?: string | null;
          applied_at?: string | null;
          audience_insights?: string | null;
          available_start_date?: string | null;
          campaign_id: string;
          delivery_timeframe?: string | null;
          experience_relevant?: string | null;
          id?: string;
          influencer_id: string;
          portfolio_links?: string[] | null;
          proposal_text?: string | null;
          proposed_rate: number;
          responded_at?: string | null;
          status?: Database['public']['Enums']['application_status'] | null;
        };
        Update: {
          additional_services?: string | null;
          applied_at?: string | null;
          audience_insights?: string | null;
          available_start_date?: string | null;
          campaign_id?: string;
          delivery_timeframe?: string | null;
          experience_relevant?: string | null;
          id?: string;
          influencer_id?: string;
          portfolio_links?: string[] | null;
          proposal_text?: string | null;
          proposed_rate?: number;
          responded_at?: string | null;
          status?: Database['public']['Enums']['application_status'] | null;
        };
        Relationships: [
          {
            foreignKeyName: 'campaign_applications_campaign_id_fkey';
            columns: ['campaign_id'];
            isOneToOne: false;
            referencedRelation: 'campaigns';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'campaign_applications_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencer_search_view';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'campaign_applications_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencers';
            referencedColumns: ['id'];
          },
        ];
      };
      campaign_categories: {
        Row: {
          campaign_id: string;
          category_id: number;
          created_at: string | null;
          id: number;
        };
        Insert: {
          campaign_id: string;
          category_id: number;
          created_at?: string | null;
          id?: number;
        };
        Update: {
          campaign_id?: string;
          category_id?: number;
          created_at?: string | null;
          id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'campaign_categories_campaign_id_fkey';
            columns: ['campaign_id'];
            isOneToOne: false;
            referencedRelation: 'campaigns';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'campaign_categories_category_id_fkey';
            columns: ['category_id'];
            isOneToOne: false;
            referencedRelation: 'categories';
            referencedColumns: ['id'];
          },
        ];
      };
      campaign_platforms: {
        Row: {
          campaign_id: string;
          content_types: string[];
          created_at: string | null;
          id: string;
          platform_id: number;
        };
        Insert: {
          campaign_id: string;
          content_types: string[];
          created_at?: string | null;
          id?: string;
          platform_id: number;
        };
        Update: {
          campaign_id?: string;
          content_types?: string[];
          created_at?: string | null;
          id?: string;
          platform_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'campaign_platforms_campaign_id_fkey';
            columns: ['campaign_id'];
            isOneToOne: false;
            referencedRelation: 'campaigns';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'campaign_platforms_platform_id_fkey';
            columns: ['platform_id'];
            isOneToOne: false;
            referencedRelation: 'platforms';
            referencedColumns: ['id'];
          },
        ];
      };
      campaigns: {
        Row: {
          activated_at: string | null;
          additional_notes: string | null;
          age_range_max: number | null;
          age_range_min: number | null;
          application_deadline: string | null;
          applications_count: number | null;
          budget: number | null;
          business_id: string;
          campaign_goal: string | null;
          collaboration_type: string | null;
          contact_email: string | null;
          contact_phone: string | null;
          content_types: Database['public']['Enums']['content_type'][];
          created_at: string | null;
          deliverables: string | null;
          description: string;
          do_not_mention: string[] | null;
          end_date: string | null;
          exclusivity_period: number | null;
          featured_until: string | null;
          gender: string | null;
          hashtags: string[] | null;
          id: string;
          is_featured: boolean | null;
          is_marketplace_active: boolean | null;
          location: string | null;
          max_followers: number | null;
          min_followers: number | null;
          payment_terms: string | null;
          product_description: string | null;
          requirements: string | null;
          revisions_included: number | null;
          show_business_name: boolean | null;
          start_date: string | null;
          status: Database['public']['Enums']['campaign_status'] | null;
          target_audience: Json | null;
          title: string;
          updated_at: string | null;
          usage_rights: string | null;
          views_count: number | null;
        };
        Insert: {
          activated_at?: string | null;
          additional_notes?: string | null;
          age_range_max?: number | null;
          age_range_min?: number | null;
          application_deadline?: string | null;
          applications_count?: number | null;
          budget?: number | null;
          business_id: string;
          campaign_goal?: string | null;
          collaboration_type?: string | null;
          contact_email?: string | null;
          contact_phone?: string | null;
          content_types: Database['public']['Enums']['content_type'][];
          created_at?: string | null;
          deliverables?: string | null;
          description: string;
          do_not_mention?: string[] | null;
          end_date?: string | null;
          exclusivity_period?: number | null;
          featured_until?: string | null;
          gender?: string | null;
          hashtags?: string[] | null;
          id?: string;
          is_featured?: boolean | null;
          is_marketplace_active?: boolean | null;
          location?: string | null;
          max_followers?: number | null;
          min_followers?: number | null;
          payment_terms?: string | null;
          product_description?: string | null;
          requirements?: string | null;
          revisions_included?: number | null;
          show_business_name?: boolean | null;
          start_date?: string | null;
          status?: Database['public']['Enums']['campaign_status'] | null;
          target_audience?: Json | null;
          title: string;
          updated_at?: string | null;
          usage_rights?: string | null;
          views_count?: number | null;
        };
        Update: {
          activated_at?: string | null;
          additional_notes?: string | null;
          age_range_max?: number | null;
          age_range_min?: number | null;
          application_deadline?: string | null;
          applications_count?: number | null;
          budget?: number | null;
          business_id?: string;
          campaign_goal?: string | null;
          collaboration_type?: string | null;
          contact_email?: string | null;
          contact_phone?: string | null;
          content_types?: Database['public']['Enums']['content_type'][];
          created_at?: string | null;
          deliverables?: string | null;
          description?: string;
          do_not_mention?: string[] | null;
          end_date?: string | null;
          exclusivity_period?: number | null;
          featured_until?: string | null;
          gender?: string | null;
          hashtags?: string[] | null;
          id?: string;
          is_featured?: boolean | null;
          is_marketplace_active?: boolean | null;
          location?: string | null;
          max_followers?: number | null;
          min_followers?: number | null;
          payment_terms?: string | null;
          product_description?: string | null;
          requirements?: string | null;
          revisions_included?: number | null;
          show_business_name?: boolean | null;
          start_date?: string | null;
          status?: Database['public']['Enums']['campaign_status'] | null;
          target_audience?: Json | null;
          title?: string;
          updated_at?: string | null;
          usage_rights?: string | null;
          views_count?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'campaigns_business_id_fkey';
            columns: ['business_id'];
            isOneToOne: false;
            referencedRelation: 'businesses';
            referencedColumns: ['id'];
          },
        ];
      };
      categories: {
        Row: {
          created_at: string | null;
          description: string | null;
          icon: string | null;
          id: number;
          name: string;
          slug: string;
        };
        Insert: {
          created_at?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: number;
          name: string;
          slug: string;
        };
        Update: {
          created_at?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: number;
          name?: string;
          slug?: string;
        };
        Relationships: [];
      };
      chat_messages: {
        Row: {
          created_at: string | null;
          edited_at: string | null;
          file_name: string | null;
          file_size: number | null;
          file_type: string | null;
          file_url: string | null;
          id: string;
          message_text: string | null;
          read_at: string | null;
          room_id: string;
          sender_id: string;
          sender_type: string;
        };
        Insert: {
          created_at?: string | null;
          edited_at?: string | null;
          file_name?: string | null;
          file_size?: number | null;
          file_type?: string | null;
          file_url?: string | null;
          id?: string;
          message_text?: string | null;
          read_at?: string | null;
          room_id: string;
          sender_id: string;
          sender_type: string;
        };
        Update: {
          created_at?: string | null;
          edited_at?: string | null;
          file_name?: string | null;
          file_size?: number | null;
          file_type?: string | null;
          file_url?: string | null;
          id?: string;
          message_text?: string | null;
          read_at?: string | null;
          room_id?: string;
          sender_id?: string;
          sender_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_messages_room_id_fkey';
            columns: ['room_id'];
            isOneToOne: false;
            referencedRelation: 'chat_rooms';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_messages_sender_id_fkey';
            columns: ['sender_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_participants: {
        Row: {
          id: string;
          is_active: boolean | null;
          joined_at: string | null;
          last_read_at: string | null;
          room_id: string;
          user_id: string;
          user_type: string;
        };
        Insert: {
          id?: string;
          is_active?: boolean | null;
          joined_at?: string | null;
          last_read_at?: string | null;
          room_id: string;
          user_id: string;
          user_type: string;
        };
        Update: {
          id?: string;
          is_active?: boolean | null;
          joined_at?: string | null;
          last_read_at?: string | null;
          room_id?: string;
          user_id?: string;
          user_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_participants_room_id_fkey';
            columns: ['room_id'];
            isOneToOne: false;
            referencedRelation: 'chat_rooms';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_participants_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_permissions: {
        Row: {
          business_approved: boolean | null;
          business_id: string;
          campaign_application_id: string | null;
          chat_enabled: boolean | null;
          created_at: string | null;
          id: string;
          influencer_approved: boolean | null;
          influencer_id: string;
          offer_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          business_approved?: boolean | null;
          business_id: string;
          campaign_application_id?: string | null;
          chat_enabled?: boolean | null;
          created_at?: string | null;
          id?: string;
          influencer_approved?: boolean | null;
          influencer_id: string;
          offer_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          business_approved?: boolean | null;
          business_id?: string;
          campaign_application_id?: string | null;
          chat_enabled?: boolean | null;
          created_at?: string | null;
          id?: string;
          influencer_approved?: boolean | null;
          influencer_id?: string;
          offer_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_permissions_business_id_fkey';
            columns: ['business_id'];
            isOneToOne: false;
            referencedRelation: 'businesses';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_permissions_campaign_application_id_fkey';
            columns: ['campaign_application_id'];
            isOneToOne: false;
            referencedRelation: 'campaign_applications';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_permissions_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencer_search_view';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_permissions_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_permissions_offer_id_fkey';
            columns: ['offer_id'];
            isOneToOne: false;
            referencedRelation: 'direct_offers';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_rooms: {
        Row: {
          business_id: string;
          campaign_application_id: string | null;
          created_at: string | null;
          id: string;
          influencer_id: string;
          last_message_at: string | null;
          offer_id: string | null;
          room_title: string;
          room_type: string;
          updated_at: string | null;
        };
        Insert: {
          business_id: string;
          campaign_application_id?: string | null;
          created_at?: string | null;
          id?: string;
          influencer_id: string;
          last_message_at?: string | null;
          offer_id?: string | null;
          room_title: string;
          room_type: string;
          updated_at?: string | null;
        };
        Update: {
          business_id?: string;
          campaign_application_id?: string | null;
          created_at?: string | null;
          id?: string;
          influencer_id?: string;
          last_message_at?: string | null;
          offer_id?: string | null;
          room_title?: string;
          room_type?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_rooms_business_id_fkey';
            columns: ['business_id'];
            isOneToOne: false;
            referencedRelation: 'businesses';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_rooms_campaign_application_id_fkey';
            columns: ['campaign_application_id'];
            isOneToOne: false;
            referencedRelation: 'campaign_applications';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_rooms_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencer_search_view';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_rooms_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_rooms_offer_id_fkey';
            columns: ['offer_id'];
            isOneToOne: false;
            referencedRelation: 'direct_offers';
            referencedColumns: ['id'];
          },
        ];
      };
      collaborations: {
        Row: {
          agreed_deliverables: string | null;
          agreed_price: number;
          application_id: string;
          business_approved: boolean | null;
          completed_at: string | null;
          completion_proof: string | null;
          created_at: string | null;
          deadline: string | null;
          id: string;
          is_completed: boolean | null;
          is_paid: boolean | null;
        };
        Insert: {
          agreed_deliverables?: string | null;
          agreed_price: number;
          application_id: string;
          business_approved?: boolean | null;
          completed_at?: string | null;
          completion_proof?: string | null;
          created_at?: string | null;
          deadline?: string | null;
          id?: string;
          is_completed?: boolean | null;
          is_paid?: boolean | null;
        };
        Update: {
          agreed_deliverables?: string | null;
          agreed_price?: number;
          application_id?: string;
          business_approved?: boolean | null;
          completed_at?: string | null;
          completion_proof?: string | null;
          created_at?: string | null;
          deadline?: string | null;
          id?: string;
          is_completed?: boolean | null;
          is_paid?: boolean | null;
        };
        Relationships: [
          {
            foreignKeyName: 'collaborations_application_id_fkey';
            columns: ['application_id'];
            isOneToOne: false;
            referencedRelation: 'campaign_applications';
            referencedColumns: ['id'];
          },
        ];
      };
      content_types: {
        Row: {
          created_at: string | null;
          description: string | null;
          id: number;
          is_active: boolean | null;
          name: string;
          platform_id: number | null;
          slug: string;
        };
        Insert: {
          created_at?: string | null;
          description?: string | null;
          id?: number;
          is_active?: boolean | null;
          name: string;
          platform_id?: number | null;
          slug: string;
        };
        Update: {
          created_at?: string | null;
          description?: string | null;
          id?: number;
          is_active?: boolean | null;
          name?: string;
          platform_id?: number | null;
          slug?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'content_types_platform_id_fkey';
            columns: ['platform_id'];
            isOneToOne: false;
            referencedRelation: 'platforms';
            referencedColumns: ['id'];
          },
        ];
      };
      direct_offers: {
        Row: {
          accepted_at: string | null;
          budget: number;
          business_id: string;
          business_message: string | null;
          content_types: string[];
          created_at: string | null;
          deadline: string | null;
          deliverables: string | null;
          description: string;
          id: string;
          influencer_id: string;
          influencer_response: string | null;
          offer_type: string | null;
          package_id: number | null;
          platforms: string[];
          rejected_at: string | null;
          requirements: string | null;
          responded_at: string | null;
          status: string | null;
          title: string;
          updated_at: string | null;
        };
        Insert: {
          accepted_at?: string | null;
          budget: number;
          business_id: string;
          business_message?: string | null;
          content_types: string[];
          created_at?: string | null;
          deadline?: string | null;
          deliverables?: string | null;
          description: string;
          id?: string;
          influencer_id: string;
          influencer_response?: string | null;
          offer_type?: string | null;
          package_id?: number | null;
          platforms: string[];
          rejected_at?: string | null;
          requirements?: string | null;
          responded_at?: string | null;
          status?: string | null;
          title: string;
          updated_at?: string | null;
        };
        Update: {
          accepted_at?: string | null;
          budget?: number;
          business_id?: string;
          business_message?: string | null;
          content_types?: string[];
          created_at?: string | null;
          deadline?: string | null;
          deliverables?: string | null;
          description?: string;
          id?: string;
          influencer_id?: string;
          influencer_response?: string | null;
          offer_type?: string | null;
          package_id?: number | null;
          platforms?: string[];
          rejected_at?: string | null;
          requirements?: string | null;
          responded_at?: string | null;
          status?: string | null;
          title?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'direct_offers_business_id_fkey';
            columns: ['business_id'];
            isOneToOne: false;
            referencedRelation: 'businesses';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'direct_offers_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencer_search_view';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'direct_offers_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'direct_offers_package_id_fkey';
            columns: ['package_id'];
            isOneToOne: false;
            referencedRelation: 'influencer_platform_pricing';
            referencedColumns: ['id'];
          },
        ];
      };
      featured_campaign_promotions: {
        Row: {
          business_id: string;
          campaign_id: string;
          created_at: string | null;
          duration_days: number;
          ends_at: string;
          id: string;
          payment_status: string;
          price_eur: number;
          starts_at: string;
          stripe_payment_intent_id: string | null;
          stripe_session_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          business_id: string;
          campaign_id: string;
          created_at?: string | null;
          duration_days: number;
          ends_at: string;
          id?: string;
          payment_status?: string;
          price_eur: number;
          starts_at: string;
          stripe_payment_intent_id?: string | null;
          stripe_session_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          business_id?: string;
          campaign_id?: string;
          created_at?: string | null;
          duration_days?: number;
          ends_at?: string;
          id?: string;
          payment_status?: string;
          price_eur?: number;
          starts_at?: string;
          stripe_payment_intent_id?: string | null;
          stripe_session_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'featured_campaign_promotions_business_id_fkey';
            columns: ['business_id'];
            isOneToOne: false;
            referencedRelation: 'businesses';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'featured_campaign_promotions_campaign_id_fkey';
            columns: ['campaign_id'];
            isOneToOne: false;
            referencedRelation: 'campaigns';
            referencedColumns: ['id'];
          },
        ];
      };
      influencer_categories: {
        Row: {
          category_id: number;
          created_at: string | null;
          influencer_id: string;
          is_primary: boolean | null;
        };
        Insert: {
          category_id: number;
          created_at?: string | null;
          influencer_id: string;
          is_primary?: boolean | null;
        };
        Update: {
          category_id?: number;
          created_at?: string | null;
          influencer_id?: string;
          is_primary?: boolean | null;
        };
        Relationships: [
          {
            foreignKeyName: 'influencer_categories_category_id_fkey';
            columns: ['category_id'];
            isOneToOne: false;
            referencedRelation: 'categories';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'influencer_categories_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencer_search_view';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'influencer_categories_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencers';
            referencedColumns: ['id'];
          },
        ];
      };
      influencer_platform_pricing: {
        Row: {
          auto_generated_name: string | null;
          content_type_id: number | null;
          created_at: string | null;
          currency: string | null;
          id: number;
          influencer_id: string | null;
          is_available: boolean | null;
          platform_id: number | null;
          price: number;
          quantity: number | null;
          updated_at: string | null;
          video_duration: string | null;
        };
        Insert: {
          auto_generated_name?: string | null;
          content_type_id?: number | null;
          created_at?: string | null;
          currency?: string | null;
          id?: number;
          influencer_id?: string | null;
          is_available?: boolean | null;
          platform_id?: number | null;
          price: number;
          quantity?: number | null;
          updated_at?: string | null;
          video_duration?: string | null;
        };
        Update: {
          auto_generated_name?: string | null;
          content_type_id?: number | null;
          created_at?: string | null;
          currency?: string | null;
          id?: number;
          influencer_id?: string | null;
          is_available?: boolean | null;
          platform_id?: number | null;
          price?: number;
          quantity?: number | null;
          updated_at?: string | null;
          video_duration?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'influencer_platform_pricing_content_type_id_fkey';
            columns: ['content_type_id'];
            isOneToOne: false;
            referencedRelation: 'content_types';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'influencer_platform_pricing_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencer_search_view';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'influencer_platform_pricing_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'influencer_platform_pricing_platform_id_fkey';
            columns: ['platform_id'];
            isOneToOne: false;
            referencedRelation: 'platforms';
            referencedColumns: ['id'];
          },
        ];
      };
      influencer_platforms: {
        Row: {
          created_at: string | null;
          followers_count: number | null;
          handle: string | null;
          influencer_id: string;
          is_active: boolean | null;
          is_verified: boolean | null;
          platform_id: number;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          followers_count?: number | null;
          handle?: string | null;
          influencer_id: string;
          is_active?: boolean | null;
          is_verified?: boolean | null;
          platform_id: number;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          followers_count?: number | null;
          handle?: string | null;
          influencer_id?: string;
          is_active?: boolean | null;
          is_verified?: boolean | null;
          platform_id?: number;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'influencer_platforms_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencer_search_view';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'influencer_platforms_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'influencers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'influencer_platforms_platform_id_fkey';
            columns: ['platform_id'];
            isOneToOne: false;
            referencedRelation: 'platforms';
            referencedColumns: ['id'];
          },
        ];
      };
      influencers: {
        Row: {
          created_at: string | null;
          custom_offers_enabled: boolean | null;
          engagement_rate: number | null;
          id: string;
          is_verified: boolean | null;
          portfolio_urls: string[] | null;
          subscription_type: string | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          custom_offers_enabled?: boolean | null;
          engagement_rate?: number | null;
          id: string;
          is_verified?: boolean | null;
          portfolio_urls?: string[] | null;
          subscription_type?: string | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          custom_offers_enabled?: boolean | null;
          engagement_rate?: number | null;
          id?: string;
          is_verified?: boolean | null;
          portfolio_urls?: string[] | null;
          subscription_type?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'influencers_id_fkey';
            columns: ['id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      job_completions: {
        Row: {
          approved_by: string | null;
          business_id: string | null;
          business_notes: string | null;
          campaign_application_id: string | null;
          created_at: string | null;
          direct_offer_id: string | null;
          id: string;
          influencer_id: string | null;
          review_notes: string | null;
          reviewed_at: string | null;
          status: string | null;
          submission_files: Json | null;
          submission_notes: string | null;
          submitted_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          approved_by?: string | null;
          business_id?: string | null;
          business_notes?: string | null;
          campaign_application_id?: string | null;
          created_at?: string | null;
          direct_offer_id?: string | null;
          id?: string;
          influencer_id?: string | null;
          review_notes?: string | null;
          reviewed_at?: string | null;
          status?: string | null;
          submission_files?: Json | null;
          submission_notes?: string | null;
          submitted_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          approved_by?: string | null;
          business_id?: string | null;
          business_notes?: string | null;
          campaign_application_id?: string | null;
          created_at?: string | null;
          direct_offer_id?: string | null;
          id?: string;
          influencer_id?: string | null;
          review_notes?: string | null;
          reviewed_at?: string | null;
          status?: string | null;
          submission_files?: Json | null;
          submission_notes?: string | null;
          submitted_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'job_completions_approved_by_fkey';
            columns: ['approved_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_completions_business_id_fkey';
            columns: ['business_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_completions_campaign_application_id_fkey';
            columns: ['campaign_application_id'];
            isOneToOne: false;
            referencedRelation: 'campaign_applications';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_completions_direct_offer_id_fkey';
            columns: ['direct_offer_id'];
            isOneToOne: false;
            referencedRelation: 'direct_offers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_completions_influencer_id_fkey';
            columns: ['influencer_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      job_reviews: {
        Row: {
          comment: string | null;
          created_at: string | null;
          id: string;
          job_completion_id: string | null;
          rating: number | null;
          review_type: string | null;
          reviewee_id: string | null;
          reviewer_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          comment?: string | null;
          created_at?: string | null;
          id?: string;
          job_completion_id?: string | null;
          rating?: number | null;
          review_type?: string | null;
          reviewee_id?: string | null;
          reviewer_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          comment?: string | null;
          created_at?: string | null;
          id?: string;
          job_completion_id?: string | null;
          rating?: number | null;
          review_type?: string | null;
          reviewee_id?: string | null;
          reviewer_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'job_reviews_job_completion_id_fkey';
            columns: ['job_completion_id'];
            isOneToOne: false;
            referencedRelation: 'job_completions';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_reviews_reviewee_id_fkey';
            columns: ['reviewee_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'job_reviews_reviewer_id_fkey';
            columns: ['reviewer_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      notifications: {
        Row: {
          created_at: string | null;
          data: Json | null;
          id: string;
          message: string;
          read: boolean | null;
          title: string;
          type: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          data?: Json | null;
          id?: string;
          message: string;
          read?: boolean | null;
          title: string;
          type: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          data?: Json | null;
          id?: string;
          message?: string;
          read?: boolean | null;
          title?: string;
          type?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      payments: {
        Row: {
          campaign_application_id: string | null;
          campaign_id: string | null;
          created_at: string | null;
          currency: string;
          direct_offer_id: string | null;
          featured_until: string | null;
          id: string;
          payment_amount: number;
          payment_completed_at: string | null;
          payment_status: string;
          payment_type: string;
          platform_fee: number;
          stripe_payment_intent_id: string | null;
          stripe_session_id: string | null;
          subscription_duration_months: number | null;
          subscription_end_date: string | null;
          subscription_plan: string | null;
          subscription_start_date: string | null;
          subscription_type: string | null;
          total_paid: number;
          updated_at: string | null;
          user_id: string | null;
          user_type: string | null;
        };
        Insert: {
          campaign_application_id?: string | null;
          campaign_id?: string | null;
          created_at?: string | null;
          currency?: string;
          direct_offer_id?: string | null;
          featured_until?: string | null;
          id?: string;
          payment_amount: number;
          payment_completed_at?: string | null;
          payment_status?: string;
          payment_type: string;
          platform_fee: number;
          stripe_payment_intent_id?: string | null;
          stripe_session_id?: string | null;
          subscription_duration_months?: number | null;
          subscription_end_date?: string | null;
          subscription_plan?: string | null;
          subscription_start_date?: string | null;
          subscription_type?: string | null;
          total_paid: number;
          updated_at?: string | null;
          user_id?: string | null;
          user_type?: string | null;
        };
        Update: {
          campaign_application_id?: string | null;
          campaign_id?: string | null;
          created_at?: string | null;
          currency?: string;
          direct_offer_id?: string | null;
          featured_until?: string | null;
          id?: string;
          payment_amount?: number;
          payment_completed_at?: string | null;
          payment_status?: string;
          payment_type?: string;
          platform_fee?: number;
          stripe_payment_intent_id?: string | null;
          stripe_session_id?: string | null;
          subscription_duration_months?: number | null;
          subscription_end_date?: string | null;
          subscription_plan?: string | null;
          subscription_start_date?: string | null;
          subscription_type?: string | null;
          total_paid?: number;
          updated_at?: string | null;
          user_id?: string | null;
          user_type?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'payments_campaign_application_id_fkey';
            columns: ['campaign_application_id'];
            isOneToOne: false;
            referencedRelation: 'campaign_applications';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'payments_campaign_id_fkey';
            columns: ['campaign_id'];
            isOneToOne: false;
            referencedRelation: 'campaigns';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'payments_direct_offer_id_fkey';
            columns: ['direct_offer_id'];
            isOneToOne: false;
            referencedRelation: 'direct_offers';
            referencedColumns: ['id'];
          },
        ];
      };
      platforms: {
        Row: {
          created_at: string | null;
          icon: string | null;
          id: number;
          is_active: boolean | null;
          name: string;
          slug: string;
        };
        Insert: {
          created_at?: string | null;
          icon?: string | null;
          id?: number;
          is_active?: boolean | null;
          name: string;
          slug: string;
        };
        Update: {
          created_at?: string | null;
          icon?: string | null;
          id?: number;
          is_active?: boolean | null;
          name?: string;
          slug?: string;
        };
        Relationships: [];
      };
      profiles: {
        Row: {
          address: string | null;
          age: number | null;
          avatar_url: string | null;
          average_rating: number | null;
          bank_account: string | null;
          bank_name: string | null;
          bio: string | null;
          card_avatar_url: string | null;
          city: string | null;
          country: string | null;
          created_at: string | null;
          full_name: string | null;
          gender: string | null;
          id: string;
          navbar_avatar_url: string | null;
          onboarding_completed: boolean | null;
          onboarding_completed_at: string | null;
          onboarding_step: number | null;
          phone: string | null;
          postal_code: string | null;
          preview_avatar_url: string | null;
          profile_avatar_url: string | null;
          profile_completed: boolean | null;
          public_display_name: string | null;
          tax_id: string | null;
          total_reviews: number | null;
          updated_at: string | null;
          user_type: Database['public']['Enums']['user_type'];
          username: string | null;
          website_url: string | null;
        };
        Insert: {
          address?: string | null;
          age?: number | null;
          avatar_url?: string | null;
          average_rating?: number | null;
          bank_account?: string | null;
          bank_name?: string | null;
          bio?: string | null;
          card_avatar_url?: string | null;
          city?: string | null;
          country?: string | null;
          created_at?: string | null;
          full_name?: string | null;
          gender?: string | null;
          id: string;
          navbar_avatar_url?: string | null;
          onboarding_completed?: boolean | null;
          onboarding_completed_at?: string | null;
          onboarding_step?: number | null;
          phone?: string | null;
          postal_code?: string | null;
          preview_avatar_url?: string | null;
          profile_avatar_url?: string | null;
          profile_completed?: boolean | null;
          public_display_name?: string | null;
          tax_id?: string | null;
          total_reviews?: number | null;
          updated_at?: string | null;
          user_type: Database['public']['Enums']['user_type'];
          username?: string | null;
          website_url?: string | null;
        };
        Update: {
          address?: string | null;
          age?: number | null;
          avatar_url?: string | null;
          average_rating?: number | null;
          bank_account?: string | null;
          bank_name?: string | null;
          bio?: string | null;
          card_avatar_url?: string | null;
          city?: string | null;
          country?: string | null;
          created_at?: string | null;
          full_name?: string | null;
          gender?: string | null;
          id?: string;
          navbar_avatar_url?: string | null;
          onboarding_completed?: boolean | null;
          onboarding_completed_at?: string | null;
          onboarding_step?: number | null;
          phone?: string | null;
          postal_code?: string | null;
          preview_avatar_url?: string | null;
          profile_avatar_url?: string | null;
          profile_completed?: boolean | null;
          public_display_name?: string | null;
          tax_id?: string | null;
          total_reviews?: number | null;
          updated_at?: string | null;
          user_type?: Database['public']['Enums']['user_type'];
          username?: string | null;
          website_url?: string | null;
        };
        Relationships: [];
      };
      reviews: {
        Row: {
          collaboration_id: string;
          comment: string | null;
          created_at: string | null;
          id: string;
          rating: number | null;
          reviewee_id: string;
          reviewer_id: string;
        };
        Insert: {
          collaboration_id: string;
          comment?: string | null;
          created_at?: string | null;
          id?: string;
          rating?: number | null;
          reviewee_id: string;
          reviewer_id: string;
        };
        Update: {
          collaboration_id?: string;
          comment?: string | null;
          created_at?: string | null;
          id?: string;
          rating?: number | null;
          reviewee_id?: string;
          reviewer_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'reviews_collaboration_id_fkey';
            columns: ['collaboration_id'];
            isOneToOne: false;
            referencedRelation: 'collaborations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'reviews_reviewee_id_fkey';
            columns: ['reviewee_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'reviews_reviewer_id_fkey';
            columns: ['reviewer_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      security_audit_logs: {
        Row: {
          action: string;
          created_at: string | null;
          details: Json | null;
          id: string;
          ip_address: string | null;
          resource_id: string;
          timestamp: string;
          user_agent: string | null;
          user_id: string;
        };
        Insert: {
          action: string;
          created_at?: string | null;
          details?: Json | null;
          id?: string;
          ip_address?: string | null;
          resource_id: string;
          timestamp?: string;
          user_agent?: string | null;
          user_id: string;
        };
        Update: {
          action?: string;
          created_at?: string | null;
          details?: Json | null;
          id?: string;
          ip_address?: string | null;
          resource_id?: string;
          timestamp?: string;
          user_agent?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      subscription_plans: {
        Row: {
          created_at: string | null;
          duration_months: number;
          features: Json | null;
          id: string;
          is_active: boolean | null;
          plan_name: string;
          price: number;
          stripe_price_id: string | null;
          updated_at: string | null;
          user_type: string;
        };
        Insert: {
          created_at?: string | null;
          duration_months: number;
          features?: Json | null;
          id?: string;
          is_active?: boolean | null;
          plan_name: string;
          price: number;
          stripe_price_id?: string | null;
          updated_at?: string | null;
          user_type: string;
        };
        Update: {
          created_at?: string | null;
          duration_months?: number;
          features?: Json | null;
          id?: string;
          is_active?: boolean | null;
          plan_name?: string;
          price?: number;
          stripe_price_id?: string | null;
          updated_at?: string | null;
          user_type?: string;
        };
        Relationships: [];
      };
      user_subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null;
          created_at: string | null;
          current_period_end: string;
          current_period_start: string;
          id: string;
          status: string;
          stripe_subscription_id: string | null;
          subscription_plan_id: string;
          updated_at: string | null;
          user_id: string;
          user_type: string;
        };
        Insert: {
          cancel_at_period_end?: boolean | null;
          created_at?: string | null;
          current_period_end: string;
          current_period_start: string;
          id?: string;
          status?: string;
          stripe_subscription_id?: string | null;
          subscription_plan_id: string;
          updated_at?: string | null;
          user_id: string;
          user_type: string;
        };
        Update: {
          cancel_at_period_end?: boolean | null;
          created_at?: string | null;
          current_period_end?: string;
          current_period_start?: string;
          id?: string;
          status?: string;
          stripe_subscription_id?: string | null;
          subscription_plan_id?: string;
          updated_at?: string | null;
          user_id?: string;
          user_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'user_subscriptions_subscription_plan_id_fkey';
            columns: ['subscription_plan_id'];
            isOneToOne: false;
            referencedRelation: 'subscription_plans';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      influencer_search_view: {
        Row: {
          age: number | null;
          avatar_url: string | null;
          average_rating: number | null;
          bio: string | null;
          categories: string[] | null;
          created_at: string | null;
          full_name: string | null;
          gender: string | null;
          id: string | null;
          is_verified: boolean | null;
          location: string | null;
          max_price: number | null;
          min_price: number | null;
          platforms: Json | null;
          pricing: Json | null;
          public_display_name: string | null;
          search_vector: unknown | null;
          total_followers: number | null;
          total_reviews: number | null;
          updated_at: string | null;
          username: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'influencers_id_fkey';
            columns: ['id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Functions: {
      check_user_exists: {
        Args: { user_email: string };
        Returns: boolean;
      };
      cleanup_expired_featured_campaigns: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      cleanup_old_read_status: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      convert_km_to_eur: {
        Args: { km_amount: number };
        Returns: number;
      };
      count_business_applications_by_status: {
        Args: { p_business_id: string };
        Returns: {
          accepted_count: number;
          pending_count: number;
          rejected_count: number;
          total_count: number;
        }[];
      };
      count_business_campaigns_by_status: {
        Args: { p_business_id: string };
        Returns: {
          active_count: number;
          cancelled_count: number;
          completed_count: number;
          draft_count: number;
          featured_count: number;
          paused_count: number;
          total_count: number;
        }[];
      };
      count_business_offers_by_status: {
        Args: { p_business_id: string };
        Returns: {
          accepted_count: number;
          cancelled_count: number;
          completed_count: number;
          pending_count: number;
          rejected_count: number;
          total_count: number;
        }[];
      };
      count_campaigns_cards: {
        Args: {
          p_categories?: number[];
          p_featured_only?: boolean;
          p_location?: string;
          p_max_budget?: number;
          p_min_budget?: number;
          p_platforms?: number[];
          p_search_query?: string;
        };
        Returns: number;
      };
      count_influencers_paginated: {
        Args: {
          p_categories?: number[];
          p_gender?: string;
          p_location?: string;
          p_max_age?: number;
          p_max_followers?: number;
          p_max_price?: number;
          p_min_age?: number;
          p_min_followers?: number;
          p_min_price?: number;
          p_platforms?: number[];
          p_search?: string;
        };
        Returns: number;
      };
      create_notification: {
        Args: {
          p_data?: Json;
          p_message: string;
          p_title: string;
          p_type: string;
          p_user_id: string;
        };
        Returns: string;
      };
      create_test_influencers: {
        Args: { end_num: number; start_num: number };
        Returns: string;
      };
      create_test_user: {
        Args:
          | Record<PropertyKey, never>
          | {
              user_email: string;
              user_password: string;
              user_type_param: string;
            };
        Returns: string;
      };
      generate_package_name: {
        Args: {
          p_content_type_name: string;
          p_platform_name: string;
          p_quantity: number;
          p_video_duration?: string;
        };
        Returns: string;
      };
      get_application_details: {
        Args: { p_application_id: string };
        Returns: {
          additional_services: string;
          applied_at: string;
          audience_insights: string;
          available_start_date: string;
          campaign_budget: number;
          campaign_description: string;
          campaign_id: string;
          campaign_location: string;
          campaign_title: string;
          delivery_timeframe: string;
          experience_relevant: string;
          id: string;
          influencer_avatar_url: string;
          influencer_bio: string;
          influencer_display_name: string;
          influencer_full_name: string;
          influencer_id: string;
          influencer_username: string;
          portfolio_links: string[];
          proposal_text: string;
          proposed_rate: number;
          status: Database['public']['Enums']['application_status'];
        }[];
      };
      get_business_applications_cards: {
        Args: {
          p_business_id: string;
          p_limit?: number;
          p_offset?: number;
          p_status?: Database['public']['Enums']['application_status'];
        };
        Returns: {
          applied_at: string;
          campaign_budget: number;
          campaign_id: string;
          campaign_title: string;
          delivery_timeframe: string;
          id: string;
          influencer_avatar_url: string;
          influencer_display_name: string;
          influencer_id: string;
          influencer_username: string;
          proposal_text: string;
          proposed_rate: number;
          status: Database['public']['Enums']['application_status'];
        }[];
      };
      get_business_campaigns_dashboard: {
        Args: {
          p_business_id: string;
          p_limit?: number;
          p_offset?: number;
          p_status?: string;
        };
        Returns: {
          application_count: number;
          budget: number;
          content_types: Database['public']['Enums']['content_type'][];
          created_at: string;
          description: string;
          id: string;
          is_featured: boolean;
          platforms: Json;
          status: Database['public']['Enums']['campaign_status'];
          title: string;
        }[];
      };
      get_business_campaigns_dashboard_table: {
        Args: {
          p_business_id: string;
          p_limit?: number;
          p_offset?: number;
          p_status?: string;
        };
        Returns: {
          application_count: number;
          budget: number;
          business_id: string;
          created_at: string;
          description: string;
          id: string;
          is_featured: boolean;
          status: Database['public']['Enums']['campaign_status'];
          title: string;
        }[];
      };
      get_business_offers_cards: {
        Args: {
          p_business_id: string;
          p_limit?: number;
          p_offset?: number;
          p_status?: string;
        };
        Returns: {
          budget: number;
          content_types: string[];
          created_at: string;
          deadline: string;
          description: string;
          id: string;
          influencer_avatar_url: string;
          influencer_display_name: string;
          influencer_id: string;
          influencer_username: string;
          offer_type: string;
          platforms: string[];
          status: string;
          title: string;
        }[];
      };
      get_campaign_details: {
        Args: { p_campaign_id: string };
        Returns: {
          additional_notes: string;
          age_range_max: number;
          age_range_min: number;
          application_deadline: string;
          applications_count: number;
          budget: number;
          business_avatar: string;
          business_id: string;
          business_industry: string;
          business_name: string;
          business_username: string;
          categories: Json;
          collaboration_type: string;
          content_types: string[];
          created_at: string;
          description: string;
          do_not_mention: string[];
          gender: string;
          hashtags: string[];
          id: string;
          is_featured: boolean;
          location: string;
          max_followers: number;
          min_followers: number;
          platforms: Json;
          status: string;
          title: string;
        }[];
      };
      get_campaigns_cards: {
        Args: {
          p_categories?: number[];
          p_featured_only?: boolean;
          p_limit?: number;
          p_location?: string;
          p_max_budget?: number;
          p_min_budget?: number;
          p_offset?: number;
          p_platforms?: number[];
          p_search_query?: string;
          p_sort_by?: string;
          p_sort_order?: string;
        };
        Returns: {
          applications_count: number;
          budget: number;
          business_name: string;
          created_at: string;
          description: string;
          id: string;
          is_featured: boolean;
          platforms: Json;
          title: string;
        }[];
      };
      get_campaigns_paginated: {
        Args: {
          p_categories?: number[];
          p_deadline_before?: string;
          p_featured_only?: boolean;
          p_gender?: string;
          p_limit?: number;
          p_location?: string;
          p_max_budget?: number;
          p_max_followers?: number;
          p_min_budget?: number;
          p_min_followers?: number;
          p_offset?: number;
          p_platforms?: number[];
          p_search_query?: string;
          p_sort_by?: string;
          p_sort_order?: string;
        };
        Returns: {
          age_range_max: number;
          age_range_min: number;
          application_deadline: string;
          applications_count: number;
          budget: number;
          business_avatar: string;
          business_id: string;
          business_industry: string;
          business_name: string;
          business_username: string;
          categories: Json;
          content_types: string;
          created_at: string;
          description: string;
          gender: string;
          id: string;
          is_featured: boolean;
          location: string;
          max_followers: number;
          min_followers: number;
          platforms: Json;
          status: string;
          title: string;
        }[];
      };
      get_company_name_from_metadata: {
        Args: { user_id: string };
        Returns: string;
      };
      get_display_name: {
        Args: {
          full_name_param: string;
          public_display_name_param: string;
          username_param: string;
        };
        Returns: string;
      };
      get_influencer_applications: {
        Args: {
          p_influencer_id: string;
          p_limit?: number;
          p_offset?: number;
          p_status?: string;
        };
        Returns: {
          applied_at: string;
          audience_insights: string;
          business_username: string;
          campaign_budget: number;
          campaign_business_id: string;
          campaign_id: string;
          campaign_title: string;
          delivery_timeframe: string;
          experience_relevant: string;
          id: string;
          portfolio_links: string[];
          proposal_text: string;
          proposed_rate: string;
          status: string;
        }[];
      };
      get_influencer_offers: {
        Args: {
          p_influencer_id: string;
          p_limit?: number;
          p_offset?: number;
          p_status?: string;
        };
        Returns: {
          accepted_at: string;
          budget: number;
          business_avatar_url: string;
          business_company_name: string;
          business_id: string;
          business_industry: string;
          business_message: string;
          business_username: string;
          content_types: string[];
          created_at: string;
          deadline: string;
          description: string;
          id: string;
          influencer_id: string;
          influencer_response: string;
          offer_type: string;
          package_id: number;
          platforms: string[];
          rejected_at: string;
          requirements: string;
          responded_at: string;
          status: string;
          title: string;
          updated_at: string;
        }[];
      };
      get_influencer_search_results: {
        Args: {
          category_filter?: string;
          limit_count?: number;
          location_filter?: string;
          max_followers?: number;
          max_price_filter?: number;
          min_followers?: number;
          min_price_filter?: number;
          search_term?: string;
        };
        Returns: {
          age: number;
          avatar_url: string;
          average_rating: number;
          bio: string;
          categories: string[];
          created_at: string;
          full_name: string;
          gender: string;
          id: string;
          is_verified: boolean;
          location: string;
          max_price: number;
          min_price: number;
          platforms: Json;
          pricing: Json;
          total_followers: number;
          total_reviews: number;
          username: string;
        }[];
      };
      get_influencers_paginated: {
        Args: {
          p_categories?: number[];
          p_gender?: string;
          p_limit?: number;
          p_location?: string;
          p_max_age?: number;
          p_max_followers?: number;
          p_max_price?: number;
          p_min_age?: number;
          p_min_followers?: number;
          p_min_price?: number;
          p_offset?: number;
          p_platforms?: number[];
          p_search?: string;
          p_sort_by?: string;
          p_sort_order?: string;
        };
        Returns: {
          age: number;
          avatar_url: string;
          avg_rating: number;
          bio: string;
          created_at: string;
          full_name: string;
          gender: string;
          id: string;
          location: string;
          platforms: Json;
          subscription_type: string;
          total_followers: number;
          total_reviews: number;
          username: string;
        }[];
      };
      get_influencers_with_details: {
        Args: {
          category_filter?: string;
          limit_count?: number;
          location_filter?: string;
          max_followers?: number;
          max_price?: number;
          min_followers?: number;
          min_price?: number;
          platform_filter?: string;
          search_term?: string;
        };
        Returns: {
          age: number;
          avatar_url: string;
          average_rating: number;
          bio: string;
          created_at: string;
          full_name: string;
          gender: string;
          id: string;
          instagram_followers: number;
          is_verified: boolean;
          location: string;
          price_per_post: number;
          price_per_reel: number;
          price_per_story: number;
          tiktok_followers: number;
          total_reviews: number;
          username: string;
          youtube_subscribers: number;
        }[];
      };
      get_offer_details: {
        Args: { p_offer_id: string };
        Returns: {
          accepted_at: string;
          budget: number;
          business_id: string;
          business_message: string;
          content_types: string[];
          created_at: string;
          deadline: string;
          deliverables: string;
          description: string;
          id: string;
          influencer_avatar_url: string;
          influencer_bio: string;
          influencer_display_name: string;
          influencer_full_name: string;
          influencer_id: string;
          influencer_response: string;
          offer_type: string;
          package_id: number;
          platforms: string[];
          rejected_at: string;
          requirements: string;
          responded_at: string;
          status: string;
          title: string;
          updated_at: string;
        }[];
      };
      get_public_influencer_profile: {
        Args: { influencer_username: string };
        Returns: {
          age: number;
          avatar_url: string;
          bio: string;
          categories: Json;
          city: string;
          country: string;
          created_at: string;
          full_name: string;
          gender: string;
          id: string;
          is_verified: boolean;
          platforms: Json;
          portfolio_urls: string[];
          pricing: Json;
          total_followers: number;
          username: string;
        }[];
      };
      get_user_email: {
        Args: { user_id: string };
        Returns: string;
      };
      increment_campaign_views: {
        Args: { campaign_id: string };
        Returns: undefined;
      };
      refresh_influencer_search_view: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      rollback_cleanup_migration: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      validate_cleanup_migration: {
        Args: Record<PropertyKey, never>;
        Returns: {
          details: string;
          status: string;
          validation_step: string;
        }[];
      };
    };
    Enums: {
      application_status: 'pending' | 'accepted' | 'rejected' | 'completed';
      campaign_status:
        | 'draft'
        | 'active'
        | 'paused'
        | 'completed'
        | 'cancelled';
      content_type: 'post' | 'story' | 'reel' | 'video' | 'blog';
      user_type: 'influencer' | 'business';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  'public'
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      application_status: ['pending', 'accepted', 'rejected', 'completed'],
      campaign_status: ['draft', 'active', 'paused', 'completed', 'cancelled'],
      content_type: ['post', 'story', 'reel', 'video', 'blog'],
      user_type: ['influencer', 'business'],
    },
  },
} as const;
