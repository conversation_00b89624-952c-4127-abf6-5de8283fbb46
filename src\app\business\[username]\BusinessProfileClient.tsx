'use client';

import { useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
import {
  MapPin,
  Users,
  Building,
  Globe,
  ArrowLeft,
  ExternalLink,
} from 'lucide-react';
import { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import { DesktopHeader } from '@/components/navigation/DesktopHeader';
import { useAuth } from '@/contexts/AuthContext';
import { getOrCreateProfile } from '@/lib/profiles';
import { useBackButton } from '@/hooks/useBackButton';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import type { PublicBusinessProfile } from './BusinessProfileWithAccessControl';

interface BusinessProfileClientProps {
  profile: PublicBusinessProfile;
}

export function BusinessProfileClient({ profile }: BusinessProfileClientProps) {
  const { user } = useAuth();
  const router = useRouter();
  const [influencerProfile, setInfluencerProfile] = useState<{
    id: string;
    username: string | null;
    full_name: string | null;
    user_type: 'influencer' | 'business';
  } | null>(null);
  const { shouldShowBackButton } = useBackButton();

  const loadInfluencerProfile = useCallback(async () => {
    if (!user) return;

    try {
      const { data, error } = await getOrCreateProfile(user.id);
      if (data && !error) {
        setInfluencerProfile(data);
      }
    } catch (error) {
      console.error('Error loading influencer profile:', error);
    }
  }, [user]);

  useEffect(() => {
    if (user && user.user_metadata?.user_type === 'influencer') {
      loadInfluencerProfile();
    }
  }, [user, loadInfluencerProfile]);

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <div className="min-h-screen bg-background pb-16 md:pb-0">
      {/* Desktop Header */}
      <div className="hidden md:block">
        <DesktopHeader userType="influencer" profile={influencerProfile} />
      </div>

      {/* Mobile Navigation */}
      <ResponsiveNavigation
        userType="influencer"
        profile={influencerProfile}
        showBackButton={shouldShowBackButton}
        onBackClick={() => router.back()}
      />

      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto">
          {/* Desktop: Back button */}
          <div className="hidden md:block pt-6 pb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Nazad
            </Button>
          </div>

          {/* Mobile: Stack layout, Desktop: Split layout */}
          <div className="flex flex-col lg:grid lg:grid-cols-5 lg:gap-8 lg:items-start">
            {/* Profile Image - Full width on mobile, left side on desktop */}
            <div className="w-full lg:col-span-2">
              <div className="relative w-full aspect-square lg:w-full lg:max-w-md lg:mx-auto lg:rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={profile.avatar_url || '/placeholder.svg'}
                  alt={
                    profile.brand_name || profile.full_name || profile.username
                  }
                  fill
                  quality={100}
                  className="object-cover"
                  sizes="(max-width: 1024px) 100vw, 400px"
                  priority
                />
              </div>
            </div>

            {/* Profile Info - Below image on mobile, right side on desktop */}
            <div className="px-4 py-6 lg:col-span-3 lg:px-0 lg:py-8">
              <div className="space-y-6 lg:space-y-8">
                {/* Brand Name and Username */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3 flex-wrap">
                    <Building className="h-8 w-8 text-purple-600" />
                    <h1 className="text-2xl lg:text-4xl font-bold text-gray-900">
                      {profile.brand_name ||
                        profile.full_name ||
                        profile.username}
                    </h1>
                  </div>
                  <p className="text-lg lg:text-xl text-muted-foreground">
                    @{profile.username}
                  </p>
                </div>

                {/* Business Type Badge */}
                <div className="flex items-center gap-2">
                  <Badge
                    variant="secondary"
                    className="bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0 px-3 py-1 shadow-lg"
                  >
                    <Building className="w-3 h-3 mr-1 fill-current" />
                    <span className="text-sm">Biznis profil</span>
                  </Badge>
                </div>

                {/* Location and Website */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3 text-sm lg:text-base">
                  {profile.location && (
                    <div className="flex items-center gap-2 bg-gray-50 rounded-lg px-3 py-2">
                      <MapPin className="h-4 w-4 lg:h-5 lg:w-5 text-purple-500 flex-shrink-0" />
                      <span className="text-gray-700">{profile.location}</span>
                    </div>
                  )}
                  {profile.website_url && (
                    <div className="flex items-center gap-2 bg-gray-50 rounded-lg px-3 py-2 sm:col-span-2 lg:col-span-1 xl:col-span-2">
                      <Globe className="h-4 w-4 lg:h-5 lg:w-5 text-blue-500 flex-shrink-0" />
                      <a
                        href={profile.website_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline flex items-center gap-1"
                      >
                        {profile.website_url.replace(/^https?:\/\//, '')}
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Desktop: Two columns, Mobile: Single column */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column: Company Description, Bio, Categories */}
            <div className="space-y-6">
              {/* Company Description */}
              {profile.bio && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50/80 via-purple-50/60 to-blue-100/80 border border-blue-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
                  {/* Business gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-100/20 to-blue-200/40 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-300/20 via-purple-300/10 to-blue-400/20 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Building className="h-5 w-5 text-blue-600" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        O kompaniji
                      </h3>
                    </div>
                    <p className="text-gray-700 leading-relaxed">
                      {profile.bio}
                    </p>
                  </div>
                </div>
              )}

              {/* Business Categories/Industries */}
              {profile.categories && profile.categories.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50/80 via-purple-50/60 to-blue-100/80 border border-blue-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
                  {/* Business gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-100/20 to-blue-200/40 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-300/20 via-purple-300/10 to-blue-400/20 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Users className="h-5 w-5 text-purple-600" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Industrija & Kategorije
                      </h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {profile.categories.map((category, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-blue-200 text-sm"
                        >
                          {category.icon && (
                            <span className="mr-1">{category.icon}</span>
                          )}
                          {category.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column: Social Media Platforms */}
            <div className="space-y-6">
              {/* Social Media Platforms */}
              {profile.platforms && profile.platforms.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50/80 via-purple-50/60 to-blue-100/80 border border-blue-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
                  {/* Business gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-100/20 to-blue-200/40 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-300/20 via-purple-300/10 to-blue-400/20 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

                  <div className="relative p-6 space-y-4">
                    <div className="flex items-center gap-2">
                      <Globe className="h-5 w-5 text-blue-600" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Društvene mreže
                      </h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {profile.platforms.map((platform, index) => (
                        <div
                          key={index}
                          className="bg-white/60 rounded-lg p-4 border border-blue-100/50 hover:bg-white/80 transition-all duration-300"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <PlatformIconSimple
                                platform={platform.platform_name}
                                size="xl"
                              />
                              <div>
                                <p className="font-medium text-gray-800">
                                  {platform.platform_name}
                                </p>
                                {platform.handle && (
                                  <p className="text-sm text-gray-600">
                                    @{platform.handle}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                {formatFollowers(platform.followers_count)}
                              </p>
                              <p className="text-xs text-gray-600">pratilaca</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
