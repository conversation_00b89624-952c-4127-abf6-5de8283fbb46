'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { checkInfluencerProfileAccess } from '@/lib/profile-access';
import { InfluencerProfileClient } from './InfluencerProfileClient';
import { Loader2 } from 'lucide-react';
import { PublicInfluencerProfile } from '@/lib/marketplace';
import { getBusiness } from '@/lib/profiles';

interface InfluencerProfileWithAccessControlProps {
  profile: PublicInfluencerProfile;
  targetUsername: string;
}

export function InfluencerProfileWithAccessControl({
  profile,
  targetUsername,
}: InfluencerProfileWithAccessControlProps) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [accessChecking, setAccessChecking] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const [businessSubscriptionType, setBusinessSubscriptionType] = useState<
    'free' | 'premium'
  >('free');

  useEffect(() => {
    checkAccess();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, targetUsername]);

  const checkAccess = async () => {
    if (authLoading) return;

    if (!user) {
      // Korisnik nije ulogovan - middleware bi trebao ovo spriječiti, ali dodajemo za sigurnost
      router.push('/prijava');
      return;
    }

    try {
      setAccessChecking(true);

      const accessResult = await checkInfluencerProfileAccess(
        targetUsername,
        user.id
      );

      if (!accessResult.hasAccess) {
        console.log(
          `Access denied for user ${user.id} to profile ${targetUsername}: ${accessResult.reason}`
        );

        if (accessResult.redirectTo) {
          router.push(accessResult.redirectTo);
        } else {
          router.push('/dashboard');
        }
        return;
      }

      // Access granted - user can view profile
      setHasAccess(true);

      // If the user is a business user, fetch their subscription type
      if (user.user_metadata?.user_type === 'business') {
        try {
          // Check new subscription system first
          const { supabase } = await import('@/lib/supabase');
          const { data: subscription } = await supabase
            .from('user_subscriptions')
            .select('status')
            .eq('user_id', user.id)
            .eq('user_type', 'business')
            .eq('status', 'active')
            .single();

          // Get business data (fallback to old system)
          const { data: businessData, error: businessError } =
            await getBusiness(user.id);

          if (businessData && !businessError) {
            // Use new subscription system if available, otherwise fallback
            const isPremium =
              !!subscription || businessData.subscription_type === 'premium';
            setBusinessSubscriptionType(isPremium ? 'premium' : 'free');
          }
        } catch (error) {
          console.error('Error fetching business subscription type:', error);
          // Default to free on error
          setBusinessSubscriptionType('free');
        }
      }
    } catch (error) {
      console.error('Error checking access:', error);
      router.push('/dashboard');
    } finally {
      setAccessChecking(false);
    }
  };

  // Prikaži loading dok se proverava autentifikacija ili access control
  if (authLoading || accessChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Provjera pristupa...</p>
        </div>
      </div>
    );
  }

  // Ako nema pristup, ne prikazuj ništa (redirect će se desiti)
  if (!hasAccess) {
    return null;
  }

  // Prikaži profil ako korisnik ima pristup
  return (
    <InfluencerProfileClient
      profile={profile}
      businessSubscriptionType={businessSubscriptionType}
    />
  );
}
