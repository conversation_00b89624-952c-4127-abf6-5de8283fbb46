'use client';

import * as React from 'react';
import { Crown, Star, CheckCircle } from 'lucide-react';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

export type UpgradeFeatureType =
  | 'campaign_activation'
  | 'application_viewing'
  | 'handle_viewing'
  | 'custom_offer_limit'
  | 'custom_offers_receiving'
  | 'campaign_application_limit';

interface UpgradeRequiredModalProps {
  isOpen: boolean;
  onClose: () => void;
  feature: UpgradeFeatureType;
  currentCount?: number;
  limit?: number;
  resetDate?: string;
}

const MODAL_CONTENT = {
  campaign_activation: {
    title: 'Premium plan potreban',
    icon: Crown,
    message:
      'Već imate aktiviranu 1 kampanju kao Free korisnik. Za aktivaciju više kampanja (neograničeno) potreban je Premium plan.',
    benefits: [
      'Neograničeno aktivnih kampanja',
      'Napredna analitika kampanja',
      'Prioritetna podrška',
      'Napredni filteri za pretragu',
    ],
    ctaText: 'Upgrade na Premium',
  },
  application_viewing: {
    title: 'Premium plan potreban',
    icon: Star,
    message:
      'Da biste vidjeli prijave na kampanje morate biti Premium korisnik',
    benefits: [
      'Pristup svim prijavama',
      'Detaljni influencer profili',
      'Chat sa influencerima',
      'Napredni sistemi ocjenjivanja',
    ],
    ctaText: 'Pretplatite se',
  },
  handle_viewing: {
    title: 'Premium plan potreban',
    icon: Star,
    message:
      'Da biste vidjeli društvene mreže influencera morate biti Premium korisnik',
    benefits: [
      'Pristup svim društvenim mrežama influencera',
      'Direktne linkove na profile',
      'Kontakt informacije',
      'Detaljne follower statistike',
    ],
    ctaText: 'Upgrade na Premium',
  },
  custom_offer_limit: {
    title: 'Dostignut mjesečni limit',
    icon: Crown,
    message:
      'Poslali ste već 3 custom ponude ovaj mjesec kao Free korisnik. Za neograničeno slanje ponuda potreban je Premium plan.',
    benefits: [
      'Neograničeno custom ponuda',
      'Prioritetni chat sa influencerima',
      'Napredni sistemi ocjenjivanja',
      'Detaljnu analitiku odgovora',
    ],
    ctaText: 'Upgrade na Premium',
  },
  custom_offers_receiving: {
    title: 'Premium plan potreban',
    icon: Crown,
    message:
      'Primanje custom ponuda je ekskluzivna Premium funkcija. Pretplatite se na Premium plan da biste mogli primati custom ponude od brendova.',
    benefits: [
      'Primanje custom ponuda od brendova',
      'Mogućnost pregovaranja cijene',
      'Personalizirane ponude prema vašim uslovima',
      'Prioritetna komunikacija sa brendovima',
    ],
    ctaText: 'Upgrade na Premium',
  },
  campaign_application_limit: {
    title: 'Dostignut mjesečni limit',
    icon: Crown,
    message:
      'Aplikovali ste već na 5 kampanja ovaj mjesec kao Free korisnik. Za neograničeno aplikovanje na kampanje potreban je Premium plan.',
    benefits: [
      'Neograničeno aplikovanja na kampanje',
      'Prioritetna vidljivost vašeg profila',
      'Napredna analitika performansi',
      'Direktan chat sa brendovima',
    ],
    ctaText: 'Upgrade na Premium',
  },
} as const;

export function UpgradeRequiredModal({
  isOpen,
  onClose,
  feature,
  currentCount,
  limit,
  resetDate,
}: UpgradeRequiredModalProps) {
  const content = MODAL_CONTENT[feature];
  const IconComponent = content.icon;

  const handleUpgrade = () => {
    // Determine redirect URL based on feature type
    const redirectUrl =
      feature === 'custom_offers_receiving' ||
      feature === 'campaign_application_limit'
        ? '/dashboard/influencer/packages'
        : '/dashboard/biznis/packages';

    onClose();
    window.location.href = redirectUrl;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
            <IconComponent className="h-6 w-6 text-yellow-600" />
          </div>
          <DialogTitle className="text-xl font-semibold">
            {content.title}
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {content.message}
          </DialogDescription>

          {feature === 'campaign_activation' && currentCount && limit && (
            <div className="mt-3 text-sm text-gray-500">
              Trenutno aktivno: {currentCount}/{limit} kampanja
            </div>
          )}

          {(feature === 'custom_offer_limit' ||
            feature === 'campaign_application_limit') &&
            currentCount !== undefined &&
            limit && (
              <div className="mt-3 space-y-1">
                <div className="text-sm text-gray-500">
                  {feature === 'custom_offer_limit'
                    ? `Poslano ovaj mjesec: ${currentCount}/${limit} custom ponuda`
                    : `Aplikovano ovaj mjesec: ${currentCount}/${limit} kampanja`}
                </div>
                {resetDate && (
                  <div className="text-xs text-gray-400">
                    Limit se resetira:{' '}
                    {new Date(resetDate).toLocaleDateString('sr-Latn-BA', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric',
                    })}
                  </div>
                )}
              </div>
            )}
        </DialogHeader>

        <div className="mt-6">
          <div className="mb-6">
            <h4 className="mb-3 font-medium text-gray-900">
              Premium prednosti:
            </h4>
            <ul className="space-y-2">
              {content.benefits.map((benefit, index) => (
                <li
                  key={index}
                  className="flex items-center text-sm text-gray-600"
                >
                  <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                  {benefit}
                </li>
              ))}
            </ul>
          </div>

          <div className="flex gap-3">
            <DialogClose asChild>
              <Button variant="outline" className="flex-1">
                Možda kasnije
              </Button>
            </DialogClose>
            <Button
              onClick={handleUpgrade}
              className="flex-1 bg-yellow-500 hover:bg-yellow-600"
            >
              {content.ctaText}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
