import { NextRequest, NextResponse } from 'next/server';
import { sendEmail, generatePasswordResetTemplate } from '@/lib/email-service';

export async function POST(request: NextRequest) {
  try {
    // Verify webhook signature if needed (for security)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { user, email_data } = body;

    if (!user?.email || !email_data?.token_url) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate our custom password reset email
    const { htmlContent, textContent } = generatePasswordResetTemplate(
      email_data.token_url,
      user.email
    );

    const result = await sendEmail({
      to: user.email,
      subject: 'Resetovanje lozinke - INFLUEXUS',
      htmlContent,
      textContent,
    });

    if (result.success) {
      return NextResponse.json({
        message: 'Password reset email sent successfully',
      });
    } else {
      console.error('Failed to send password reset email:', result.error);
      return NextResponse.json(
        { error: 'Failed to send email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Auth hook password reset error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
