'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  Mail,
  MessageCircle,
  Heart,
  Sparkles,
  ExternalLink,
  Copy,
  Check,
  Users,
} from 'lucide-react';
import { toast } from 'sonner';

export default function InfluencerSupportPage() {
  const [emailCopied, setEmailCopied] = useState(false);
  const supportEmail = '<EMAIL>';

  const copyEmailToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(supportEmail);
      setEmailCopied(true);
      toast.success('Email adresa kopirana!');
      setTimeout(() => setEmailCopied(false), 2000);
    } catch {
      toast.error('Greška pri kopiranju email adrese');
    }
  };

  const openEmailClient = () => {
    window.location.href = `mailto:${supportEmail}?subject=Influencer upit sa Influexus platforme`;
  };

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="bg-card/95 backdrop-blur-md border border-purple-500/20 shadow-2xl shadow-purple-500/20 rounded-2xl overflow-hidden relative">
          {/* Decorative background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#7F5BFE]/5 via-[#F35BF6]/3 to-[#f04a13]/5 opacity-50"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-400/10 to-transparent rounded-full blur-2xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-400/10 to-transparent rounded-full blur-xl"></div>

          <div className="relative p-8">
            {/* Header */}
            <div className="text-center space-y-6 pb-8">
              {/* Icon */}
              <div className="flex justify-center">
                <div className="flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-[#7F5BFE]/20 to-[#F35BF6]/20 border border-purple-500/20">
                  <Heart className="h-10 w-10 text-purple-600 dark:text-purple-400" />
                </div>
              </div>

              <div className="space-y-4">
                <h1 className="text-3xl font-bold text-foreground flex items-center justify-center gap-3">
                  <Users className="h-6 w-6 text-purple-500" />
                  Influencer Podrška
                  <Sparkles className="h-6 w-6 text-purple-500" />
                </h1>

                <Badge className="bg-gradient-to-r from-[#7F5BFE] to-[#F35BF6] text-white border-0 text-sm">
                  Podrška za influencere
                </Badge>
              </div>

              <p className="text-center text-muted-foreground leading-relaxed text-lg">
                Ovdje možete pronaći pomoć i podršku vezanu za vaš influencer
                profil i aktivnosti na platformi.
              </p>
            </div>

            {/* Content */}
            <div className="space-y-8">
              {/* Info Card */}
              <div className="relative p-6 rounded-xl bg-gradient-to-r from-purple-500/5 via-pink-500/3 to-orange-500/5 border border-purple-500/20">
                <div className="flex items-start gap-4">
                  <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex-shrink-0 mt-1">
                    <MessageCircle className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div className="space-y-3">
                    <h2 className="text-xl font-semibold text-foreground">
                      Javite nam se za:
                    </h2>
                    <ul className="text-muted-foreground space-y-2 text-base">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        Pitanja o profilu i postavkama
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        Pomoć s aplikacijama na kampanje
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        Problemi s isplatama
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        Problemi s komunikacijom s brendovima
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        Općenita pitanja o platformi
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Email Section */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground text-center">
                  Kontaktirajte nas
                </h2>

                <div className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-accent/30 to-accent/20 border border-border/50">
                  <Mail className="h-5 w-5 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                  <span className="font-mono text-base text-foreground flex-1">
                    {supportEmail}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={copyEmailToClipboard}
                    className="h-10 w-10 p-0 hover:bg-purple-500/10 transition-all duration-300 group"
                  >
                    {emailCopied ? (
                      <Check className="h-4 w-4 text-green-600 group-hover:scale-110 transition-transform duration-300" />
                    ) : (
                      <Copy className="h-4 w-4 text-purple-600 dark:text-purple-400 group-hover:scale-110 transition-transform duration-300" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Action Button */}
              <div className="flex justify-center pt-4">
                <Button
                  onClick={openEmailClient}
                  size="lg"
                  className="bg-gradient-to-r from-[#7F5BFE] via-[#F35BF6] to-[#f04a13] text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group px-8 py-3"
                >
                  <Mail className="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-300" />
                  Pošaljite email
                  <ExternalLink className="h-4 w-4 ml-3 group-hover:scale-110 transition-transform duration-300" />
                </Button>
              </div>

              {/* Footer */}
              <div className="text-center pt-4">
                <p className="text-sm text-muted-foreground/70">
                  Odgovorićemo vam u najkraćem mogućem roku
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
