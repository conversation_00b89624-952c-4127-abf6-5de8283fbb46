'use client';

import { useEffect, useState, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import {
  Calendar,
  FileText,
  Clock,
  CheckCircle,
  Send,
  Loader2,
  Hash,
  Ban,
  Euro,
  Building2,
  X,
  MessageCircle,
} from 'lucide-react';
import { toast } from 'sonner';
import { WithdrawApplicationModal } from '@/components/applications/WithdrawApplicationModal';
import { BackButton } from '@/components/ui/back-button';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  getCampaignApplication,
  getApplicationPaymentInfo,
  withdrawCampaignApplication,
} from '@/lib/campaigns';
import { getJobCompletionByCampaignApplication } from '@/lib/job-completions';
import { CampaignJobSubmissionForm } from '@/components/job-completion/CampaignJobSubmissionForm';
import { formatDate } from '@/lib/date-utils';
import { ApplicationWithDetails } from '@/lib/types';
import { JobCompletionWithDetails } from '@/lib/job-completions';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';

export default function InfluencerApplicationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [application, setApplication] = useState<ApplicationWithDetails | null>(
    null
  );
  const [paymentInfo, setPaymentInfo] = useState<Record<
    string,
    unknown
  > | null>(null);
  const [jobCompletion, setJobCompletion] =
    useState<JobCompletionWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLoadingJobCompletion, setIsLoadingJobCompletion] = useState(false);
  const [showJobSubmission, setShowJobSubmission] = useState(false);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);

  const loadApplication = useCallback(async () => {
    try {
      const { data, error } = await getCampaignApplication(params.id as string);
      if (error) {
        console.error('Error loading application:', error);
        return;
      }
      setApplication(data);

      // Load payment info if application is accepted
      if (data && data.status === 'accepted') {
        const paymentData = await getApplicationPaymentInfo(data.id);
        setPaymentInfo(paymentData as Record<string, unknown> | null);
      }
    } catch (error) {
      console.error('Error loading application:', error);
    } finally {
      setLoading(false);
    }
  }, [params.id]);

  const loadJobCompletion = useCallback(async (applicationId: string) => {
    setIsLoadingJobCompletion(true);
    try {
      const { data } =
        await getJobCompletionByCampaignApplication(applicationId);
      setJobCompletion(data as JobCompletionWithDetails | null);
    } catch (error) {
      console.error('Error loading job completion:', error);
    } finally {
      setIsLoadingJobCompletion(false);
    }
  }, []);

  useEffect(() => {
    loadApplication();
  }, [loadApplication]);

  useEffect(() => {
    if (application?.status === 'accepted') {
      loadJobCompletion(application.id);
    }
  }, [application, loadJobCompletion]);

  const handleWithdrawApplication = () => {
    setShowWithdrawModal(true);
  };

  const confirmWithdrawApplication = async () => {
    setShowWithdrawModal(false);
    setIsWithdrawing(true);
    try {
      const { error } = await withdrawCampaignApplication(application!.id);

      if (error) {
        toast.error(
          (error as any)?.message || 'Greška pri povlačenju aplikacije'
        );
        return;
      }

      toast.success('Aplikacija je uspješno povučena');
      router.push('/dashboard/influencer/applications');
    } catch (error) {
      console.error('Error withdrawing application:', error);
      toast.error('Neočekivana greška pri povlačenju aplikacije');
    } finally {
      setIsWithdrawing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  if (!application) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold mb-2">Aplikacija nije pronađena</h2>
          <p className="text-muted-foreground">
            Možda je obrisana ili nemate dozvolu da je vidite.
          </p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <div className="hidden md:block">
            <Link href="/dashboard/influencer/applications">
              <BackButton />
            </Link>
          </div>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Detalji aplikacije
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Pregled vaše aplikacije za kampanju
            </p>
          </div>
        </div>

        {/* Priority Status Cards - Always at Top */}
        <div className="mb-6">
          {/* Payment Pending Status - Always shows as full width when payment not completed */}
          {application.status === 'accepted' && !paymentInfo && (
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-amber-50/80 via-yellow-50/60 to-amber-100/80 dark:from-amber-950/20 dark:via-yellow-950/10 dark:to-amber-900/30 border border-amber-200/50 dark:border-amber-800/30 backdrop-blur-sm shadow-lg mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-amber-100/30 via-yellow-100/20 to-amber-200/40 dark:from-amber-900/10 dark:via-yellow-900/5 dark:to-amber-800/20 opacity-60" />
              <div className="relative p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Clock className="h-5 w-5 text-amber-600" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent">
                    Čeka se plaćanje biznisa
                  </h3>
                </div>

                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-amber-100/50 dark:border-amber-800/30">
                  <p className="text-amber-700 dark:text-amber-300 mb-3">
                    🎉 Biznis je prihvatio vašu aplikaciju! Sada čekamo da
                    izvrši plaćanje pre početka komunikacije i rada na
                    kampaanji.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-amber-600 dark:text-amber-400">
                        Predložena cena:
                      </span>
                      <span className="font-semibold text-amber-900 dark:text-amber-100">
                        {application.proposed_rate.toLocaleString()} €
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-amber-600 dark:text-amber-400">
                        Status plaćanja:
                      </span>
                      <span className="font-medium text-amber-800 dark:text-amber-200">
                        U toku...
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 text-center">
                  <p className="text-sm text-amber-700 dark:text-amber-300">
                    Bićete obavešteni čim biznis izvrši plaćanje.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Two-column layout for job completion and payment completed cards on desktop */}
          {application.status === 'accepted' && paymentInfo && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Job Completion Section */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                    Završetak posla
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    Označite posao kao završen kada završite sve dogovorene
                    aktivnosti
                  </p>
                  <div>
                    {isLoadingJobCompletion ? (
                      <div className="animate-pulse space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    ) : !jobCompletion && !showJobSubmission ? (
                      <div className="text-center py-6">
                        <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          Spremni za submission?
                        </h3>
                        <p className="text-muted-foreground mb-4">
                          Kada završite sve dogovorene aktivnosti, možete
                          označiti posao kao završen.
                        </p>
                        <button
                          onClick={() => setShowJobSubmission(true)}
                          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-200/50 dark:hover:shadow-purple-900/30"
                        >
                          <Send className="w-4 h-4" />
                          Označi kao završeno
                        </button>
                      </div>
                    ) : showJobSubmission && !jobCompletion ? (
                      <CampaignJobSubmissionForm
                        campaignApplicationId={application.id}
                        campaignTitle={application.campaign.title}
                        businessName={
                          application.campaign.business?.company_name
                        }
                        businessAvatar={
                          application.campaign.business?.profile?.avatar_url
                        }
                        proposedRate={application.proposed_rate}
                        onSuccess={() => {
                          setShowJobSubmission(false);
                          loadJobCompletion(application.id);
                        }}
                        onCancel={() => setShowJobSubmission(false)}
                      />
                    ) : jobCompletion ? (
                      <div className="space-y-4">
                        <div className="text-center py-6">
                          <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                          <h3 className="text-lg font-medium mb-2">
                            Završetak posla je poslan!
                          </h3>
                          <p className="text-muted-foreground mb-4">
                            {jobCompletion.status === 'submitted' &&
                              'Vaš rad je poslan biznisu na pregled. Čekamo njihovu potvrdu.'}
                            {jobCompletion.status === 'approved' &&
                              'Biznis je odobrio vaš rad. Čestitamo!'}
                            {jobCompletion.status === 'rejected' &&
                              'Biznis je odbacio vaš rad. Molimo kontaktirajte ih za više informacija.'}
                          </p>
                          <Badge
                            variant={
                              jobCompletion.status === 'approved'
                                ? 'default'
                                : jobCompletion.status === 'rejected'
                                  ? 'destructive'
                                  : 'secondary'
                            }
                            className="mb-4"
                          >
                            {jobCompletion.status === 'approved'
                              ? 'Odobreno'
                              : jobCompletion.status === 'rejected'
                                ? 'Odbijeno'
                                : jobCompletion.status === 'submitted'
                                  ? 'Na čekanju pregleda'
                                  : 'Pending'}
                          </Badge>
                        </div>

                        {jobCompletion.submitted_at && (
                          <div className="text-center">
                            <p className="text-sm text-muted-foreground">
                              Poslano{' '}
                              {formatDistanceToNow(
                                new Date(jobCompletion.submitted_at),
                                { addSuffix: true, locale: hr }
                              )}
                            </p>
                          </div>
                        )}

                        {jobCompletion.submission_notes && (
                          <div>
                            {(() => {
                              try {
                                const submissionData = JSON.parse(
                                  jobCompletion.submission_notes
                                );
                                return (
                                  <div className="space-y-4">
                                    {/* Post Links */}
                                    {submissionData.post_links &&
                                      submissionData.post_links.length > 0 && (
                                        <div>
                                          <h4 className="font-medium mb-3 text-gray-800">
                                            Objavljeni postovi:
                                          </h4>
                                          <div className="space-y-2">
                                            {submissionData.post_links.map(
                                              (link: string, index: number) => (
                                                <div
                                                  key={index}
                                                  className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg border border-blue-100"
                                                >
                                                  <a
                                                    href={link}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-2 hover:underline"
                                                  >
                                                    <svg
                                                      className="h-4 w-4"
                                                      fill="none"
                                                      stroke="currentColor"
                                                      viewBox="0 0 24 24"
                                                    >
                                                      <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                                      />
                                                    </svg>
                                                    {link}
                                                  </a>
                                                </div>
                                              )
                                            )}
                                          </div>
                                        </div>
                                      )}

                                    {/* Optional Message */}
                                    {submissionData.message && (
                                      <div>
                                        <h4 className="font-medium mb-2 text-gray-800">
                                          Dodatna poruka:
                                        </h4>
                                        <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border border-green-100">
                                          <p className="text-sm text-gray-700">
                                            {submissionData.message}
                                          </p>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                );
                              } catch {
                                // Fallback for old format
                                return (
                                  <div>
                                    <h4 className="font-medium mb-2">
                                      Vaše napomene:
                                    </h4>
                                    <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                                      {jobCompletion.submission_notes}
                                    </p>
                                  </div>
                                );
                              }
                            })()}
                          </div>
                        )}

                        {jobCompletion.business_notes && (
                          <div>
                            <h4 className="font-medium mb-2">
                              Napomene biznisa:
                            </h4>
                            <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                              {jobCompletion.business_notes}
                            </p>
                          </div>
                        )}
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>

              {/* Payment Completed Status */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-50/80 via-emerald-50/60 to-green-100/80 dark:from-green-950/20 dark:via-emerald-950/10 dark:to-green-900/30 border border-green-200/50 dark:border-green-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-green-100/30 via-emerald-100/20 to-green-200/40 dark:from-green-900/10 dark:via-emerald-900/5 dark:to-green-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                      Plaćanje završeno - možete početi!
                    </h3>
                  </div>
                  <p className="text-green-600 dark:text-green-400 text-sm mb-4">
                    Odlične vesti! Biznis je izvršio plaćanje.
                  </p>

                  {/* Centered Payment Amount */}
                  <div className="text-center py-6">
                    <Euro className="h-12 w-12 text-green-600 mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2 text-green-800 dark:text-green-200">
                      Osigurana sredstva
                    </h3>
                    {paymentInfo && (
                      <div className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-4">
                        {paymentInfo?.payment_amount?.toLocaleString()} €
                      </div>
                    )}
                    <p className="text-green-700 dark:text-green-300 text-sm">
                      Možete komunicirati i početi rad na kampaanji.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Application Status */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Status aplikacije
                  </h3>
                  <Badge
                    variant="outline"
                    className={`${getStatusColor(application.status || 'pending')} font-medium`}
                  >
                    {getStatusText(application.status || 'pending')}
                  </Badge>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-purple-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Aplicirali:{' '}
                    </span>
                    <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                      {formatDate(application.applied_at || '')}
                    </span>
                  </div>

                  {application.status === 'rejected' &&
                    (application as any).rejection_reason && (
                      <div className="p-3 bg-red-50/80 border border-red-200/50 rounded-lg backdrop-blur-sm">
                        <p className="text-sm text-red-800">
                          <strong>Razlog odbacivanja:</strong>{' '}
                          {(application as any).rejection_reason}
                        </p>
                      </div>
                    )}
                </div>
              </div>
            </div>

            {/* Campaign Details */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6 space-y-4">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Detalji kampanje
                </h3>

                <div>
                  <h4 className="font-medium text-lg text-gray-900 dark:text-gray-100">
                    {application.campaign.title}
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300 mt-1 leading-relaxed">
                    {application.campaign.description}
                  </p>
                </div>

                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                  <div className="flex items-center gap-2">
                    <Euro className="w-4 h-4 text-purple-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Budžet kampanje:
                    </span>
                    <span className="font-semibold text-gray-900 dark:text-gray-100">
                      {application.campaign.budget} €
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Platforms and Content Types */}
            {application.campaign.platforms &&
              application.campaign.platforms.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                      Platforme i tipovi sadržaja
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                      Gde influencer treba da objavi sadržaj i koji tip sadržaja
                    </p>
                    <div className="space-y-3">
                      {application.campaign.platforms.map(
                        (platform: any, index: number) => (
                          <div
                            key={index}
                            className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30"
                          >
                            <div className="flex items-center gap-2 mb-2">
                              <PlatformIconSimple
                                platform={platform.name}
                                size="md"
                              />
                              <span className="font-medium text-gray-800 dark:text-gray-200 text-sm">
                                {platform.name}
                              </span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {platform.content_types?.map(
                                (type: string, typeIndex: number) => (
                                  <Badge
                                    key={typeIndex}
                                    variant="secondary"
                                    className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                                  >
                                    {type === 'post'
                                      ? 'Photo post'
                                      : type === 'video'
                                        ? 'Video'
                                        : type === 'story'
                                          ? 'Story'
                                          : type === 'reel'
                                            ? 'Shorts'
                                            : type}
                                  </Badge>
                                )
                              )}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                </div>
              )}

            {/* Hashtags */}
            {application.campaign.hashtags &&
              application.campaign.hashtags.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Hash className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Obavezni hashtag-ovi
                      </h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {application.campaign.hashtags.map(
                        (hashtag: string, index: number) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="font-mono bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                          >
                            #{hashtag}
                          </Badge>
                        )
                      )}
                    </div>
                  </div>
                </div>
              )}

            {/* Do Not Mention */}
            {application.campaign.do_not_mention &&
              application.campaign.do_not_mention.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Ban className="h-5 w-5 text-red-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Ne spominjati
                      </h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {application.campaign.do_not_mention.map(
                        (item: string, index: number) => (
                          <Badge
                            key={index}
                            className="bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200"
                          >
                            {item}
                          </Badge>
                        )
                      )}
                    </div>
                  </div>
                </div>
              )}

            {/* Additional Notes */}
            {application.campaign.additional_notes && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <FileText className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Dodatne napomene
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                    {application.campaign.additional_notes}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Your Application */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6 space-y-4">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Vaša aplikacija
                </h3>

                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                  <div className="flex items-center gap-2">
                    <Euro className="w-4 h-4 text-purple-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Predložena cijena:
                    </span>
                    <span className="font-semibold text-gray-900 dark:text-gray-100">
                      {application.proposed_rate} €
                    </span>
                  </div>
                </div>

                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                  <div className="flex items-center gap-2 mb-1">
                    <Clock className="w-4 h-4 text-pink-500" />
                    <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Vremenski okvir:
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {application.delivery_timeframe}
                  </p>
                </div>

                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                  <div className="flex items-center gap-2 mb-1">
                    <FileText className="w-4 h-4 text-purple-500" />
                    <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Vaš prijedlog:
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                    {application.proposal_text}
                  </p>
                </div>

                {application.experience_relevant && (
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                    <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Relevantno iskustvo:
                    </span>
                    <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 whitespace-pre-wrap leading-relaxed">
                      {application.experience_relevant}
                    </p>
                  </div>
                )}

                {application.audience_insights && (
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                    <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Informacije o publici:
                    </span>
                    <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 whitespace-pre-wrap leading-relaxed">
                      {application.audience_insights}
                    </p>
                  </div>
                )}

                {/* Withdraw Application Button - only show if status is pending */}
                {application.status === 'pending' && (
                  <div className="pt-4">
                    <Button
                      variant="outline"
                      className="w-full border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 hover:text-red-700 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-950/20"
                      onClick={handleWithdrawApplication}
                      disabled={isWithdrawing}
                    >
                      {isWithdrawing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Povlačim aplikaciju...
                        </>
                      ) : (
                        <>
                          <X className="h-4 w-4 mr-2" />
                          Povuci aplikaciju
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Business Info */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                  Biznis
                </h3>

                <div className="flex items-center gap-3">
                  <Avatar className="h-16 w-16 border-2 border-white/50">
                    <AvatarImage
                      src={
                        application.campaign.business?.profile?.avatar_url || ''
                      }
                      alt={application.campaign.business?.company_name}
                    />
                    <AvatarFallback className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-lg">
                      <Building2 className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                      {application.campaign.business?.company_name ||
                        application.campaign.business?.profile?.username ||
                        'Business'}
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      @{application.campaign.business?.profile?.username}
                    </p>
                  </div>
                </div>

                {application.campaign.business?.profile?.username && (
                  <Link
                    href={`/business/${application.campaign.business.profile.username}`}
                  >
                    <button className="w-full mt-4 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md">
                      <Building2 className="h-4 w-4" />
                      Pogledaj biznis profil
                    </button>
                  </Link>
                )}
              </div>
            </div>

            {/* Chat */}
            {application.status === 'accepted' &&
              paymentInfo &&
              application.campaign.business_id && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                      Komunikacija
                    </h3>
                    <Button
                      onClick={() => {
                        window.location.href = `/dashboard/chat?application=${application.id}`;
                      }}
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0"
                    >
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Otvori chat
                    </Button>
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>

      <WithdrawApplicationModal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        onConfirm={confirmWithdrawApplication}
        isLoading={isWithdrawing}
      />
    </DashboardLayout>
  );
}
