'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Check, Clock } from 'lucide-react';
import {
  getChatPermission,
  approveBusinessChat,
  approveInfluencerChat,
  isChatEnabled,
} from '@/lib/chat-permissions';
import { toast } from 'sonner';
import type { ChatPermission } from '@/lib/chat-permissions';

interface ChatPermissionStatusProps {
  businessId: string;
  influencerId: string;
  offerId?: string;
  applicationId?: string;
  userType: 'business' | 'influencer';
  onChatEnabled?: () => void;
}

export function ChatPermissionStatus({
  businessId,
  influencerId,
  offerId,
  applicationId,
  userType,
  onChatEnabled,
}: ChatPermissionStatusProps) {
  const [permission, setPermission] = useState<ChatPermission | null>(null);
  const [loading, setLoading] = useState(true);
  const [approving, setApproving] = useState(false);
  const [chatEnabled, setChatEnabled] = useState(false);

  useEffect(() => {
    loadPermission();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [businessId, influencerId, offerId, applicationId]);

  const loadPermission = async () => {
    try {
      setLoading(true);
      const permissionData = await getChatPermission(
        businessId,
        influencerId,
        offerId,
        applicationId
      );
      setPermission(permissionData);

      if (permissionData) {
        setChatEnabled(permissionData.chat_enabled || false);
      } else {
        // Provjeri da li je chat omogućen
        const enabled = await isChatEnabled(
          businessId,
          influencerId,
          offerId,
          applicationId
        );
        setChatEnabled(enabled);
      }
    } catch (error) {
      console.error('Error loading chat permission:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    try {
      setApproving(true);

      if (userType === 'business') {
        await approveBusinessChat(
          businessId,
          influencerId,
          offerId,
          applicationId
        );
        toast.success(
          'Odobrili ste chat komunikaciju. Čeka se odobrenje od influencera.'
        );
      } else {
        await approveInfluencerChat(
          businessId,
          influencerId,
          offerId,
          applicationId
        );
        toast.success(
          'Odobrili ste chat komunikaciju. Čeka se odobrenje od biznisa.'
        );
      }

      await loadPermission();

      // Provjeri da li je chat sada omogućen
      const enabled = await isChatEnabled(
        businessId,
        influencerId,
        offerId,
        applicationId
      );
      if (enabled && onChatEnabled) {
        onChatEnabled();
      }
    } catch (error) {
      console.error('Error approving chat:', error);
      toast.error('Došlo je do greške prilikom odobravanja chat-a.');
    } finally {
      setApproving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 animate-spin" />
            <span>Učitavanje...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (chatEnabled) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5 text-green-600" />
            <span className="font-medium text-green-800">Chat je omogućen</span>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <Check className="h-3 w-3 mr-1" />
              Aktivno
            </Badge>
          </div>
          <p className="text-sm text-green-600 mt-2">
            Obje strane su odobrile komunikaciju. Možete početi chat.
          </p>
        </CardContent>
      </Card>
    );
  }

  const currentUserApproved =
    userType === 'business'
      ? permission?.business_approved
      : permission?.influencer_approved;

  const otherUserApproved =
    userType === 'business'
      ? permission?.influencer_approved
      : permission?.business_approved;

  const otherUserType = userType === 'business' ? 'influencer' : 'biznis';

  return (
    <Card className="border-yellow-200 bg-yellow-50">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-yellow-800">
          <MessageCircle className="h-5 w-5" />
          <span>Chat dozvola</span>
        </CardTitle>
        <CardDescription className="text-yellow-600">
          Chat komunikacija zahtijeva odobrenje obje strane
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Vaše odobrenje:</span>
            {currentUserApproved ? (
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800"
              >
                <Check className="h-3 w-3 mr-1" />
                Odobreno
              </Badge>
            ) : (
              <Badge variant="secondary" className="bg-gray-100 text-gray-600">
                <Clock className="h-3 w-3 mr-1" />
                Čeka
              </Badge>
            )}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              Odobrenje {otherUserType}a:
            </span>
            {otherUserApproved ? (
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800"
              >
                <Check className="h-3 w-3 mr-1" />
                Odobreno
              </Badge>
            ) : (
              <Badge variant="secondary" className="bg-gray-100 text-gray-600">
                <Clock className="h-3 w-3 mr-1" />
                Čeka
              </Badge>
            )}
          </div>
        </div>

        {!currentUserApproved && (
          <Button
            onClick={handleApprove}
            disabled={approving}
            className="w-full"
          >
            {approving ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Odobrava se...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Odobri chat komunikaciju
              </>
            )}
          </Button>
        )}

        {currentUserApproved && !otherUserApproved && (
          <div className="text-center text-sm text-yellow-600">
            Čeka se odobrenje od {otherUserType}a da bi se omogućio chat.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
