# 📸 Profile Image Upload sa Kompresijom - Dokumentacija

**Implementirano**: 16.08.2025  
**Status**: ZAVRŠENO ✅

## 🎯 **Pregled funkcionalnosti**

Implementiran je kompletan sistem za upload profile slika sa automatskom kompresijom u WebP format i kreiranje multiple verzija za različite use case-ove.

## 🔧 **Tehnički detalji**

### **Biblioteke k<PERSON>šćene:**

- `browser-image-compression` - za client-side kompresiju slika
- React hooks za state management
- Supabase Storage za čuvanje slika

### **Folder struktura u Supabase Storage:**

```
avatars/
└── {userId}/
    ├── navbar/     # 40x40px - za navbar ikone
    ├── card/       # 300x300px - za marketplace kartice (povećano za bolji kvalitet)
    ├── profile/    # 400x400px - za profile stranice
    └── preview/    # 600x600px - za business preview sekcije
```

**Napomena**: Folder struktura je organizovana po user ID-u za RLS (Row Level Security) kompatibilnost.

### **Database schema ažuriranje:**

```sql
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS
  navbar_avatar_url TEXT,
  card_avatar_url TEXT,
  profile_avatar_url TEXT,
  preview_avatar_url TEXT;
```

### **Storage RLS Policies:**

```sql
-- Enable users to upload their own avatars
CREATE POLICY "Users can upload their own avatars" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Enable users to update their own avatars
CREATE POLICY "Users can update their own avatars" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Enable users to delete their own avatars
CREATE POLICY "Users can delete their own avatars" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Enable public read access to avatars
CREATE POLICY "Public can view avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');
```

## 📁 **Krerani fajlovi**

### **1. `src/lib/image-compression.ts`**

- Utility funkcije za kompresiju slika
- Konfiguracija različitih dimenzija
- Validacija fajlova
- Progress tracking

### **2. `src/lib/avatar-upload.ts`**

- Upload funkcionalnost na Supabase
- Organizacija storage strukture
- Database update logika
- Error handling

### **3. `src/components/profile/AvatarUpload.tsx`**

- React komponenta za upload
- Drag & Drop funkcionalnost
- Preview slika
- Progress indicator
- Success/Error messaging

### **4. `src/components/ui/alert.tsx`**

- Alert komponenta za notifikacije
- Različiti varianti (default, destructive)

## 🚀 **Kako koristiti**

### **1. Import komponente:**

```tsx
import { AvatarUpload } from '@/components/profile/AvatarUpload';
```

### **2. Korišćenje u komponenti:**

```tsx
<AvatarUpload
  userId={user.id}
  currentAvatarUrl={profile?.avatar_url}
  onUploadComplete={avatarUrls => {
    toast.success('Slika je upload-ovana!');
    // Refresh profile data
  }}
  onUploadError={error => {
    toast.error(`Greška: ${error}`);
  }}
/>
```

### **3. Dobijanje različitih verzija slika:**

```tsx
import { getAvatarUrls } from '@/lib/avatar-upload';

const avatarUrls = await getAvatarUrls(userId);
// avatarUrls.navbar_url - za navbar
// avatarUrls.card_url - za kartice
// avatarUrls.profile_url - za profile
// avatarUrls.preview_url - za preview
```

## 📊 **Kompresija statistike**

Sistem automatski:

- Kompresuje slike u WebP format
- Kreira 4 različite dimenzije
- Prikazuje compression ratio (koliko % prostora je uštjeđeno)
- Validira fajl tip i veličinu (max 10MB)

## 🔄 **Backward compatibility**

- Postojeći `avatar_url` se i dalje koristi kao fallback
- Stare slike će raditi normalno
- Nove slike se čuvaju u `profile_avatar_url` i kopiraju u `avatar_url`

## 🎨 **UX funkcionalnosti**

- **Drag & Drop** - povuci sliku ili klikni da izabereš
- **Preview** - vidi sliku prije upload-a
- **Progress bar** - prati napredak kompresije i upload-a
- **Validation** - provjera formata i veličine
- **Success/Error messages** - jasne poruke o statusu

## 📱 **Responsive design**

Komponenta je potpuno responsive i radi na:

- Desktop računarima
- Tablet uređajima
- Mobilnim telefonima

## 🔒 **Sigurnost**

- Client-side validacija fajlova
- Server-side RLS policies na Supabase
- Ograničena veličina fajlova (10MB)
- Dozvoljen samo image/\* MIME tipovi

## 🚀 **Performance optimizacije**

- **WebP format** - 25-35% manji fajlovi od JPEG
- **Multiple sizes** - učitava se odgovarajuća veličina za svaki use case
- **Web Workers** - kompresija se izvršava u background thread-u
- **Lazy loading** - slike se učitavaju kada su potrebne

## 📈 **Buduća poboljšanja**

Moguća poboljšanja:

- [ ] Batch upload multiple slika
- [ ] Image cropping tool
- [ ] AVIF format support (kada bude šire podržan)
- [ ] CDN integration za još brže učitavanje
- [ ] Automatic image optimization na server strani

## 🐛 **Troubleshooting**

### **Česti problemi:**

1. **"Module not found: @/components/ui/alert"**
   - Riješeno: Kreirana Alert komponenta

2. **Upload ne radi - RLS Error**
   - **Error**: "new row violates row-level security policy"
   - **Uzrok**: Nedostaju storage RLS policies
   - **Riješenje**: Kreiraj storage policies (vidi sekciju "Storage RLS Policies")
   - **Provjeri**: Da li je folder struktura `{userId}/{sizeType}/filename.webp`

3. **Kompresija sporo**
   - Normalno za velike slike
   - Web Worker se koristi da ne blokira UI

4. **Slika se ne prikazuje**
   - Provjeri da li su URL-ovi ispravno sačuvani u bazi
   - Provjeri da li je bucket public

## 📞 **Support**

Za pitanja ili probleme, kontaktiraj development tim ili kreiraj issue u repository-ju.
