import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  console.log('🚀 API Route called - create-application-payment');
  try {
    // Get auth token from request header
    const authHeader = req.headers.get('authorization');
    console.log('🔑 Auth header present:', !!authHeader);

    if (!authHeader) {
      console.log('❌ No auth header');
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const { applicationId, proposedRate } = await req.json();
    console.log('📝 Request data:', { applicationId, proposedRate });
    console.log('🌍 Environment check:', {
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      anonKeyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length,
      nodeEnv: process.env.NODE_ENV,
    });

    console.log('🌍 About to call Edge Function...');

    // Call Edge Function with proper auth header
    const edgeFunctionUrl =
      'https://awxxrkyommynqlcdwwon.supabase.co/functions/v1/create-application-payment';
    console.log('🔗 Edge Function URL:', edgeFunctionUrl);

    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader,
        apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      },
      body: JSON.stringify({
        applicationId,
        proposedRate,
      }),
    });

    console.log('🔄 Edge Function response status:', response.status);
    console.log('🔄 Edge Function response ok:', response.ok);

    if (!response.ok) {
      const error = await response.text();
      console.error('❌ Edge Function error:', error);
      return NextResponse.json(
        { error: 'Failed to create payment session', details: error },
        { status: response.status }
      );
    }

    const result = await response.json();
    console.log('✅ Edge Function success:', result);
    return NextResponse.json(result);
  } catch (error) {
    console.error('🔥 Critical error in API route:', error);
    console.error(
      '🔥 Error stack:',
      error instanceof Error ? error.stack : 'No stack trace'
    );
    return NextResponse.json(
      {
        error: 'Failed to create payment session',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
