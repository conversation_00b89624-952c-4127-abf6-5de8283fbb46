src/app/campaigns/[id]/edit/page.tsx(279,13): error TS2322: Type '{ title: string; description: string; budget: number | null; content_types: string[]; min_followers: number | undefined; max_followers: number | undefined; ageRangeMin: number | undefined; ... 12 more ...; isFeatured: boolean; }' is not assignable to type 'Partial<{ title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }>'.
  Types of property 'budget' are incompatible.
    Type 'number | null' is not assignable to type 'number | undefined'.
      Type 'null' is not assignable to type 'number | undefined'.
src/app/campaigns/[id]/edit/page.tsx(280,13): error TS2322: Type '{ platform_id: string; platforms: { name: string; }; }[]' is not assignable to type '{ platform_id: number; platforms: { name: string; }; }[]'.
  Type '{ platform_id: string; platforms: { name: string; }; }' is not assignable to type '{ platform_id: number; platforms: { name: string; }; }'.
    Types of property 'platform_id' are incompatible.
      Type 'string' is not assignable to type 'number'.
src/app/campaigns/[id]/edit/page.tsx(281,13): error TS2322: Type '(formData: { title: string; description: string; budget: number; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; gender?: "male" | "female" | "all"; ageRangeMin?: number; ageRangeMax?: number; hashtags?: string; doNotMention?: string; additionalNotes?: string; showBusinessName: boolean...' is not assignable to type '(data: { title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }) => void'.
  Types of parameters 'formData' and 'data' are incompatible.
    Type '{ title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }' is missing the following properties from type '{ title: string; description: string; budget: number; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; gender?: "all" | "male" | "female" | undefined; ageRangeMin?: number | undefined; ... 9 more ...; max_followers?: number | undefined; }': selectedPlatforms, content_types
src/app/campaigns/[id]/page.tsx(118,46): error TS2448: Block-scoped variable 'loadData' used before its declaration.
src/app/campaigns/[id]/page.tsx(118,46): error TS2454: Variable 'loadData' is used before being assigned.
src/app/campaigns/[id]/page.tsx(131,18): error TS2345: Argument of type '{ address: string | null; age: number | null; avatar_url: string | null; average_rating: number | null; bank_account: string | null; bank_name: string | null; bio: string | null; ... 22 more ...; website_url: string | null; }' is not assignable to parameter of type 'SetStateAction<{ id: string; user_type: "influencer" | "business"; username?: string | undefined; } | null>'.
  Type '{ address: string | null; age: number | null; avatar_url: string | null; average_rating: number | null; bank_account: string | null; bank_name: string | null; bio: string | null; ... 22 more ...; website_url: string | null; }' is not assignable to type '{ id: string; user_type: "influencer" | "business"; username?: string | undefined; }'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
src/app/campaigns/[id]/page.tsx(384,27): error TS2339: Property 'message' does not exist on type '{}'.
src/app/campaigns/[id]/page.tsx(517,39): error TS2339: Property 'content_types' does not exist on type 'PlatformWithDetails'.
src/app/campaigns/[id]/page.tsx(517,59): error TS7006: Parameter 'type' implicitly has an 'any' type.
src/app/campaigns/[id]/page.tsx(517,65): error TS7006: Parameter 'typeIndex' implicitly has an 'any' type.
src/app/campaigns/[id]/page.tsx(784,24): error TS2322: Type '{ children: (string | Element)[]; gradientVariant: "primary"; className: string; size: string; onClick: () => Promise<void>; disabled: boolean; }' is not assignable to type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
  Property 'children' does not exist on type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
src/app/campaigns/[id]/page.tsx(801,26): error TS2322: Type '{ children: (string | number | Element | undefined)[]; gradientVariant: "primary"; className: string; size: string; onClick: () => void; }' is not assignable to type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
  Property 'children' does not exist on type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
src/app/campaigns/[id]/page.tsx(833,27): error TS2322: Type 'boolean | 0 | undefined' is not assignable to type 'boolean | undefined'.
  Type '0' is not assignable to type 'boolean | undefined'.
src/app/campaigns/[id]/page.tsx(968,26): error TS2322: Type '{ children: (string | Element)[]; gradientVariant: "primary"; className: string; size: string; onClick: () => void; }' is not assignable to type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
  Property 'children' does not exist on type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
src/app/campaigns/[id]/page.tsx(990,33): error TS2322: Type 'number | null' is not assignable to type 'number'.
  Type 'null' is not assignable to type 'number'.
src/app/campaigns/[id]/page.tsx(992,33): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/app/campaigns/[id]/page.tsx(993,33): error TS2322: Type 'PlatformWithDetails[] | undefined' is not assignable to type '{ platform_name: string; platform_icon: string; posts_required: number; budget_per_post: number; }[]'.
  Type 'undefined' is not assignable to type '{ platform_name: string; platform_icon: string; posts_required: number; budget_per_post: number; }[]'.
src/app/campaigns/[id]/page.tsx(1055,9): error TS2322: Type '{ isOpen: boolean; onClose: () => void; campaign: { id: string; title: string; business_id: string; } | null; onSuccess: () => void; }' is not assignable to type 'IntrinsicAttributes & PromoteCampaignModalProps'.
  Property 'onSuccess' does not exist on type 'IntrinsicAttributes & PromoteCampaignModalProps'.
src/app/dashboard/biznis/account/page.tsx(123,13): error TS2448: Block-scoped variable 'loadProfile' used before its declaration.
src/app/dashboard/biznis/account/page.tsx(123,13): error TS2454: Variable 'loadProfile' is used before being assigned.
src/app/dashboard/biznis/account/page.tsx(137,18): error TS2345: Argument of type '{ address: string | null; age: number | null; avatar_url: string | null; average_rating: number | null; bank_account: string | null; bank_name: string | null; bio: string | null; ... 22 more ...; website_url: string | null; }' is not assignable to parameter of type 'SetStateAction<{ id: string; full_name?: string | undefined; email?: string | undefined; phone?: string | undefined; city?: string | undefined; country?: string | undefined; } | null>'.
  Type '{ address: string | null; age: number | null; avatar_url: string | null; average_rating: number | null; bank_account: string | null; bank_name: string | null; bio: string | null; ... 22 more ...; website_url: string | null; }' is not assignable to type '{ id: string; full_name?: string | undefined; email?: string | undefined; phone?: string | undefined; city?: string | undefined; country?: string | undefined; }'.
    Types of property 'full_name' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
src/app/dashboard/biznis/account/page.tsx(156,38): error TS2353: Object literal may only specify known properties, and 'hasActiveSubscription' does not exist in type '{ company_name?: string | undefined; address?: string | undefined; postal_code?: string | undefined; tax_id?: string | undefined; bank_account?: string | undefined; bank_name?: string | undefined; subscription_type?: SubscriptionType | undefined; } | ((prevState: { ...; } | null) => { ...; } | null)'.
src/app/dashboard/biznis/account/page.tsx(247,56): error TS2339: Property 'hasActiveSubscription' does not exist on type '{ company_name?: string | undefined; address?: string | undefined; postal_code?: string | undefined; tax_id?: string | undefined; bank_account?: string | undefined; bank_name?: string | undefined; subscription_type?: SubscriptionType | undefined; }'.
src/app/dashboard/biznis/applications/[id]/page.tsx(144,24): error TS2345: Argument of type 'ApplicationWithDetails | null' is not assignable to parameter of type 'SetStateAction<ApplicationDetails | null>'.
  Type 'ApplicationWithDetails' is not assignable to type 'SetStateAction<ApplicationDetails | null>'.
    Type 'ApplicationWithDetails' is not assignable to type 'ApplicationDetails'.
      Types of property 'status' are incompatible.
        Type '"pending" | "accepted" | "rejected" | "completed" | null' is not assignable to type '"pending" | "accepted" | "rejected"'.
          Type 'null' is not assignable to type '"pending" | "accepted" | "rejected"'.
src/app/dashboard/biznis/applications/[id]/page.tsx(153,26): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'SetStateAction<{ total_paid: number; platform_fee: number; payment_amount: number; } | null>'.
src/app/dashboard/biznis/applications/[id]/page.tsx(266,63): error TS18047: 'application' is possibly 'null'.
src/app/dashboard/biznis/applications/[id]/page.tsx(268,22): error TS2345: Argument of type 'ApplicationWithDetails' is not assignable to parameter of type 'SetStateAction<ApplicationDetails | null>'.
  Type 'ApplicationWithDetails' is not assignable to type 'ApplicationDetails'.
    Types of property 'status' are incompatible.
      Type '"pending" | "accepted" | "rejected" | "completed" | null' is not assignable to type '"pending" | "accepted" | "rejected"'.
        Type 'null' is not assignable to type '"pending" | "accepted" | "rejected"'.
src/app/dashboard/biznis/applications/[id]/page.tsx(757,24): error TS2322: Type '{ children: (string | Element)[]; applicationId: string; proposedRate: number; onPaymentSuccess: () => Promise<void>; className: string; }' is not assignable to type 'IntrinsicAttributes & ApplicationPaymentButtonProps'.
  Property 'children' does not exist on type 'IntrinsicAttributes & ApplicationPaymentButtonProps'.
src/app/dashboard/biznis/applications/[id]/page.tsx(994,46): error TS2551: Property 'influencer_profile' does not exist on type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 8 more ...; updated_at: string | null; }'. Did you mean 'influencer_id'?
src/app/dashboard/biznis/applications/[id]/page.tsx(996,50): error TS2551: Property 'influencer_profile' does not exist on type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 8 more ...; updated_at: string | null; }'. Did you mean 'influencer_id'?
src/app/dashboard/biznis/applications/[id]/page.tsx(997,35): error TS2551: Property 'influencer_profile' does not exist on type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 8 more ...; updated_at: string | null; }'. Did you mean 'influencer_id'?
src/app/dashboard/biznis/applications/[id]/page.tsx(1009,46): error TS2551: Property 'influencer_profile' does not exist on type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 8 more ...; updated_at: string | null; }'. Did you mean 'influencer_id'?
src/app/dashboard/biznis/applications/[id]/page.tsx(1011,50): error TS2551: Property 'influencer_profile' does not exist on type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 8 more ...; updated_at: string | null; }'. Did you mean 'influencer_id'?
src/app/dashboard/biznis/applications/[id]/page.tsx(1012,35): error TS2551: Property 'influencer_profile' does not exist on type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 8 more ...; updated_at: string | null; }'. Did you mean 'influencer_id'?
src/app/dashboard/biznis/applications/page.tsx(81,27): error TS2345: Argument of type 'ApplicationCard[]' is not assignable to parameter of type 'SetStateAction<Application[]>'.
  Type 'ApplicationCard[]' is not assignable to type 'Application[]'.
    Type 'ApplicationCard' is missing the following properties from type 'Application': delivery_timeframe, proposal_text
src/app/dashboard/biznis/offers/[id]/page.tsx(62,18): error TS2448: Block-scoped variable 'loadOffer' used before its declaration.
src/app/dashboard/biznis/offers/[id]/page.tsx(62,18): error TS2454: Variable 'loadOffer' is used before being assigned.
src/app/dashboard/biznis/offers/[id]/page.tsx(62,29): error TS2448: Block-scoped variable 'loadJobCompletion' used before its declaration.
src/app/dashboard/biznis/offers/[id]/page.tsx(62,29): error TS2454: Variable 'loadJobCompletion' is used before being assigned.
src/app/dashboard/biznis/offers/[id]/page.tsx(76,28): error TS2448: Block-scoped variable 'handlePaymentSuccess' used before its declaration.
src/app/dashboard/biznis/offers/[id]/page.tsx(76,28): error TS2454: Variable 'handlePaymentSuccess' is used before being assigned.
src/app/dashboard/biznis/offers/[id]/page.tsx(92,28): error TS2345: Argument of type 'PaymentInfo | null' is not assignable to parameter of type 'SetStateAction<{ total_paid: number; platform_fee: number; payment_amount: number; } | null>'.
  Type 'PaymentInfo' is not assignable to type 'SetStateAction<{ total_paid: number; platform_fee: number; payment_amount: number; } | null>'.
    Type 'PaymentInfo' is missing the following properties from type '{ total_paid: number; platform_fee: number; payment_amount: number; }': total_paid, platform_fee, payment_amount
src/app/dashboard/biznis/offers/[id]/page.tsx(790,41): error TS2345: Argument of type 'string | number | true | { [key: string]: Json | undefined; } | Json[]' is not assignable to parameter of type 'string'.
  Type 'number' is not assignable to type 'string'.
src/app/dashboard/biznis/offers/page.tsx(42,18): error TS2448: Block-scoped variable 'loadOffers' used before its declaration.
src/app/dashboard/biznis/offers/page.tsx(42,18): error TS2454: Variable 'loadOffers' is used before being assigned.
src/app/dashboard/biznis/offers/page.tsx(151,19): error TS2739: Type '{ influencer: { id: string; username: string; full_name: string; public_display_name: string; avatar_url: string | null; }; id: string; influencer_id: string; title: string; description: string; ... 9 more ...; influencer_avatar_url: string | null; }' is missing the following properties from type 'DirectOfferWithDetails': business_id, updated_at
src/app/dashboard/biznis/packages/page.tsx(48,13): error TS2448: Block-scoped variable 'loadBusiness' used before its declaration.
src/app/dashboard/biznis/packages/page.tsx(48,13): error TS2454: Variable 'loadBusiness' is used before being assigned.
src/app/dashboard/biznis/packages/page.tsx(75,23): error TS2345: Argument of type 'UserSubscription | null' is not assignable to parameter of type 'SetStateAction<{ status: "active" | "cancelled" | "inactive"; plan_id?: string | undefined; plan_name?: string | undefined; } | null>'.
  Type 'UserSubscription' is not assignable to type 'SetStateAction<{ status: "active" | "cancelled" | "inactive"; plan_id?: string | undefined; plan_name?: string | undefined; } | null>'.
    Type 'UserSubscription' is not assignable to type '{ status: "active" | "cancelled" | "inactive"; plan_id?: string | undefined; plan_name?: string | undefined; }'.
      Types of property 'status' are incompatible.
        Type '"active" | "cancelled" | "past_due" | "unpaid" | "expired"' is not assignable to type '"active" | "cancelled" | "inactive"'.
          Type '"past_due"' is not assignable to type '"active" | "cancelled" | "inactive"'.
src/app/dashboard/biznis/packages/page.tsx(197,26): error TS2339: Property 'comingSoon' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; } | { name: string; price: number; duration: string; features: string[]; popular: boolean; planId: string; stripePriceId: string; limitations?: undefined; }'.
  Property 'comingSoon' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; }'.
src/app/dashboard/biznis/packages/page.tsx(210,27): error TS2339: Property 'description' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; } | { name: string; price: number; duration: string; features: string[]; popular: boolean; planId: string; stripePriceId: string; limitations?: undefined; }'.
  Property 'description' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; }'.
src/app/dashboard/biznis/packages/page.tsx(222,27): error TS2339: Property 'originalPrice' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; } | { name: string; price: number; duration: string; features: string[]; popular: boolean; planId: string; stripePriceId: string; limitations?: undefined; }'.
  Property 'originalPrice' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; }'.
src/app/dashboard/biznis/packages/page.tsx(224,32): error TS2339: Property 'originalPrice' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; } | { name: string; price: number; duration: string; features: string[]; popular: boolean; planId: string; stripePriceId: string; limitations?: undefined; }'.
  Property 'originalPrice' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; }'.
src/app/dashboard/biznis/packages/page.tsx(249,21): error TS2322: Type 'boolean | "" | null' is not assignable to type 'boolean | undefined'.
  Type 'null' is not assignable to type 'boolean | undefined'.
src/app/dashboard/biznis/page.tsx(24,13): error TS2448: Block-scoped variable 'loadBusiness' used before its declaration.
src/app/dashboard/biznis/page.tsx(24,13): error TS2454: Variable 'loadBusiness' is used before being assigned.
src/app/dashboard/biznis/page.tsx(73,50): error TS2339: Property 'profiles' does not exist on type '{ id: string; company_name: string; hasActiveSubscription?: boolean | undefined; }'.
src/app/dashboard/biznis/profile/page.tsx(129,13): error TS2448: Block-scoped variable 'loadData' used before its declaration.
src/app/dashboard/biznis/profile/page.tsx(129,13): error TS2454: Variable 'loadData' is used before being assigned.
src/app/dashboard/biznis/profile/page.tsx(143,18): error TS2345: Argument of type '{ address: string | null; age: number | null; avatar_url: string | null; average_rating: number | null; bank_account: string | null; bank_name: string | null; bio: string | null; ... 22 more ...; website_url: string | null; }' is not assignable to parameter of type 'SetStateAction<{ id: string; username: string; full_name?: string | undefined; bio?: string | undefined; city?: string | undefined; country?: string | undefined; website_url?: string | undefined; } | null>'.
  Type '{ address: string | null; age: number | null; avatar_url: string | null; average_rating: number | null; bank_account: string | null; bank_name: string | null; bio: string | null; ... 22 more ...; website_url: string | null; }' is not assignable to type '{ id: string; username: string; full_name?: string | undefined; bio?: string | undefined; city?: string | undefined; country?: string | undefined; website_url?: string | undefined; }'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
src/app/dashboard/biznis/profile/page.tsx(161,23): error TS2345: Argument of type '{ category_id: number; categories: { created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }; }[]' is not assignable to parameter of type 'SetStateAction<{ category_id: number; categories?: { icon?: string | undefined; name: string; } | undefined; }[]>'.
  Type '{ category_id: number; categories: { created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }; }[]' is not assignable to type '{ category_id: number; categories?: { icon?: string | undefined; name: string; } | undefined; }[]'.
    Type '{ category_id: number; categories: { created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }; }' is not assignable to type '{ category_id: number; categories?: { icon?: string | undefined; name: string; } | undefined; }'.
      The types of 'categories.icon' are incompatible between these types.
        Type 'string | null' is not assignable to type 'string | undefined'.
          Type 'null' is not assignable to type 'string | undefined'.
src/app/dashboard/biznis/profile/page.tsx(170,22): error TS2345: Argument of type '{ business_id: string | null; created_at: string | null; followers_count: number | null; handle: string; id: string; platform_id: number | null; updated_at: string | null; platforms: { ...; } | null; }[]' is not assignable to parameter of type 'SetStateAction<{ platform_id: number; handle?: string | undefined; followers_count?: number | undefined; }[]>'.
  Type '{ business_id: string | null; created_at: string | null; followers_count: number | null; handle: string; id: string; platform_id: number | null; updated_at: string | null; platforms: { ...; } | null; }[]' is not assignable to type '{ platform_id: number; handle?: string | undefined; followers_count?: number | undefined; }[]'.
    Type '{ business_id: string | null; created_at: string | null; followers_count: number | null; handle: string; id: string; platform_id: number | null; updated_at: string | null; platforms: { ...; } | null; }' is not assignable to type '{ platform_id: number; handle?: string | undefined; followers_count?: number | undefined; }'.
      Types of property 'platform_id' are incompatible.
        Type 'number | null' is not assignable to type 'number'.
          Type 'null' is not assignable to type 'number'.
src/app/dashboard/biznis/zavrseni-poslovi/page.tsx(32,25): error TS2345: Argument of type '{ business_to_influencer_review: { id: string; job_completion_id: string | null; rating: number | null; comment: string | null; created_at: string | null; review_type: string | null; } | null; ... 19 more ...; business_profile: { ...; } | null; }[]' is not assignable to parameter of type 'SetStateAction<JobCompletionWithDetails[]>'.
  Type '{ business_to_influencer_review: { id: string; job_completion_id: string | null; rating: number | null; comment: string | null; created_at: string | null; review_type: string | null; } | null; ... 19 more ...; business_profile: { ...; } | null; }[]' is not assignable to type 'JobCompletionWithDetails[]'.
    Type '{ business_to_influencer_review: { id: string; job_completion_id: string | null; rating: number | null; comment: string | null; created_at: string | null; review_type: string | null; } | null; ... 19 more ...; business_profile: { ...; } | null; }' is not assignable to type 'JobCompletionWithDetails'.
      Types of property 'campaign_application' are incompatible.
        Type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; business: { company_name: string; profiles: { username: string | null; avatar_url: string | null; }; }; }; } | null' is not assignable to type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; business?: { company_name: string; profiles?: { username: string; avatar_url: string | null; } | undefined; } | undefined; }; } | undefined'.
          Type 'null' is not assignable to type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; business?: { company_name: string; profiles?: { username: string; avatar_url: string | null; } | undefined; } | undefined; }; } | undefined'.
src/app/dashboard/campaigns/page.tsx(224,45): error TS2448: Block-scoped variable 'loadCampaigns' used before its declaration.
src/app/dashboard/campaigns/page.tsx(224,45): error TS2454: Variable 'loadCampaigns' is used before being assigned.
src/app/dashboard/campaigns/page.tsx(282,32): error TS2339: Property 'is_featured' does not exist on type 'CampaignDashboardCard'.
src/app/dashboard/campaigns/page.tsx(287,33): error TS2339: Property 'is_featured' does not exist on type 'CampaignDashboardCard'.
src/app/dashboard/campaigns/page.tsx(446,14): error TS2322: Type '{ children: (string | Element)[]; gradientVariant: "primary"; className: string; }' is not assignable to type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
  Property 'children' does not exist on type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
src/app/dashboard/campaigns/page.tsx(479,22): error TS2322: Type '{ children: (string | Element)[]; gradientVariant: "primary"; }' is not assignable to type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
  Property 'children' does not exist on type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
src/app/dashboard/campaigns/page.tsx(555,9): error TS2322: Type '{ isOpen: boolean; onClose: () => void; campaign: { id: string; title: string; business_id: string; } | null; onSuccess: () => void; }' is not assignable to type 'IntrinsicAttributes & PromoteCampaignModalProps'.
  Property 'onSuccess' does not exist on type 'IntrinsicAttributes & PromoteCampaignModalProps'.
src/app/dashboard/campaigns/test/campaigns-table.tsx(12,8): error TS2307: Cannot find module '@/components/ui/table' or its corresponding type declarations.
src/app/dashboard/campaigns/test/page.tsx(161,45): error TS2339: Property 'business_id' does not exist on type 'Campaign'.
src/app/dashboard/campaigns/test/page.tsx(200,45): error TS2448: Block-scoped variable 'loadCampaigns' used before its declaration.
src/app/dashboard/campaigns/test/page.tsx(200,45): error TS2454: Variable 'loadCampaigns' is used before being assigned.
src/app/dashboard/campaigns/test/page.tsx(241,36): error TS2345: Argument of type '(p: Platform) => { name: string; icon: string; content_types: string[]; }' is not assignable to parameter of type '(value: PlatformWithInfo, index: number, array: PlatformWithInfo[]) => { name: string; icon: string; content_types: string[]; }'.
  Types of parameters 'p' and 'value' are incompatible.
    Property 'content_types' is missing in type 'PlatformWithInfo' but required in type 'Platform'.
src/app/dashboard/campaigns/test/page.tsx(251,32): error TS2339: Property 'is_featured' does not exist on type '{ platforms: { name: string; icon: string; content_types: string[]; }[]; id: string; title: string; description: string; budget: number; status: "completed" | "draft" | "active" | "paused" | "cancelled"; created_at: string; content_types: string[]; application_count: number; }'.
src/app/dashboard/campaigns/test/page.tsx(255,33): error TS2339: Property 'is_featured' does not exist on type '{ platforms: { name: string; icon: string; content_types: string[]; }[]; id: string; title: string; description: string; budget: number; status: "completed" | "draft" | "active" | "paused" | "cancelled"; created_at: string; content_types: string[]; application_count: number; }'.
src/app/dashboard/campaigns/test/page.tsx(335,14): error TS2322: Type '{ children: (string | Element)[]; gradientVariant: "primary"; className: string; }' is not assignable to type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
  Property 'children' does not exist on type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
src/app/dashboard/campaigns/test/page.tsx(368,22): error TS2322: Type '{ children: (string | Element)[]; gradientVariant: "primary"; }' is not assignable to type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
  Property 'children' does not exist on type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
src/app/dashboard/chat/permissions/page.tsx(21,26): error TS2554: Expected 1 arguments, but got 0.
src/app/dashboard/influencer/account/page.tsx(102,13): error TS2448: Block-scoped variable 'loadProfile' used before its declaration.
src/app/dashboard/influencer/account/page.tsx(102,13): error TS2454: Variable 'loadProfile' is used before being assigned.
src/app/dashboard/influencer/account/page.tsx(102,26): error TS2448: Block-scoped variable 'checkSubscription' used before its declaration.
src/app/dashboard/influencer/account/page.tsx(102,26): error TS2454: Variable 'checkSubscription' is used before being assigned.
src/app/dashboard/influencer/applications/[id]/page.tsx(71,24): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'SetStateAction<Record<string, unknown> | null>'.
src/app/dashboard/influencer/applications/[id]/page.tsx(85,24): error TS2345: Argument of type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 11 more ...; campaign_application: { ...; } | null; } | null' is not assignable to parameter of type 'SetStateAction<JobCompletionWithDetails | null>'.
  Type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 11 more ...; campaign_application: { ...; } | null; }' is not assignable to type 'SetStateAction<JobCompletionWithDetails | null>'.
    Type '{ approved_by: string | null; business_id: string | null; business_notes: string | null; campaign_application_id: string | null; created_at: string | null; direct_offer_id: string | null; ... 11 more ...; campaign_application: { ...; } | null; }' is not assignable to type 'JobCompletionWithDetails'.
      Types of property 'campaign_application' are incompatible.
        Type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; }; } | null' is not assignable to type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; business?: { company_name: string; profiles?: { username: string; avatar_url: string | null; } | undefined; } | undefined; }; } | undefined'.
          Type 'null' is not assignable to type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; business?: { company_name: string; profiles?: { username: string; avatar_url: string | null; } | undefined; } | undefined; }; } | undefined'.
src/app/dashboard/influencer/applications/[id]/page.tsx(114,27): error TS2339: Property 'message' does not exist on type '{}'.
src/app/dashboard/influencer/applications/[id]/page.tsx(292,58): error TS2551: Property 'profiles' does not exist on type '{ active_campaigns_count: number | null; budget_range: string | null; company_name: string; company_size: string | null; contact_person_name: string | null; created_at: string | null; ... 5 more ...; updated_at: string | null; } & { ...; }'. Did you mean 'profile'?
src/app/dashboard/influencer/applications/[id]/page.tsx(495,50): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/app/dashboard/influencer/applications/[id]/page.tsx(497,36): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/app/dashboard/influencer/applications/[id]/page.tsx(512,33): error TS2339: Property 'rejection_reason' does not exist on type 'ApplicationWithDetails'.
src/app/dashboard/influencer/applications/[id]/page.tsx(516,40): error TS2339: Property 'rejection_reason' does not exist on type 'ApplicationWithDetails'.
src/app/dashboard/influencer/applications/[id]/page.tsx(794,56): error TS2551: Property 'profiles' does not exist on type '{ active_campaigns_count: number | null; budget_range: string | null; company_name: string; company_size: string | null; contact_person_name: string | null; created_at: string | null; ... 5 more ...; updated_at: string | null; } & { ...; }'. Did you mean 'profile'?
src/app/dashboard/influencer/applications/[id]/page.tsx(806,56): error TS2551: Property 'profiles' does not exist on type '{ active_campaigns_count: number | null; budget_range: string | null; company_name: string; company_size: string | null; contact_person_name: string | null; created_at: string | null; ... 5 more ...; updated_at: string | null; } & { ...; }'. Did you mean 'profile'?
src/app/dashboard/influencer/applications/[id]/page.tsx(810,56): error TS2551: Property 'profiles' does not exist on type '{ active_campaigns_count: number | null; budget_range: string | null; company_name: string; company_size: string | null; contact_person_name: string | null; created_at: string | null; ... 5 more ...; updated_at: string | null; } & { ...; }'. Did you mean 'profile'?
src/app/dashboard/influencer/applications/[id]/page.tsx(815,49): error TS2551: Property 'profiles' does not exist on type '{ active_campaigns_count: number | null; budget_range: string | null; company_name: string; company_size: string | null; contact_person_name: string | null; created_at: string | null; ... 5 more ...; updated_at: string | null; } & { ...; }'. Did you mean 'profile'?
src/app/dashboard/influencer/applications/[id]/page.tsx(817,70): error TS2551: Property 'profiles' does not exist on type '{ active_campaigns_count: number | null; budget_range: string | null; company_name: string; company_size: string | null; contact_person_name: string | null; created_at: string | null; ... 5 more ...; updated_at: string | null; } & { ...; }'. Did you mean 'profile'?
src/app/dashboard/influencer/applications/page.tsx(47,11): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/app/dashboard/influencer/applications/page.tsx(50,11): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/app/dashboard/influencer/applications/page.tsx(55,23): error TS2345: Argument of type '{ applied_at: string; audience_insights: string; business_username: string; campaign_budget: number; campaign_business_id: string; campaign_id: string; campaign_title: string; delivery_timeframe: string; ... 5 more ...; status: string; }[]' is not assignable to parameter of type 'SetStateAction<Application[]>'.
  Type '{ applied_at: string; audience_insights: string; business_username: string; campaign_budget: number; campaign_business_id: string; campaign_id: string; campaign_title: string; delivery_timeframe: string; ... 5 more ...; status: string; }[]' is not assignable to type 'Application[]'.
    Type '{ applied_at: string; audience_insights: string; business_username: string; campaign_budget: number; campaign_business_id: string; campaign_id: string; campaign_title: string; delivery_timeframe: string; ... 5 more ...; status: string; }' is not assignable to type 'Application'.
      Types of property 'status' are incompatible.
        Type 'string' is not assignable to type '"pending" | "accepted" | "rejected"'.
src/app/dashboard/influencer/applications/page.tsx(85,27): error TS2339: Property 'message' does not exist on type '{}'.
src/app/dashboard/influencer/offers/[id]/page.tsx(327,39): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/app/dashboard/influencer/offers/[id]/page.tsx(328,41): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/app/dashboard/influencer/offers/[id]/page.tsx(371,25): error TS2322: Type 'unknown' is not assignable to type 'ReactNode'.
src/app/dashboard/influencer/offers/[id]/page.tsx(376,42): error TS2769: No overload matches this call.
  Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.
    Argument of type '{}' is not assignable to parameter of type 'string | number | Date'.
  Overload 2 of 4, '(value: string | number): Date', gave the following error.
    Argument of type '{}' is not assignable to parameter of type 'string | number'.
src/app/dashboard/influencer/offers/[id]/page.tsx(383,25): error TS2322: Type 'unknown' is not assignable to type 'ReactNode'.
src/app/dashboard/influencer/offers/[id]/page.tsx(388,35): error TS2345: Argument of type '{}' is not assignable to parameter of type 'string'.
src/app/dashboard/influencer/offers/[id]/page.tsx(457,39): error TS2322: Type '{}' is not assignable to type 'ReactNode'.
src/app/dashboard/influencer/offers/[id]/page.tsx(466,25): error TS2322: Type 'unknown' is not assignable to type 'ReactNode'.
src/app/dashboard/influencer/offers/[id]/page.tsx(472,31): error TS2322: Type '{}' is not assignable to type 'ReactNode'.
src/app/dashboard/influencer/offers/[id]/page.tsx(749,20): error TS2339: Property 'deliverables' does not exist on type 'DirectOfferWithDetails'.
src/app/dashboard/influencer/offers/[id]/page.tsx(760,28): error TS2339: Property 'deliverables' does not exist on type 'DirectOfferWithDetails'.
src/app/dashboard/influencer/offers/[id]/page.tsx(912,28): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/app/dashboard/influencer/offers/[id]/page.tsx(913,28): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/app/dashboard/influencer/offers/[id]/page.tsx(921,24): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/app/dashboard/influencer/offers/[id]/page.tsx(922,25): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/app/dashboard/influencer/offers/[id]/page.tsx(926,25): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/app/dashboard/influencer/offers/[id]/page.tsx(931,42): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/app/dashboard/influencer/offers/page.tsx(51,9): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/app/dashboard/influencer/offers/page.tsx(54,9): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/app/dashboard/influencer/offers/page.tsx(76,19): error TS2345: Argument of type '{ businesses: { id: unknown; company_name: unknown; industry: unknown; categories: never[]; profiles: { avatar_url: unknown; username: unknown; }; }; }[]' is not assignable to parameter of type 'SetStateAction<DirectOfferWithDetails[]>'.
  Type '{ businesses: { id: unknown; company_name: unknown; industry: unknown; categories: never[]; profiles: { avatar_url: unknown; username: unknown; }; }; }[]' is not assignable to type 'DirectOfferWithDetails[]'.
    Type '{ businesses: { id: unknown; company_name: unknown; industry: unknown; categories: never[]; profiles: { avatar_url: unknown; username: unknown; }; }; }' is missing the following properties from type 'DirectOfferWithDetails': id, business_id, influencer_id, title, and 21 more.
src/app/dashboard/influencer/offers/page.tsx(164,53): error TS2741: Property 'influencer' is missing in type 'DirectOfferWithDetails' but required in type 'import("C:/Users/<USER>/Desktop/DEV/Software/1 Influencer Marketing/influencer-platform 2/src/lib/offers").DirectOfferWithDetails'.
src/app/dashboard/influencer/packages/page.tsx(64,7): error TS2552: Cannot find name 'setInfluencer'. Did you mean 'getInfluencer'?
src/app/dashboard/influencer/packages/page.tsx(174,48): error TS2554: Expected 0 arguments, but got 1.
src/app/dashboard/influencer/packages/page.tsx(186,26): error TS2339: Property 'comingSoon' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; } | { name: string; price: number; duration: string; features: string[]; popular: boolean; planId: string; stripePriceId: string; limitations?: undefined; }'.
  Property 'comingSoon' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; }'.
src/app/dashboard/influencer/packages/page.tsx(199,27): error TS2339: Property 'description' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; } | { name: string; price: number; duration: string; features: string[]; popular: boolean; planId: string; stripePriceId: string; limitations?: undefined; }'.
  Property 'description' does not exist on type '{ name: string; price: number; duration: string; features: string[]; limitations: string[]; planId: null; stripePriceId: null; popular: boolean; }'.
src/app/dashboard/influencer/packages/page.tsx(233,21): error TS2322: Type 'boolean | "" | null' is not assignable to type 'boolean | undefined'.
  Type 'null' is not assignable to type 'boolean | undefined'.
src/app/dashboard/influencer/pricing/page.tsx(107,20): error TS2345: Argument of type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Platform[]>'.
  Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to type 'Platform[]'.
    Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }' is not assignable to type 'Platform'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/app/dashboard/influencer/pricing/page.tsx(117,23): error TS2345: Argument of type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<ContentType[]>'.
  Type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }[]' is not assignable to type 'ContentType[]'.
    Type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }' is not assignable to type 'ContentType'.
      Types of property 'platform_id' are incompatible.
        Type 'number | null' is not assignable to type 'number'.
          Type 'null' is not assignable to type 'number'.
src/app/dashboard/influencer/pricing/page.tsx(466,27): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/app/dashboard/influencer/profile/page.tsx(859,29): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/app/dashboard/influencer/zavrseni-poslovi/page.tsx(35,25): error TS2345: Argument of type '{ business_to_influencer_review: { id: string; job_completion_id: string | null; rating: number | null; comment: string | null; created_at: string | null; review_type: string | null; } | null; ... 19 more ...; business_profile: { ...; } | null; }[]' is not assignable to parameter of type 'SetStateAction<JobCompletionWithDetails[]>'.
  Type '{ business_to_influencer_review: { id: string; job_completion_id: string | null; rating: number | null; comment: string | null; created_at: string | null; review_type: string | null; } | null; ... 19 more ...; business_profile: { ...; } | null; }[]' is not assignable to type 'JobCompletionWithDetails[]'.
    Type '{ business_to_influencer_review: { id: string; job_completion_id: string | null; rating: number | null; comment: string | null; created_at: string | null; review_type: string | null; } | null; ... 19 more ...; business_profile: { ...; } | null; }' is not assignable to type 'JobCompletionWithDetails'.
      Types of property 'campaign_application' are incompatible.
        Type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; business: { company_name: string; profiles: { username: string | null; avatar_url: string | null; }; }; }; } | null' is not assignable to type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; business?: { company_name: string; profiles?: { username: string; avatar_url: string | null; } | undefined; } | undefined; }; } | undefined'.
          Type 'null' is not assignable to type '{ id: string; proposed_rate: number; campaign: { id: string; title: string; description: string; business?: { company_name: string; profiles?: { username: string; avatar_url: string | null; } | undefined; } | undefined; }; } | undefined'.
src/app/dashboard/notifications/page.tsx(104,5): error TS2322: Type '(offset: number, limit: number) => Promise<{ data: { created_at: string | null; data: Json | null; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]; hasMore: boolean; error: null; } | { ...; }>' is not assignable to type '(offset: number, limit: number) => Promise<{ data: Notification[]; hasMore: boolean; error?: unknown; }>'.
  Type 'Promise<{ data: { created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]; hasMore: boolean; error: null; } | { ...; }>' is not assignable to type 'Promise<{ data: Notification[]; hasMore: boolean; error?: unknown; }>'.
    Type '{ data: { created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]; hasMore: boolean; error: null; } | { ...; }' is not assignable to type '{ data: Notification[]; hasMore: boolean; error?: unknown; }'.
      Type '{ data: { created_at: string | null; data: Json | null; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]; hasMore: boolean; error: null; }' is not assignable to type '{ data: Notification[]; hasMore: boolean; error?: unknown; }'.
        Types of property 'data' are incompatible.
          Type '{ created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]' is not assignable to type 'Notification[]'.
            Type '{ created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }' is not assignable to type 'Notification'.
              Types of property 'data' are incompatible.
                Type 'Json' is not assignable to type 'Record<string, unknown>'.
                  Type 'null' is not assignable to type 'Record<string, unknown>'.
src/app/dashboard/notifications/page.tsx(312,11): error TS2322: Type 'Dispatch<SetStateAction<"read" | "all" | "unread">>' is not assignable to type '(value: string) => void'.
  Types of parameters 'value' and 'value' are incompatible.
    Type 'string' is not assignable to type 'SetStateAction<"read" | "all" | "unread">'.
src/app/influencer/[username]/InfluencerProfileClient.tsx(376,54): error TS2339: Property 'category_name' does not exist on type '{ id: number; name: string; icon: string; is_primary: boolean; }'.
src/app/influencer/[username]/InfluencerProfileClient.tsx(622,50): error TS2339: Property 'auto_generated_name' does not exist on type '{ platform_id: number; platform_name: string; content_type_id: number; content_type_name: string; price: number; currency: string; }'.
src/app/influencer/[username]/InfluencerProfileClient.tsx(683,54): error TS2339: Property 'auto_generated_name' does not exist on type '{ platform_id: number; platform_name: string; content_type_id: number; content_type_name: string; price: number; currency: string; }'.
src/app/influencer/[username]/page.tsx(62,7): error TS2322: Type '{ id: string; username: string | null; full_name: string; avatar_url: string | null; bio: string | null; location: string; created_at: string | null; gender: string | null; age: number | null; ... 11 more ...; total_reviews: number; }' is not assignable to type 'PublicInfluencerProfile'.
  Types of property 'username' are incompatible.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
src/app/marketplace/campaigns/page.tsx(103,5): error TS2322: Type '(offset: number, limit: number) => Promise<{ data: never[] | { id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { ...; }; }[]; hasMore: boolean; error: null; } | { ...; }>' is not assignable to type '(offset: number, limit: number) => Promise<{ data: Campaign[]; hasMore: boolean; error?: unknown; }>'.
  Type 'Promise<{ data: never[] | { id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { ...; }; }[]; hasMore: boolean; error: null; } | { ...; }>' is not assignable to type 'Promise<{ data: Campaign[]; hasMore: boolean; error?: unknown; }>'.
    Type '{ data: never[] | { id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }[]; hasMore: boolean; error: null; } | { ...; }' is not assignable to type '{ data: Campaign[]; hasMore: boolean; error?: unknown; }'.
      Type '{ data: never[] | { id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }[]; hasMore: boolean; error: null; }' is not assignable to type '{ data: Campaign[]; hasMore: boolean; error?: unknown; }'.
        Types of property 'data' are incompatible.
          Type 'never[] | { id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }[]' is not assignable to type 'Campaign[]'.
            Type '{ id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }[]' is not assignable to type 'Campaign[]'.
              Type '{ id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }' is missing the following properties from type 'Campaign': status, location, application_deadline, min_followers, and 6 more.
src/app/marketplace/campaigns/page.tsx(126,28): error TS2345: Argument of type 'never[] | { id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }[]' is not assignable to parameter of type 'SetStateAction<Campaign[]>'.
  Type '{ id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }[]' is not assignable to type 'SetStateAction<Campaign[]>'.
    Type '{ id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }[]' is not assignable to type 'Campaign[]'.
      Type '{ id: string; title: string; description: string; budget: number; is_featured: boolean; created_at: string; platforms: Json[]; applications_count: number; businesses: { company_name: string; }; }' is missing the following properties from type 'Campaign': status, location, application_deadline, min_followers, and 6 more.
src/app/marketplace/campaigns/page.tsx(164,11): error TS2322: Type 'unknown' is not assignable to type 'ReactNode'.
src/app/marketplace/influencers/page.tsx(75,11): error TS2322: Type '"created_at"' is not assignable to type '"relevance" | "price_asc" | "price_desc" | "followers_desc" | "newest" | undefined'.
src/app/marketplace/influencers/page.tsx(123,11): error TS2322: Type 'unknown' is not assignable to type 'ReactNode'.
src/app/marketplace/influencers/page.tsx(166,21): error TS2322: Type '{ key: string; influencer: InfluencerSearchResult; filters: {}; }' is not assignable to type 'IntrinsicAttributes & InfluencerCardProps'.
  Property 'filters' does not exist on type 'IntrinsicAttributes & InfluencerCardProps'.
src/app/prijava/page.tsx(69,11): error TS18046: 'authData' is of type 'unknown'.
src/app/profil/edit/page.tsx(134,53): error TS2339: Property 'location' does not exist on type '{ address: string | null; age: number | null; avatar_url: string | null; average_rating: number | null; bank_account: string | null; bank_name: string | null; bio: string | null; ... 22 more ...; website_url: string | null; }'.
src/app/profil/edit/page.tsx(136,41): error TS2339: Property 'gender' does not exist on type '{ created_at: string | null; custom_offers_enabled: boolean | null; engagement_rate: number | null; id: string; is_verified: boolean | null; portfolio_urls: string[] | null; subscription_type: string | null; updated_at: string | null; profiles: { ...; }; }'.
src/app/profil/edit/page.tsx(137,38): error TS2339: Property 'age' does not exist on type '{ created_at: string | null; custom_offers_enabled: boolean | null; engagement_rate: number | null; id: string; is_verified: boolean | null; portfolio_urls: string[] | null; subscription_type: string | null; updated_at: string | null; profiles: { ...; }; }'.
src/app/profil/edit/page.tsx(179,9): error TS2353: Object literal may only specify known properties, and 'location' does not exist in type '{ address?: string | null | undefined; age?: number | null | undefined; avatar_url?: string | null | undefined; average_rating?: number | null | undefined; bank_account?: string | null | undefined; ... 24 more ...; website_url?: string | ... 1 more ... | undefined; }'.
src/app/profil/edit/page.tsx(194,9): error TS2353: Object literal may only specify known properties, and 'gender' does not exist in type '{ created_at?: string | null | undefined; custom_offers_enabled?: boolean | null | undefined; engagement_rate?: number | null | undefined; id?: string | undefined; is_verified?: boolean | null | undefined; portfolio_urls?: string[] | ... 1 more ... | undefined; subscription_type?: string | ... 1 more ... | undefined...'.
src/app/profil/kreiranje/biznis/onboarding/page.tsx(313,61): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }, options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Type 'number | null' is not assignable to type 'string | null | undefined'.
      Type 'number' is not assignable to type 'string'.
  Overload 2 of 2, '(values: { active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }[], options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Object literal may only specify known properties, and 'id' does not exist in type '{ active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }[]'.
src/app/profil/kreiranje/biznis/onboarding/page.tsx(367,10): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }, options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Type 'number | null' is not assignable to type 'string | null | undefined'.
      Type 'number' is not assignable to type 'string'.
  Overload 2 of 2, '(values: { active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }[], options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Object literal may only specify known properties, and 'id' does not exist in type '{ active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }[]'.
src/app/profil/kreiranje/influencer/onboarding/page.tsx(155,29): error TS2345: Argument of type '(prev: Partial<OnboardingData>) => { username: string; age: number | null; gender: string; categories: number[]; country: string; city: string; bio: string; socialMedia: { ...; }[]; packages?: { ...; }[] | undefined; }' is not assignable to parameter of type 'SetStateAction<Partial<OnboardingData>>'.
  Type '(prev: Partial<OnboardingData>) => { username: string; age: number | null; gender: string; categories: number[]; country: string; city: string; bio: string; socialMedia: { ...; }[]; packages?: { ...; }[] | undefined; }' is not assignable to type '(prevState: Partial<OnboardingData>) => Partial<OnboardingData>'.
    Call signature return types '{ username: string; age: number | null; gender: string; categories: number[]; country: string; city: string; bio: string; socialMedia: { platform: "instagram" | "youtube" | "tiktok"; handle: string | null; followers: number | null; }[]; packages?: { ...; }[] | undefined; }' and 'Partial<OnboardingData>' are incompatible.
      The types of 'socialMedia' are incompatible between these types.
        Type '{ platform: "instagram" | "youtube" | "tiktok"; handle: string | null; followers: number | null; }[]' is not assignable to type '{ platform: "instagram" | "youtube" | "tiktok"; handle: string; followers: number; }[]'.
          Type '{ platform: "instagram" | "youtube" | "tiktok"; handle: string | null; followers: number | null; }' is not assignable to type '{ platform: "instagram" | "youtube" | "tiktok"; handle: string; followers: number; }'.
            Types of property 'handle' are incompatible.
              Type 'string | null' is not assignable to type 'string'.
                Type 'null' is not assignable to type 'string'.
src/app/profil/kreiranje/influencer/onboarding/page.tsx(381,7): error TS18048: 'onboardingData.categories' is possibly 'undefined'.
src/app/profil/kreiranje/influencer/onboarding/page.tsx(466,13): error TS2322: Type 'number | null | undefined' is not assignable to type 'number | null'.
  Type 'undefined' is not assignable to type 'number | null'.
src/app/registracija/business/page.tsx(91,11): error TS18047: 'authData' is possibly 'null'.
src/app/registracija/business/page.tsx(91,29): error TS18047: 'authData' is possibly 'null'.
src/app/registracija/influencer/page.tsx(83,11): error TS18047: 'authData' is possibly 'null'.
src/app/registracija/influencer/page.tsx(83,29): error TS18047: 'authData' is possibly 'null'.
src/components/campaigns/campaign-application-form.tsx(324,14): error TS2322: Type '{ children: (string | false | Element)[]; type: string; disabled: boolean; gradientVariant: "primary"; }' is not assignable to type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
  Property 'children' does not exist on type 'IntrinsicAttributes & GradientButtonProps & RefAttributes<HTMLButtonElement>'.
src/components/campaigns/campaign-filters.tsx(71,48): error TS2345: Argument of type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Platform[]>'.
  Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to type 'Platform[]'.
    Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }' is not assignable to type 'Platform'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/campaigns/campaign-filters.tsx(72,50): error TS2345: Argument of type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Category[]>'.
  Type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }[]' is not assignable to type 'Category[]'.
    Type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }' is not assignable to type 'Category'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/campaigns/CampaignCard.tsx(137,44): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/components/campaigns/CampaignCard.tsx(139,31): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/components/campaigns/CampaignCard.tsx(171,29): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/components/campaigns/create-campaign-form.tsx(157,5): error TS2322: Type 'Resolver<{ title: string; budget: number; description: string; selectedCategories: number[]; gender?: "all" | "male" | "female" | undefined; ageRangeMin?: number | undefined; ageRangeMax?: number | undefined; ... 5 more ...; isFeatured?: boolean | undefined; }, any, { ...; }>' is not assignable to type 'Resolver<{ title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }, any, { ...; }>'.
  Types of parameters 'options' and 'options' are incompatible.
    Type 'ResolverOptions<{ title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }>' is not assignable to type 'ResolverOptions<{ title: string; budget: number; description: string; selectedCategories: number[]; gender?: "all" | "male" | "female" | undefined; ageRangeMin?: number | undefined; ageRangeMax?: number | undefined; ... 5 more ...; isFeatured?: boolean | undefined; }>'.
      Type '"paid" | "barter" | "hybrid" | undefined' is not assignable to type '"paid" | "barter" | "hybrid"'.
        Type 'undefined' is not assignable to type '"paid" | "barter" | "hybrid"'.
src/components/campaigns/create-campaign-form.tsx(183,24): error TS2345: Argument of type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Platform[]>'.
  Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to type 'Platform[]'.
    Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }' is not assignable to type 'Platform'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/campaigns/create-campaign-form.tsx(197,50): error TS2339: Property 'content_types' does not exist on type 'Partial<{ title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }>'.
src/components/campaigns/create-campaign-form.tsx(198,61): error TS2339: Property 'content_types' does not exist on type 'Partial<{ title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }>'.
src/components/campaigns/create-campaign-form.tsx(204,50): error TS2345: Argument of type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Category[]>'.
  Type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }[]' is not assignable to type 'Category[]'.
    Type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }' is not assignable to type 'Category'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/campaigns/create-campaign-form.tsx(409,30): error TS2345: Argument of type '{ business_id: string; title: string; description: string; budget: number; content_types: string[]; collaboration_type: "paid" | "barter" | "hybrid"; gender: "male" | "female" | null | undefined; ... 7 more ...; status: "draft"; }' is not assignable to parameter of type '{ activated_at?: string | null | undefined; additional_notes?: string | null | undefined; age_range_max?: number | null | undefined; age_range_min?: number | null | undefined; application_deadline?: string | ... 1 more ... | undefined; ... 34 more ...; views_count?: number | ... 1 more ... | undefined; }'.
  Types of property 'content_types' are incompatible.
    Type 'string[]' is not assignable to type '("post" | "story" | "reel" | "video" | "blog")[]'.
      Type 'string' is not assignable to type '"post" | "story" | "reel" | "video" | "blog"'.
src/components/campaigns/create-campaign-form.tsx(466,9): error TS2349: This expression is not callable.
  Type 'never' has no call signatures.
src/components/campaigns/create-campaign-form.tsx(480,34): error TS2345: Argument of type '(data: CampaignForm) => Promise<void>' is not assignable to parameter of type 'SubmitHandler<TFieldValues>'.
  Types of parameters 'data' and 'data' are incompatible.
    Type 'TFieldValues' is not assignable to type '{ title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }'.
      Type 'FieldValues' is missing the following properties from type '{ title: string; budget: number; description: string; selectedCategories: number[]; collaborationType: "paid" | "barter" | "hybrid"; showBusinessName: boolean; isFeatured: boolean; gender?: "all" | ... 2 more ... | undefined; ... 4 more ...; additionalNotes?: string | undefined; }': title, budget, description, selectedCategories, and 3 more.
src/components/campaigns/create-campaign-form.tsx(1023,42): error TS2304: Cannot find name 'externalLoading'.
src/components/campaigns/create-campaign-form.tsx(1026,34): error TS2304: Cannot find name 'externalLoading'.
src/components/campaigns/create-campaign-form.tsx(1029,20): error TS2304: Cannot find name 'submitButtonText'.
src/components/campaigns/InfluencerOfferCard.tsx(90,22): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/components/campaigns/InfluencerOfferCard.tsx(91,22): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/components/campaigns/InfluencerOfferCard.tsx(99,18): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/components/campaigns/InfluencerOfferCard.tsx(100,19): error TS18048: 'offer.businesses' is possibly 'undefined'.
src/components/chat/ChatContextBar.tsx(129,25): error TS2345: Argument of type '{ id: string; title: string; budget: number | null; end_date: string | null; status: "pending" | "accepted" | "rejected" | "completed" | null; company_name: string; }' is not assignable to parameter of type 'SetStateAction<CampaignData | null>'.
  Type '{ id: string; title: string; budget: number | null; end_date: string | null; status: "pending" | "accepted" | "rejected" | "completed" | null; company_name: string; }' is not assignable to type 'CampaignData'.
    Types of property 'budget' are incompatible.
      Type 'number | null' is not assignable to type 'number'.
        Type 'null' is not assignable to type 'number'.
src/components/chat/ChatPermissionStatus.tsx(61,24): error TS2345: Argument of type 'boolean | null' is not assignable to parameter of type 'SetStateAction<boolean>'.
  Type 'null' is not assignable to type 'SetStateAction<boolean>'.
src/components/chat/ChatRoom.tsx(98,25): error TS2345: Argument of type '(prev: ChatMessage[]) => (ChatMessage | { created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; ... 7 more ...; sender_profile: { ...; }; })[]' is not assignable to parameter of type 'SetStateAction<ChatMessage[]>'.
  Type '(prev: ChatMessage[]) => (ChatMessage | { created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; ... 7 more ...; sender_profile: { ...; }; })[]' is not assignable to type '(prevState: ChatMessage[]) => ChatMessage[]'.
    Type '(ChatMessage | { created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; file_url: string | null; ... 6 more ...; sender_profile: { ...; }; })[]' is not assignable to type 'ChatMessage[]'.
      Type 'ChatMessage | { created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; file_url: string | null; ... 6 more ...; sender_profile: { ...; }; }' is not assignable to type 'ChatMessage'.
        Type '{ created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; file_url: string | null; id: string; message_text: string | null; ... 4 more ...; sender_profile: { ...; }; }' is not assignable to type 'ChatMessage'.
          The types of 'sender_profile.full_name' are incompatible between these types.
            Type 'string | null' is not assignable to type 'string'.
              Type 'null' is not assignable to type 'string'.
src/components/chat/ChatRoom.tsx(162,7): error TS2741: Property 'public_display_name' is missing in type '{ full_name: any; username: any; avatar_url: any; }' but required in type '{ full_name: string; public_display_name: string | null; username: string; avatar_url: string | null; user_type?: string | undefined; }'.
src/components/chat/ChatRoom.tsx(387,10): error TS2304: Cannot find name 'isTyping'.
src/components/job-completion/CampaignJobSubmissionForm.tsx(88,9): error TS2345: Argument of type 'null' is not assignable to parameter of type 'unknown[] | undefined'.
src/components/job-completion/DirectOfferJobSubmissionForm.tsx(88,9): error TS2345: Argument of type 'null' is not assignable to parameter of type 'unknown[] | undefined'.
src/components/job-completion/JobCompletionCard.tsx(107,9): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
src/components/job-completion/JobSubmissionForm.tsx(81,9): error TS2345: Argument of type 'null' is not assignable to parameter of type 'unknown[] | undefined'.
src/components/marketplace/horizontal-filters.tsx(42,13): error TS2551: Property 'min_price' does not exist on type 'SearchFilters'. Did you mean 'minPrice'?
src/components/marketplace/horizontal-filters.tsx(43,13): error TS2551: Property 'max_price' does not exist on type 'SearchFilters'. Did you mean 'maxPrice'?
src/components/marketplace/horizontal-filters.tsx(46,13): error TS2551: Property 'min_age' does not exist on type 'SearchFilters'. Did you mean 'minAge'?
src/components/marketplace/horizontal-filters.tsx(47,13): error TS2551: Property 'max_age' does not exist on type 'SearchFilters'. Did you mean 'maxAge'?
src/components/marketplace/horizontal-filters.tsx(92,27): error TS2352: Conversion of type 'string | number | boolean | number[] | undefined' to type 'string[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'number[]' is not comparable to type 'string[]'.
    Type 'number' is not comparable to type 'string'.
src/components/marketplace/horizontal-filters.tsx(102,19): error TS2345: Argument of type '"min_price"' is not assignable to parameter of type 'keyof SearchFilters'.
src/components/marketplace/horizontal-filters.tsx(103,19): error TS2345: Argument of type '"max_price"' is not assignable to parameter of type 'keyof SearchFilters'.
src/components/marketplace/horizontal-filters.tsx(108,19): error TS2345: Argument of type '"min_age"' is not assignable to parameter of type 'keyof SearchFilters'.
src/components/marketplace/horizontal-filters.tsx(109,19): error TS2345: Argument of type '"max_age"' is not assignable to parameter of type 'keyof SearchFilters'.
src/components/marketplace/horizontal-filters.tsx(117,17): error TS2551: Property 'content_types' does not exist on type 'SearchFilters'. Did you mean 'contentTypes'?
src/components/marketplace/horizontal-filters.tsx(117,57): error TS2551: Property 'content_types' does not exist on type 'SearchFilters'. Did you mean 'contentTypes'?
src/components/marketplace/horizontal-filters.tsx(118,17): error TS2551: Property 'min_price' does not exist on type 'SearchFilters'. Did you mean 'minPrice'?
src/components/marketplace/horizontal-filters.tsx(118,52): error TS2551: Property 'max_price' does not exist on type 'SearchFilters'. Did you mean 'maxPrice'?
src/components/marketplace/horizontal-filters.tsx(121,17): error TS2551: Property 'min_age' does not exist on type 'SearchFilters'. Did you mean 'minAge'?
src/components/marketplace/horizontal-filters.tsx(121,50): error TS2551: Property 'max_age' does not exist on type 'SearchFilters'. Did you mean 'maxAge'?
src/components/marketplace/horizontal-filters.tsx(182,53): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
src/components/marketplace/horizontal-filters.tsx(219,28): error TS2551: Property 'content_types' does not exist on type 'SearchFilters'. Did you mean 'contentTypes'?
src/components/marketplace/horizontal-filters.tsx(221,32): error TS2551: Property 'content_types' does not exist on type 'SearchFilters'. Did you mean 'contentTypes'?
src/components/marketplace/horizontal-filters.tsx(242,33): error TS2551: Property 'content_types' does not exist on type 'SearchFilters'. Did you mean 'contentTypes'?
src/components/marketplace/horizontal-filters.tsx(246,43): error TS2345: Argument of type '"content_types"' is not assignable to parameter of type 'keyof SearchFilters'.
src/components/marketplace/horizontal-filters.tsx(304,54): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
src/components/marketplace/horizontal-filters.tsx(329,28): error TS2551: Property 'min_price' does not exist on type 'SearchFilters'. Did you mean 'minPrice'?
src/components/marketplace/horizontal-filters.tsx(329,49): error TS2551: Property 'max_price' does not exist on type 'SearchFilters'. Did you mean 'maxPrice'?
src/components/marketplace/horizontal-filters.tsx(331,32): error TS2551: Property 'min_price' does not exist on type 'SearchFilters'. Did you mean 'minPrice'?
src/components/marketplace/horizontal-filters.tsx(331,57): error TS2551: Property 'max_price' does not exist on type 'SearchFilters'. Did you mean 'maxPrice'?
src/components/marketplace/horizontal-filters.tsx(382,28): error TS2551: Property 'min_age' does not exist on type 'SearchFilters'. Did you mean 'minAge'?
src/components/marketplace/horizontal-filters.tsx(382,47): error TS2551: Property 'max_age' does not exist on type 'SearchFilters'. Did you mean 'maxAge'?
src/components/marketplace/horizontal-filters.tsx(384,32): error TS2551: Property 'min_age' does not exist on type 'SearchFilters'. Did you mean 'minAge'?
src/components/marketplace/horizontal-filters.tsx(384,56): error TS2551: Property 'max_age' does not exist on type 'SearchFilters'. Did you mean 'maxAge'?
src/components/marketplace/horizontal-filters.tsx(418,38): error TS2367: This comparison appears to be unintentional because the types 'string' and 'number' have no overlap.
src/components/marketplace/horizontal-filters.tsx(421,65): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
src/components/marketplace/horizontal-filters.tsx(425,22): error TS2551: Property 'content_types' does not exist on type 'SearchFilters'. Did you mean 'contentTypes'?
src/components/marketplace/horizontal-filters.tsx(425,41): error TS7006: Parameter 'contentType' implicitly has an 'any' type.
src/components/marketplace/horizontal-filters.tsx(431,39): error TS2345: Argument of type '"content_types"' is not assignable to parameter of type 'keyof SearchFilters'.
src/components/marketplace/horizontal-filters.tsx(438,39): error TS2367: This comparison appears to be unintentional because the types 'string' and 'number' have no overlap.
src/components/marketplace/horizontal-filters.tsx(441,66): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
src/components/marketplace/InfluencerCard.tsx(146,26): error TS2339: Property 'city' does not exist on type 'InfluencerSearchResult'.
src/components/marketplace/InfluencerCard.tsx(146,45): error TS2339: Property 'country' does not exist on type 'InfluencerSearchResult'.
src/components/marketplace/InfluencerCard.tsx(149,31): error TS2339: Property 'city' does not exist on type 'InfluencerSearchResult'.
src/components/marketplace/InfluencerCard.tsx(149,50): error TS2339: Property 'country' does not exist on type 'InfluencerSearchResult'.
src/components/marketplace/InfluencerCard.tsx(150,37): error TS2339: Property 'city' does not exist on type 'InfluencerSearchResult'.
src/components/marketplace/InfluencerCard.tsx(150,57): error TS2339: Property 'country' does not exist on type 'InfluencerSearchResult'.
src/components/marketplace/InfluencerCard.tsx(151,34): error TS2339: Property 'city' does not exist on type 'InfluencerSearchResult'.
src/components/marketplace/InfluencerCard.tsx(151,53): error TS2339: Property 'country' does not exist on type 'InfluencerSearchResult'.
src/components/navigation/MobileTopNavbar.tsx(171,26): error TS2345: Argument of type '{ created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]' is not assignable to parameter of type 'SetStateAction<Notification[]>'.
  Type '{ created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]' is not assignable to type 'Notification[]'.
    Type '{ created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }' is not assignable to type 'Notification'.
      Types of property 'data' are incompatible.
        Type 'Json' is not assignable to type 'Record<string, unknown>'.
          Type 'null' is not assignable to type 'Record<string, unknown>'.
src/components/notifications/NotificationDropdown.tsx(77,26): error TS2345: Argument of type '{ created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]' is not assignable to parameter of type 'SetStateAction<Notification[]>'.
  Type '{ created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]' is not assignable to type 'Notification[]'.
    Type '{ created_at: string | null; data: Json; id: string; message: string; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }' is not assignable to type 'Notification'.
      Types of property 'data' are incompatible.
        Type 'Json' is not assignable to type 'Record<string, unknown>'.
          Type 'null' is not assignable to type 'Record<string, unknown>'.
src/components/offers/DirectOfferForm.tsx(128,24): error TS2345: Argument of type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Platform[]>'.
  Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to type 'Platform[]'.
    Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }' is not assignable to type 'Platform'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/offers/DirectOfferForm.tsx(469,32): error TS2345: Argument of type 'Date | undefined' is not assignable to parameter of type 'string | number | Date'.
  Type 'undefined' is not assignable to type 'string | number | Date'.
src/components/onboarding/PackageStep.tsx(109,20): error TS2345: Argument of type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Platform[]>'.
  Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to type 'Platform[]'.
    Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }' is not assignable to type 'Platform'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/onboarding/PackageStep.tsx(110,23): error TS2345: Argument of type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<ContentType[]>'.
  Type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }[]' is not assignable to type 'ContentType[]'.
    Type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }' is not assignable to type 'ContentType'.
      Types of property 'platform_id' are incompatible.
        Type 'number | null' is not assignable to type 'number'.
          Type 'null' is not assignable to type 'number'.
src/components/onboarding/PackageStep.tsx(129,25): error TS2339: Property 'requires_video_duration' does not exist on type 'ContentType'.
src/components/onboarding/steps/PackageCreationStep.tsx(100,22): error TS2345: Argument of type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Platform[]>'.
  Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to type 'Platform[]'.
    Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }' is not assignable to type 'Platform'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/onboarding/steps/PackageCreationStep.tsx(106,25): error TS2345: Argument of type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<ContentType[]>'.
  Type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }[]' is not assignable to type 'ContentType[]'.
    Type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }' is not assignable to type 'ContentType'.
      Types of property 'platform_id' are incompatible.
        Type 'number | null' is not assignable to type 'number'.
          Type 'null' is not assignable to type 'number'.
src/components/reviews/ReviewStats.tsx(94,26): error TS2339: Property 'recent_reviews_count' does not exist on type 'ReviewStats'.
src/components/ui/category-grid-selector.tsx(52,21): error TS2345: Argument of type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Category[]>'.
  Type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }[]' is not assignable to type 'Category[]'.
    Type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }' is not assignable to type 'Category'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/ui/category-selector.tsx(68,21): error TS2345: Argument of type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Category[]>'.
  Type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }[]' is not assignable to type 'Category[]'.
    Type '{ created_at: string | null; description: string | null; icon: string | null; id: number; name: string; slug: string; }' is not assignable to type 'Category'.
      Types of property 'description' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/ui/gradient-button.tsx(3,18): error TS2305: Module '"@/components/ui/button"' has no exported member 'ButtonProps'.
src/components/ui/gradient-button.tsx(16,6): error TS2339: Property 'className' does not exist on type 'GradientButtonProps'.
src/components/ui/gradient-card.tsx(3,16): error TS2305: Module '"@/components/ui/card"' has no exported member 'CardProps'.
src/components/ui/gradient-card.tsx(11,6): error TS2339: Property 'className' does not exist on type 'GradientCardProps'.
src/components/ui/platform-selector.tsx(108,20): error TS2345: Argument of type '{ platform_id: number; platform_name: string; platform_slug: string; platform_icon: string | null; content_types: { id: number; name: string; slug: string; description: string | null; }[]; }[]' is not assignable to parameter of type 'SetStateAction<PlatformWithContentTypes[]>'.
  Type '{ platform_id: number; platform_name: string; platform_slug: string; platform_icon: string | null; content_types: { id: number; name: string; slug: string; description: string | null; }[]; }[]' is not assignable to type 'PlatformWithContentTypes[]'.
    Type '{ platform_id: number; platform_name: string; platform_slug: string; platform_icon: string | null; content_types: { id: number; name: string; slug: string; description: string | null; }[]; }' is not assignable to type 'PlatformWithContentTypes'.
      Types of property 'platform_icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/ui/platform-selector.tsx(335,48): error TS2339: Property 'id' does not exist on type 'PlatformWithContentTypes'.
src/components/ui/pricing-matrix.tsx(98,20): error TS2345: Argument of type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<Platform[]>'.
  Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }[]' is not assignable to type 'Platform[]'.
    Type '{ created_at: string | null; icon: string | null; id: number; is_active: boolean | null; name: string; slug: string; }' is not assignable to type 'Platform'.
      Types of property 'icon' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/components/ui/pricing-matrix.tsx(99,23): error TS2345: Argument of type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }[]' is not assignable to parameter of type 'SetStateAction<ContentType[]>'.
  Type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }[]' is not assignable to type 'ContentType[]'.
    Type '{ created_at: string | null; description: string | null; id: number; is_active: boolean | null; name: string; platform_id: number | null; slug: string; }' is not assignable to type 'ContentType'.
      Types of property 'description' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/lib/api-auth.ts(312,25): error TS2488: Type '{}' must have a '[Symbol.iterator]()' method that returns an iterator.
src/lib/api-middleware.ts(70,44): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/api-middleware.ts(101,51): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/api-middleware.ts(142,49): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/api-middleware.ts(168,53): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/api-middleware.ts(196,55): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/api-middleware.ts(223,53): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/api-middleware.ts(242,9): error TS2322: Type 'Record<string, unknown> | undefined' is not assignable to type '{ id: string; email: string; user_type: "influencer" | "business"; profile_completed?: boolean | undefined; metadata?: Record<string, unknown> | undefined; } | undefined'.
  Type 'Record<string, unknown>' is missing the following properties from type '{ id: string; email: string; user_type: "influencer" | "business"; profile_completed?: boolean | undefined; metadata?: Record<string, unknown> | undefined; }': id, email, user_type
src/lib/api-middleware.ts(250,44): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/api-middleware.ts(281,47): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/auth-helpers.ts(90,45): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }, options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '{ id: string; official_company_name: string; }' is not assignable to parameter of type '{ active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }'.
      Property 'company_name' is missing in type '{ id: string; official_company_name: string; }' but required in type '{ active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }'.
  Overload 2 of 2, '(values: { active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }[], options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Object literal may only specify known properties, and 'id' does not exist in type '{ active_campaigns_count?: number | null | undefined; budget_range?: string | null | undefined; company_name: string; company_size?: string | null | undefined; contact_person_name?: string | ... 1 more ... | undefined; ... 6 more ...; updated_at?: string | ... 1 more ... | undefined; }[]'.
src/lib/campaigns.ts(127,38): error TS2339: Property 'split' does not exist on type 'never'.
src/lib/campaigns.ts(163,20): error TS2352: Conversion of type '{ id: string; title: string; description: string; budget: number; status: "draft" | "active" | "paused" | "completed" | "cancelled"; location: string; ... 21 more ...; business: { ...; }; }' to type 'CampaignDetails' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ id: string; title: string; description: string; budget: number; status: "completed" | "draft" | "active" | "paused" | "cancelled"; location: string; application_deadline: string; min_followers: number; ... 19 more ...; business: { ...; }; }' is missing the following properties from type 'CampaignDetails': activated_at, campaign_goal, contact_email, contact_phone, and 13 more.
src/lib/campaigns.ts(196,32): error TS2345: Argument of type 'string' is not assignable to parameter of type 'NonNullable<"completed" | "draft" | "active" | "paused" | "cancelled" | null>'.
src/lib/campaigns.ts(250,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(251,9): error TS2322: Type 'number[] | null' is not assignable to type 'number[] | undefined'.
  Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/campaigns.ts(252,9): error TS2322: Type 'number[] | null' is not assignable to type 'number[] | undefined'.
  Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/campaigns.ts(253,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/campaigns.ts(254,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/campaigns.ts(255,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(351,34): error TS2339: Property 'split' does not exist on type 'string[]'.
src/lib/campaigns.ts(403,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(404,9): error TS2322: Type 'number[] | null' is not assignable to type 'number[] | undefined'.
  Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/campaigns.ts(405,9): error TS2322: Type 'number[] | null' is not assignable to type 'number[] | undefined'.
  Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/campaigns.ts(406,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/campaigns.ts(407,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/campaigns.ts(408,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(409,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/campaigns.ts(410,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/campaigns.ts(411,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(412,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(558,33): error TS2339: Property 'split' does not exist on type 'string[]'.
src/lib/campaigns.ts(560,36): error TS2551: Property 'doNotMention' does not exist on type 'CampaignUpdateData'. Did you mean 'do_not_mention'?
src/lib/campaigns.ts(561,24): error TS2551: Property 'doNotMention' does not exist on type 'CampaignUpdateData'. Did you mean 'do_not_mention'?
src/lib/campaigns.ts(563,38): error TS2551: Property 'additionalNotes' does not exist on type 'CampaignUpdateData'. Did you mean 'additional_notes'?
src/lib/campaigns.ts(564,40): error TS2551: Property 'collaborationType' does not exist on type 'CampaignUpdateData'. Did you mean 'collaboration_type'?
src/lib/campaigns.ts(576,20): error TS2339: Property 'platforms' does not exist on type 'CampaignUpdateData'.
src/lib/campaigns.ts(576,46): error TS2339: Property 'platforms' does not exist on type 'CampaignUpdateData'.
src/lib/campaigns.ts(584,42): error TS2339: Property 'platforms' does not exist on type 'CampaignUpdateData'.
src/lib/campaigns.ts(595,20): error TS2339: Property 'categories' does not exist on type 'CampaignUpdateData'.
src/lib/campaigns.ts(595,47): error TS2339: Property 'categories' does not exist on type 'CampaignUpdateData'.
src/lib/campaigns.ts(603,42): error TS2339: Property 'categories' does not exist on type 'CampaignUpdateData'.
src/lib/campaigns.ts(742,9): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(743,9): error TS2322: Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/campaigns.ts(744,9): error TS2322: Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/campaigns.ts(745,9): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
src/lib/campaigns.ts(746,9): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
src/lib/campaigns.ts(747,9): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(821,9): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/campaigns.ts(890,32): error TS2345: Argument of type 'string' is not assignable to parameter of type 'NonNullable<"pending" | "accepted" | "rejected" | "completed" | null>'.
src/lib/campaigns.ts(935,34): error TS2345: Argument of type 'string' is not assignable to parameter of type 'NonNullable<"pending" | "accepted" | "rejected" | "completed" | null>'.
src/lib/campaigns.ts(1019,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(1040,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { campaign_id: string; content_types: string[]; created_at?: string | null | undefined; id?: string | undefined; platform_id: number; }, options?: { count?: "exact" | "planned" | "estimated" | undefined; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '{ platform_id: number; content_type_ids: number[]; posts_required?: number | undefined; budget_per_post?: number | undefined; campaign_id: string; }[]' is not assignable to parameter of type '{ campaign_id: string; content_types: string[]; created_at?: string | null | undefined; id?: string | undefined; platform_id: number; }'.
      Type '{ platform_id: number; content_type_ids: number[]; posts_required?: number | undefined; budget_per_post?: number | undefined; campaign_id: string; }[]' is missing the following properties from type '{ campaign_id: string; content_types: string[]; created_at?: string | null | undefined; id?: string | undefined; platform_id: number; }': campaign_id, content_types, platform_id
  Overload 2 of 2, '(values: { campaign_id: string; content_types: string[]; created_at?: string | null | undefined; id?: string | undefined; platform_id: number; }[], options?: { count?: "exact" | "planned" | "estimated" | undefined; defaultToNull?: boolean | undefined; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '{ platform_id: number; content_type_ids: number[]; posts_required?: number | undefined; budget_per_post?: number | undefined; campaign_id: string; }[]' is not assignable to parameter of type '{ campaign_id: string; content_types: string[]; created_at?: string | null | undefined; id?: string | undefined; platform_id: number; }[]'.
      Property 'content_types' is missing in type '{ platform_id: number; content_type_ids: number[]; posts_required?: number; budget_per_post?: number; campaign_id: string; }' but required in type '{ campaign_id: string; content_types: string[]; created_at?: string | null | undefined; id?: string | undefined; platform_id: number; }'.
src/lib/campaigns.ts(1084,46): error TS2345: Argument of type '"refresh_campaigns_search_view"' is not assignable to parameter of type '"check_user_exists" | "cleanup_expired_featured_campaigns" | "cleanup_old_read_status" | "convert_km_to_eur" | "count_business_applications_by_status" | "count_business_campaigns_by_status" | ... 28 more ... | "validate_cleanup_migration"'.
src/lib/campaigns.ts(1101,39): error TS2345: Argument of type '(c: { id: string; }) => string' is not assignable to parameter of type '(value: { status: "completed" | "draft" | "active" | "paused" | "cancelled" | null; }, index: number, array: { status: "completed" | "draft" | "active" | "paused" | "cancelled" | null; }[]) => string'.
  Types of parameters 'c' and 'value' are incompatible.
    Property 'id' is missing in type '{ status: "completed" | "draft" | "active" | "paused" | "cancelled" | null; }' but required in type '{ id: string; }'.
src/lib/campaigns.ts(1179,34): error TS2345: Argument of type 'string' is not assignable to parameter of type 'NonNullable<"pending" | "accepted" | "rejected" | "completed" | null>'.
src/lib/campaigns.ts(1296,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(1371,13): error TS2740: Type '{ id: string; title: string; description: string; budget: number; requirements: string; deliverables: string; business_id: string; }' is missing the following properties from type 'CampaignWithBusiness': business, platforms, categories, activated_at, and 32 more.
src/lib/campaigns.ts(1383,15): error TS2740: Type '{ id: string; full_name: string; username: string; avatar_url: null; bio: string; city: string; country: string; age: null; gender: string; }' is missing the following properties from type '{ address: string | null; age: number | null; avatar_url: string | null; average_rating: number | null; bank_account: string | null; bank_name: string | null; bio: string | null; ... 22 more ...; website_url: string | null; }': address, average_rating, bank_account, bank_name, and 17 more.
src/lib/campaigns.ts(1573,20): error TS2352: Conversion of type '{ campaign: { business: { id: string; company_name: string; categories: { id: number; name: string; icon: string | null; }[]; profiles: { username: string; avatar_url: string | null; }; }; platforms: { ...; }[]; ... 12 more ...; end_date: string | null; }; ... 14 more ...; influencers: { ...; }; }' to type 'ApplicationWithDetails' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ campaign: { business: { id: string; company_name: string; categories: { id: number; name: string; icon: string | null; }[]; profiles: { username: string; avatar_url: string | null; }; }; platforms: { ...; }[]; ... 12 more ...; end_date: string | null; }; ... 14 more ...; influencers: { ...; }; }' is missing the following properties from type 'ApplicationWithDetails': available_start_date, responded_at
src/lib/campaigns.ts(1576,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(1590,18): error TS2339: Property 'rejection_reason' does not exist on type 'ApplicationUpdateData'.
src/lib/campaigns.ts(1595,15): error TS2345: Argument of type 'ApplicationUpdateData' is not assignable to parameter of type '{ additional_services?: string | null | undefined; applied_at?: string | null | undefined; audience_insights?: string | null | undefined; available_start_date?: string | null | undefined; ... 9 more ...; status?: "pending" | ... 4 more ... | undefined; }'.
  Types of property 'status' are incompatible.
    Type 'string' is not assignable to type '"pending" | "accepted" | "rejected" | "completed" | null | undefined'.
src/lib/campaigns.ts(1643,13): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/campaigns.ts(1651,13): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/campaigns.ts(1659,13): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/campaigns.ts(1673,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(1736,9): error TS2322: Type '"pending" | "accepted" | "rejected" | "completed" | null' is not assignable to type '"pending" | "accepted" | "rejected" | "completed" | undefined'.
  Type 'null' is not assignable to type '"pending" | "accepted" | "rejected" | "completed" | undefined'.
src/lib/campaigns.ts(1756,24): error TS18046: 'app' is of type 'unknown'.
src/lib/campaigns.ts(1758,19): error TS18046: 'app' is of type 'unknown'.
src/lib/campaigns.ts(1760,23): error TS18046: 'app' is of type 'unknown'.
src/lib/campaigns.ts(1761,27): error TS18046: 'app' is of type 'unknown'.
src/lib/campaigns.ts(1762,28): error TS18046: 'app' is of type 'unknown'.
src/lib/campaigns.ts(1773,14): error TS2322: Type '{ applied_at: string; campaign_budget: number; campaign_id: string; campaign_title: string; delivery_timeframe: string; id: string; influencer_avatar_url: string; influencer_display_name: string; ... 4 more ...; status: "pending" | ... 2 more ... | "completed"; }[]' is not assignable to type 'ApplicationCard[]'.
  Type '{ applied_at: string; campaign_budget: number; campaign_id: string; campaign_title: string; delivery_timeframe: string; id: string; influencer_avatar_url: string; influencer_display_name: string; ... 4 more ...; status: "pending" | ... 2 more ... | "completed"; }' is not assignable to type 'ApplicationCard'.
    Types of property 'status' are incompatible.
      Type '"pending" | "accepted" | "rejected" | "completed"' is not assignable to type '"pending" | "accepted" | "rejected"'.
        Type '"completed"' is not assignable to type '"pending" | "accepted" | "rejected"'.
src/lib/campaigns.ts(1776,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(1811,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(1883,14): error TS2322: Type '{ additional_services: string; applied_at: string; audience_insights: string; available_start_date: string; campaign_budget: number; campaign_description: string; campaign_id: string; ... 14 more ...; status: "pending" | ... 2 more ... | "completed"; } | null' is not assignable to type 'ApplicationDetails | null'.
  Type '{ additional_services: string; applied_at: string; audience_insights: string; available_start_date: string; campaign_budget: number; campaign_description: string; campaign_id: string; ... 14 more ...; status: "pending" | ... 2 more ... | "completed"; }' is not assignable to type 'ApplicationDetails'.
    Types of property 'status' are incompatible.
      Type '"pending" | "accepted" | "rejected" | "completed"' is not assignable to type '"pending" | "accepted" | "rejected"'.
        Type '"completed"' is not assignable to type '"pending" | "accepted" | "rejected"'.
src/lib/campaigns.ts(1886,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(1992,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(2040,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/campaigns.ts(2051,14): error TS2322: Type '{ application_count: number; budget: number; business_id: string; created_at: string; description: string; id: string; is_featured: boolean; status: "completed" | "draft" | "active" | "paused" | "cancelled"; title: string; }[]' is not assignable to type 'CampaignDashboardCard[]'.
  Type '{ application_count: number; budget: number; business_id: string; created_at: string; description: string; id: string; is_featured: boolean; status: "completed" | "draft" | "active" | "paused" | "cancelled"; title: string; }' is missing the following properties from type 'CampaignDashboardCard': content_types, platforms
src/lib/campaigns.ts(2054,33): error TS18046: 'error' is of type 'unknown'.
src/lib/campaigns.ts(2092,33): error TS18046: 'error' is of type 'unknown'.
src/lib/chat.ts(281,16): error TS2322: Type '{ business_profile: { full_name: string | null; public_display_name: string | null; username: string | null; avatar_url: string | null; user_type: "influencer" | "business"; } | null; ... 10 more ...; updated_at: string | null; }[]' is not assignable to type 'ChatRoom[]'.
  Type '{ business_profile: { full_name: string | null; public_display_name: string | null; username: string | null; avatar_url: string | null; user_type: "influencer" | "business"; } | null; ... 10 more ...; updated_at: string | null; }' is not assignable to type 'ChatRoom'.
    Types of property 'business_profile' are incompatible.
      Type '{ full_name: string | null; public_display_name: string | null; username: string | null; avatar_url: string | null; user_type: "influencer" | "business"; } | null' is not assignable to type '{ full_name: string; public_display_name: string | null; username: string; avatar_url: string | null; user_type?: string | undefined; } | undefined'.
        Type 'null' is not assignable to type '{ full_name: string; public_display_name: string | null; username: string; avatar_url: string | null; user_type?: string | undefined; } | undefined'.
src/lib/chat.ts(322,14): error TS2322: Type '{ created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; file_url: string | null; id: string; message_text: string | null; ... 4 more ...; sender_profile: { ...; }; }[]' is not assignable to type 'ChatMessage[]'.
  Type '{ created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; file_url: string | null; id: string; message_text: string | null; ... 4 more ...; sender_profile: { ...; }; }' is not assignable to type 'ChatMessage'.
    The types of 'sender_profile.full_name' are incompatible between these types.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
src/lib/chat.ts(386,14): error TS2322: Type '{ created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; file_url: string | null; id: string; message_text: string | null; ... 4 more ...; sender_profile: { ...; }; } | null' is not assignable to type 'ChatMessage | null'.
  Type '{ created_at: string | null; edited_at: string | null; file_name: string | null; file_size: number | null; file_type: string | null; file_url: string | null; id: string; message_text: string | null; ... 4 more ...; sender_profile: { ...; }; }' is not assignable to type 'ChatMessage'.
    The types of 'sender_profile.full_name' are incompatible between these types.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
src/lib/chat.ts(604,14): error TS2322: Type '{ business_profile: { full_name: string | null; public_display_name: string | null; username: string | null; avatar_url: string | null; user_type: "influencer" | "business"; } | null; ... 10 more ...; updated_at: string | null; }' is not assignable to type 'ChatRoom'.
  Types of property 'business_profile' are incompatible.
    Type '{ full_name: string | null; public_display_name: string | null; username: string | null; avatar_url: string | null; user_type: "influencer" | "business"; } | null' is not assignable to type '{ full_name: string; public_display_name: string | null; username: string; avatar_url: string | null; user_type?: string | undefined; } | undefined'.
      Type 'null' is not assignable to type '{ full_name: string; public_display_name: string | null; username: string; avatar_url: string | null; user_type?: string | undefined; } | undefined'.
src/lib/featured-campaigns.ts(116,9): error TS2322: Type '{ id: string; title: string; status: "completed" | "draft" | "active" | "paused" | "cancelled" | null; is_featured: boolean | null; business_id: string; }' is not assignable to type 'CampaignInfo'.
  Types of property 'status' are incompatible.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
src/lib/featured-campaigns.ts(122,32): error TS2322: Type '{ id: string; title: string; status: "completed" | "draft" | "active" | "paused" | "cancelled" | null; is_featured: boolean | null; business_id: string; }' is not assignable to type 'CampaignInfo'.
  Types of property 'status' are incompatible.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
src/lib/featured-campaigns.ts(208,29): error TS2322: Type '{ campaign_application_id: string | null; campaign_id: string | null; created_at: string | null; currency: string; direct_offer_id: string | null; featured_until: string | null; ... 16 more ...; user_type: string | null; }' is not assignable to type 'PromotionInfo'.
  Types of property 'campaign_id' are incompatible.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
src/lib/featured-campaigns.ts(244,14): error TS2322: Type '{ campaign_application_id: string | null; campaign_id: string | null; created_at: string | null; currency: string; direct_offer_id: string | null; featured_until: string | null; ... 17 more ...; campaigns: { ...; }; }[] | null' is not assignable to type 'PromotionInfo[] | null'.
  Type '{ campaign_application_id: string | null; campaign_id: string | null; created_at: string | null; currency: string; direct_offer_id: string | null; featured_until: string | null; ... 17 more ...; campaigns: { ...; }; }[]' is not assignable to type 'PromotionInfo[]'.
    Type '{ campaign_application_id: string | null; campaign_id: string | null; created_at: string | null; currency: string; direct_offer_id: string | null; featured_until: string | null; ... 17 more ...; campaigns: { ...; }; }' is not assignable to type 'PromotionInfo'.
      Types of property 'campaign_id' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/lib/featured-campaigns.ts(285,7): error TS2345: Argument of type '(p: PromotionInfo) => string' is not assignable to parameter of type '(value: { campaign_id: string | null; }, index: number, array: { campaign_id: string | null; }[]) => string'.
  Types of parameters 'p' and 'value' are incompatible.
    Type '{ campaign_id: string | null; }' is missing the following properties from type 'PromotionInfo': id, payment_type, featured_until, payment_status, and 2 more.
src/lib/featured-campaigns.ts(411,5): error TS2322: Type '{ campaign_application_id: string | null; campaign_id: string | null; created_at: string | null; currency: string; direct_offer_id: string | null; featured_until: string | null; ... 16 more ...; user_type: string | null; } | null' is not assignable to type 'PaymentRecord | null'.
  Type '{ campaign_application_id: string | null; campaign_id: string | null; created_at: string | null; currency: string; direct_offer_id: string | null; featured_until: string | null; ... 16 more ...; user_type: string | null; }' is not assignable to type 'PaymentRecord'.
    Types of property 'campaign_id' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
src/lib/job-completions.ts(284,17): error TS2339: Property 'offer_type' does not exist on type '{ influencer_id: string; business_id: string; title: string; budget: number; }'.
src/lib/job-completions.ts(289,11): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/job-completions.ts(376,11): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/job-completions.ts(377,32): error TS2339: Property 'title' does not exist on type '{ business_id: string; }'.
src/lib/job-completions.ts(598,19): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/job-completions.ts(620,11): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/job-completions.ts(685,19): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/job-completions.ts(707,11): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/job-reviews.ts(389,7): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/job-reviews.ts(392,7): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/marketplace.ts(166,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/marketplace.ts(167,9): error TS2322: Type 'number[] | null' is not assignable to type 'number[] | undefined'.
  Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/marketplace.ts(168,9): error TS2322: Type 'number[] | null' is not assignable to type 'number[] | undefined'.
  Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/marketplace.ts(169,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(170,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(171,9): error TS2322: Type '"other" | "male" | "female" | "prefer_not_to_say" | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/marketplace.ts(172,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(173,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(174,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(175,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(176,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/marketplace.ts(204,11): error TS2322: Type '{ id: string; username: string; full_name: string; avatar_url: string; navbar_avatar_url: string; card_avatar_url: string; profile_avatar_url: string; preview_avatar_url: string; bio: string; ... 13 more ...; total_reviews: number; }[]' is not assignable to type 'InfluencerSearchResult[]'.
  Type '{ id: string; username: string; full_name: string; avatar_url: string; navbar_avatar_url: string; card_avatar_url: string; profile_avatar_url: string; preview_avatar_url: string; bio: string; ... 13 more ...; total_reviews: number; }' is not assignable to type 'InfluencerSearchResult'.
    Types of property 'subscription_type' are incompatible.
      Type 'string' is not assignable to type '"premium" | "free"'.
src/lib/marketplace.ts(205,7): error TS2345: Argument of type '(item: InfluencerRPCResult) => { id: string; username: string; full_name: string; avatar_url: string; navbar_avatar_url: string; card_avatar_url: string; profile_avatar_url: string; preview_avatar_url: string; ... 14 more ...; total_reviews: number; }' is not assignable to parameter of type '(value: { age: number; avatar_url: string; avg_rating: number; bio: string; created_at: string; full_name: string; gender: string; id: string; location: string; platforms: Json; subscription_type: string; total_followers: number; total_reviews: number; username: string; }, index: number, array: { ...; }[]) => { ...; }'.
  Types of parameters 'item' and 'value' are incompatible.
    Type '{ age: number; avatar_url: string; avg_rating: number; bio: string; created_at: string; full_name: string; gender: string; id: string; location: string; platforms: Json; subscription_type: string; total_followers: number; total_reviews: number; username: string; }' is missing the following properties from type 'InfluencerRPCResult': categories, pricing, min_price, max_price
src/lib/marketplace.ts(225,35): error TS2345: Argument of type 'number | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/marketplace.ts(227,41): error TS2339: Property 'avg_rating' does not exist on type 'InfluencerRPCResult'.
src/lib/marketplace.ts(228,38): error TS2339: Property 'total_reviews' does not exist on type 'InfluencerRPCResult'.
src/lib/marketplace.ts(400,29): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
src/lib/marketplace.ts(406,29): error TS18047: 'p.followers_count' is possibly 'null'.
src/lib/marketplace.ts(448,11): error TS2322: Type '{ id: string; username: string; full_name: string; avatar_url: string; navbar_avatar_url: string; card_avatar_url: string; profile_avatar_url: string; preview_avatar_url: string; bio: string; ... 13 more ...; total_reviews: number; }[]' is not assignable to type 'InfluencerSearchResult[]'.
  Type '{ id: string; username: string; full_name: string; avatar_url: string; navbar_avatar_url: string; card_avatar_url: string; profile_avatar_url: string; preview_avatar_url: string; bio: string; ... 13 more ...; total_reviews: number; }' is not assignable to type 'InfluencerSearchResult'.
    Types of property 'subscription_type' are incompatible.
      Type 'string' is not assignable to type '"premium" | "free"'.
src/lib/marketplace.ts(470,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/marketplace.ts(471,9): error TS2322: Type 'number[] | null' is not assignable to type 'number[] | undefined'.
  Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/marketplace.ts(472,9): error TS2322: Type 'number[] | null' is not assignable to type 'number[] | undefined'.
  Type 'null' is not assignable to type 'number[] | undefined'.
src/lib/marketplace.ts(473,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(474,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(475,9): error TS2322: Type '"other" | "male" | "female" | "prefer_not_to_say" | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/marketplace.ts(476,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(477,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(478,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(479,9): error TS2322: Type 'number | null' is not assignable to type 'number | undefined'.
  Type 'null' is not assignable to type 'number | undefined'.
src/lib/marketplace.ts(480,9): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/marketplace.ts(563,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "business_platforms" | "profiles" | "platforms" | "business_target_categories" | "businesses" | "categories" | "campaign_applications" | "campaigns" | "influencers" | ... 20 more ... | "user_subscriptions"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"pricing_packages"' is not assignable to parameter of type '"business_platforms" | "profiles" | "platforms" | "business_target_categories" | "businesses" | "categories" | "campaign_applications" | "campaigns" | "influencers" | "campaign_categories" | ... 19 more ... | "user_subscriptions"'.
  Overload 2 of 2, '(relation: "influencer_search_view"): PostgrestQueryBuilder<{ Tables: { business_platforms: { Row: { business_id: string | null; created_at: string | null; followers_count: number | null; handle: string; id: string; platform_id: number | null; updated_at: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 28 more ...; user_subscriptions: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "influencer_search_view", [...]>', gave the following error.
    Argument of type '"pricing_packages"' is not assignable to parameter of type '"influencer_search_view"'.
src/lib/marketplace.ts(629,31): error TS2339: Property 'gender' does not exist on type '{ created_at: string | null; custom_offers_enabled: boolean | null; engagement_rate: number | null; id: string; is_verified: boolean | null; portfolio_urls: string[] | null; subscription_type: string | null; updated_at: string | null; }'.
src/lib/marketplace.ts(630,28): error TS2339: Property 'age' does not exist on type '{ created_at: string | null; custom_offers_enabled: boolean | null; engagement_rate: number | null; id: string; is_verified: boolean | null; portfolio_urls: string[] | null; subscription_type: string | null; updated_at: string | null; }'.
src/lib/notifications.ts(77,7): error TS2322: Type 'Record<string, unknown>' is not assignable to type 'Json | undefined'.
  Type 'Record<string, unknown>' is missing the following properties from type 'Json[]': length, pop, push, concat, and 35 more.
src/lib/offers.ts(419,15): error TS2352: Conversion of type '{ businesses: { categories: { id: number; name: string; icon: string | null; }[]; id: string; company_name: string; profiles: { avatar_url: string | null; username: string | null; }; }; accepted_at: string | null; ... 19 more ...; updated_at: string | null; }[]' to type 'DirectOfferWithDetails[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'influencer' is missing in type '{ businesses: { categories: { id: number; name: string; icon: string | null; }[]; id: string; company_name: string; profiles: { avatar_url: string | null; username: string | null; }; }; accepted_at: string | null; ... 19 more ...; updated_at: string | null; }' but required in type 'DirectOfferWithDetails'.
src/lib/offers.ts(424,20): error TS2352: Conversion of type '{ accepted_at: string | null; budget: number; business_id: string; business_message: string | null; content_types: string[]; created_at: string | null; deadline: string | null; deliverables: string | null; ... 13 more ...; businesses: { ...; }; }[]' to type 'DirectOfferWithDetails[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'influencer' is missing in type '{ accepted_at: string | null; budget: number; business_id: string; business_message: string | null; content_types: string[]; created_at: string | null; deadline: string | null; deliverables: string | null; ... 13 more ...; businesses: { ...; }; }' but required in type 'DirectOfferWithDetails'.
src/lib/offers.ts(485,17): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/offers.ts(494,27): error TS2345: Argument of type 'number | null' is not assignable to parameter of type 'number'.
  Type 'null' is not assignable to type 'number'.
src/lib/offers.ts(499,17): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/offers.ts(509,17): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/offers.ts(516,17): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/offers.ts(538,15): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
src/lib/offers.ts(657,23): error TS2339: Property 'user_id' does not exist on type 'SelectQueryError<"column 'user_id' does not exist on 'businesses'.">'.
src/lib/offers.ts(658,24): error TS2339: Property 'user_id' does not exist on type 'SelectQueryError<"column 'user_id' does not exist on 'influencers'.">'.
src/lib/offers.ts(838,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/offers.ts(848,14): error TS2322: Type '{ budget: number; content_types: string[]; created_at: string; deadline: string; description: string; id: string; influencer_avatar_url: string; influencer_display_name: string; influencer_id: string; ... 4 more ...; title: string; }[]' is not assignable to type 'OfferCard[]'.
  Type '{ budget: number; content_types: string[]; created_at: string; deadline: string; description: string; id: string; influencer_avatar_url: string; influencer_display_name: string; influencer_id: string; ... 4 more ...; title: string; }' is not assignable to type 'OfferCard'.
    Types of property 'status' are incompatible.
      Type 'string' is not assignable to type '"pending" | "accepted" | "rejected" | "completed" | "cancelled"'.
src/lib/offers.ts(952,14): error TS2322: Type '{ accepted_at: string; budget: number; business_id: string; business_message: string; content_types: string[]; created_at: string; deadline: string; deliverables: string; description: string; ... 15 more ...; updated_at: string; } | null' is not assignable to type 'OfferDetails | null'.
  Property 'influencer_username' is missing in type '{ accepted_at: string; budget: number; business_id: string; business_message: string; content_types: string[]; created_at: string; deadline: string; deliverables: string; description: string; ... 15 more ...; updated_at: string; }' but required in type 'OfferDetails'.
src/lib/offers.ts(998,5): error TS2322: Type '{ campaign_application_id: string | null; campaign_id: string | null; created_at: string | null; currency: string; direct_offer_id: string | null; featured_until: string | null; ... 16 more ...; user_type: string | null; } | null' is not assignable to type 'PaymentInfo | null'.
  Property 'amount' is missing in type '{ campaign_application_id: string | null; campaign_id: string | null; created_at: string | null; currency: string; direct_offer_id: string | null; featured_until: string | null; ... 16 more ...; user_type: string | null; }' but required in type 'PaymentInfo'.
src/lib/pricing-packages.ts(146,5): error TS2345: Argument of type '"get_platforms_with_content_types"' is not assignable to parameter of type '"check_user_exists" | "cleanup_expired_featured_campaigns" | "cleanup_old_read_status" | "convert_km_to_eur" | "count_business_applications_by_status" | "count_business_campaigns_by_status" | ... 28 more ... | "validate_cleanup_migration"'.
src/lib/pricing-packages.ts(172,9): error TS2322: Type '{ id: number; influencer_id: string; platform_id: number; content_type_id: number; quantity: number; video_duration: string | null; price: number; currency: string; auto_generated_name: string | null; ... 5 more ...; content_type_name: string; }[]' is not assignable to type 'PricingPackage[]'.
  Type '{ id: number; influencer_id: string; platform_id: number; content_type_id: number; quantity: number; video_duration: string | null; price: number; currency: string; auto_generated_name: string | null; ... 5 more ...; content_type_name: string; }' is not assignable to type 'PricingPackage'.
    Types of property 'video_duration' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
src/lib/pricing-packages.ts(173,5): error TS2345: Argument of type '(item: PricingPackageQueryResult) => { id: number; influencer_id: string; platform_id: number; content_type_id: number; quantity: number; video_duration: string | null; price: number; ... 7 more ...; content_type_name: string; }' is not assignable to parameter of type '(value: { auto_generated_name: string | null; content_type_id: number | null; created_at: string | null; currency: string | null; id: number; influencer_id: string | null; is_available: boolean | null; ... 6 more ...; content_types: { ...; }; }, index: number, array: { ...; }[]) => { ...; }'.
  Types of parameters 'item' and 'value' are incompatible.
    Type '{ auto_generated_name: string | null; content_type_id: number | null; created_at: string | null; currency: string | null; id: number; influencer_id: string | null; is_available: boolean | null; ... 6 more ...; content_types: { ...; }; }' is not assignable to type 'PricingPackageQueryResult'.
      Types of property 'influencer_id' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/lib/pricing-packages.ts(255,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/lib/pricing-packages.ts(258,7): error TS2322: Type 'null' is not assignable to type 'string | undefined'.
src/lib/pricing-packages.ts(393,11): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/lib/pricing-packages.ts(396,33): error TS2353: Object literal may only specify known properties, and 'auto_generated_name' does not exist in type 'Partial<PackageCreationData>'.
src/lib/pricing-packages.ts(462,28): error TS2345: Argument of type '(item: PricingPackageQueryResult) => { platform_id: number; platform_name: string; platform_icon: string; content_type_id: number; content_type_name: string; package_name: string | null; quantity: number; video_duration: string | null; price: number; currency: string; }' is not assignable to parameter of type '(value: { auto_generated_name: string | null; content_type_id: number | null; created_at: string | null; currency: string | null; id: number; influencer_id: string | null; is_available: boolean | null; ... 6 more ...; content_types: { ...; }; }, index: number, array: { ...; }[]) => { ...; }'.
  Types of parameters 'item' and 'value' are incompatible.
    Type '{ auto_generated_name: string | null; content_type_id: number | null; created_at: string | null; currency: string | null; id: number; influencer_id: string | null; is_available: boolean | null; ... 6 more ...; content_types: { ...; }; }' is not assignable to type 'PricingPackageQueryResult'.
      Types of property 'influencer_id' are incompatible.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/lib/profiles.ts(226,7): error TS7034: Variable 'platforms' implicitly has type 'any[]' in some locations where its type cannot be determined.
src/lib/profiles.ts(230,35): error TS2345: Argument of type '(platform: PlatformData) => { platform_id: number; platform_name: string; platform_icon: string; handle: string | null; followers_count: number | null; is_verified: boolean | null | undefined; }' is not assignable to parameter of type '(value: { platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }, index: number, array: { ...; }[]) => { ...; }'.
  Types of parameters 'platform' and 'value' are incompatible.
    Type '{ platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }' is not assignable to type 'PlatformData'.
      The types of 'platforms.icon' are incompatible between these types.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/lib/profiles.ts(239,5): error TS2322: Type '{ platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }' is not assignable to type 'number'.
src/lib/profiles.ts(240,7): error TS2769: No overload matches this call.
  Overload 1 of 3, '(callbackfn: (previousValue: { platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }, currentValue: { ...; }, currentIndex: number, array: { ...; }[]) => { ...; }, initialValue: { ...; }): { ...; }', gave the following error.
    Argument of type '(sum: number, platform: PlatformData) => number' is not assignable to parameter of type '(previousValue: { platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }, currentValue: { platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { ...; }...'.
      Types of parameters 'sum' and 'previousValue' are incompatible.
        Type '{ platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }' is not assignable to type 'number'.
  Overload 2 of 3, '(callbackfn: (previousValue: number, currentValue: { platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }, currentIndex: number, array: { ...; }[]) => number, initialValue: number): number', gave the following error.
    Argument of type '(sum: number, platform: PlatformData) => number' is not assignable to parameter of type '(previousValue: number, currentValue: { platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }, currentIndex: number, array: { ...; }[]) => number'.
      Types of parameters 'platform' and 'currentValue' are incompatible.
        Type '{ platform_id: number; handle: string | null; followers_count: number | null; is_verified: boolean | null; platforms: { name: string; icon: string | null; }; }' is not assignable to type 'PlatformData'.
          The types of 'platforms.icon' are incompatible between these types.
            Type 'string | null' is not assignable to type 'string'.
              Type 'null' is not assignable to type 'string'.
src/lib/profiles.ts(260,7): error TS7034: Variable 'pricing' implicitly has type 'any[]' in some locations where its type cannot be determined.
src/lib/profiles.ts(266,31): error TS2345: Argument of type '(pkg: PricingData) => { platform_id: number | null; platform_name: string; platform_icon: string; content_type_id: number | null; content_type_name: string; price: number; currency: string; quantity: number; video_duration: number | undefined; auto_generated_name: string | null; id: number; }' is not assignable to parameter of type '(value: { auto_generated_name: string | null; content_type_id: number | null; created_at: string | null; currency: string | null; id: number; influencer_id: string | null; is_available: boolean | null; ... 6 more ...; content_types: { ...; }; }, index: number, array: { ...; }[]) => { ...; }'.
  Types of parameters 'pkg' and 'value' are incompatible.
    Type '{ auto_generated_name: string | null; content_type_id: number | null; created_at: string | null; currency: string | null; id: number; influencer_id: string | null; is_available: boolean | null; ... 6 more ...; content_types: { ...; }; }' is not assignable to type 'PricingData'.
      The types of 'platforms.icon' are incompatible between these types.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/lib/profiles.ts(281,36): error TS2345: Argument of type '(pkg: PricingData) => number' is not assignable to parameter of type '(value: { auto_generated_name: string | null; content_type_id: number | null; created_at: string | null; currency: string | null; id: number; influencer_id: string | null; is_available: boolean | null; ... 6 more ...; content_types: { ...; }; }, index: number, array: { ...; }[]) => number'.
  Types of parameters 'pkg' and 'value' are incompatible.
    Type '{ auto_generated_name: string | null; content_type_id: number | null; created_at: string | null; currency: string | null; id: number; influencer_id: string | null; is_available: boolean | null; ... 6 more ...; content_types: { ...; }; }' is not assignable to type 'PricingData'.
      The types of 'platforms.icon' are incompatible between these types.
        Type 'string | null' is not assignable to type 'string'.
          Type 'null' is not assignable to type 'string'.
src/lib/profiles.ts(301,7): error TS7034: Variable 'categories' implicitly has type 'any[]' in some locations where its type cannot be determined.
src/lib/profiles.ts(328,16): error TS7005: Variable 'platforms' implicitly has an 'any[]' type.
src/lib/profiles.ts(329,17): error TS7005: Variable 'categories' implicitly has an 'any[]' type.
src/lib/profiles.ts(330,14): error TS7005: Variable 'pricing' implicitly has an 'any[]' type.
src/lib/profiles.ts(359,7): error TS2353: Object literal may only specify known properties, and 'location' does not exist in type '{ address?: string | null | undefined; age?: number | null | undefined; avatar_url?: string | null | undefined; average_rating?: number | null | undefined; bank_account?: string | null | undefined; ... 24 more ...; website_url?: string | ... 1 more ... | undefined; }'.
src/lib/profiles.ts(359,25): error TS2339: Property 'location' does not exist on type '{ address?: string | null | undefined; age?: number | null | undefined; avatar_url?: string | null | undefined; average_rating?: number | null | undefined; bank_account?: string | null | undefined; ... 24 more ...; website_url?: string | ... 1 more ... | undefined; }'.
src/lib/profiles.ts(636,15): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { business_id?: string | null | undefined; created_at?: string | null | undefined; followers_count?: number | null | undefined; handle: string; id?: string | undefined; platform_id?: number | ... 1 more ... | undefined; updated_at?: string | ... 1 more ... | undefined; }, options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '{ business_id: string; platform_id: number | null; handle: string | null; followers_count: number; }[]' is not assignable to parameter of type '{ business_id?: string | null | undefined; created_at?: string | null | undefined; followers_count?: number | null | undefined; handle: string; id?: string | undefined; platform_id?: number | ... 1 more ... | undefined; updated_at?: string | ... 1 more ... | undefined; }'.
      Property 'handle' is missing in type '{ business_id: string; platform_id: number | null; handle: string | null; followers_count: number; }[]' but required in type '{ business_id?: string | null | undefined; created_at?: string | null | undefined; followers_count?: number | null | undefined; handle: string; id?: string | undefined; platform_id?: number | ... 1 more ... | undefined; updated_at?: string | ... 1 more ... | undefined; }'.
  Overload 2 of 2, '(values: { business_id?: string | null | undefined; created_at?: string | null | undefined; followers_count?: number | null | undefined; handle: string; id?: string | undefined; platform_id?: number | ... 1 more ... | undefined; updated_at?: string | ... 1 more ... | undefined; }[], options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '{ business_id: string; platform_id: number | null; handle: string | null; followers_count: number; }[]' is not assignable to parameter of type '{ business_id?: string | null | undefined; created_at?: string | null | undefined; followers_count?: number | null | undefined; handle: string; id?: string | undefined; platform_id?: number | ... 1 more ... | undefined; updated_at?: string | ... 1 more ... | undefined; }[]'.
      Type '{ business_id: string; platform_id: number | null; handle: string | null; followers_count: number; }' is not assignable to type '{ business_id?: string | null | undefined; created_at?: string | null | undefined; followers_count?: number | null | undefined; handle: string; id?: string | undefined; platform_id?: number | ... 1 more ... | undefined; updated_at?: string | ... 1 more ... | undefined; }'.
        Types of property 'handle' are incompatible.
          Type 'string | null' is not assignable to type 'string'.
            Type 'null' is not assignable to type 'string'.
src/lib/profiles.ts(781,27): error TS2339: Property 'is_available' does not exist on type 'PricingPackage'.
src/lib/profiles.ts(846,7): error TS7034: Variable 'categories' implicitly has type 'any[]' in some locations where its type cannot be determined.
src/lib/profiles.ts(868,7): error TS7034: Variable 'platforms' implicitly has type 'any[]' in some locations where its type cannot be determined.
src/lib/profiles.ts(870,35): error TS2345: Argument of type '(platform: PlatformData) => { platform_id: number; platform_name: string; platform_icon: string; handle: string | null; followers_count: number | null; }' is not assignable to parameter of type '(value: { platform_id: number | null; handle: string; followers_count: number | null; platforms: { name: string; icon: string | null; }; }, index: number, array: { platform_id: number | null; handle: string; followers_count: number | null; platforms: { ...; }; }[]) => { ...; }'.
  Types of parameters 'platform' and 'value' are incompatible.
    Type '{ platform_id: number | null; handle: string; followers_count: number | null; platforms: { name: string; icon: string | null; }; }' is not assignable to type 'PlatformData'.
      Types of property 'platform_id' are incompatible.
        Type 'number | null' is not assignable to type 'number'.
          Type 'null' is not assignable to type 'number'.
src/lib/profiles.ts(897,16): error TS7005: Variable 'platforms' implicitly has an 'any[]' type.
src/lib/profiles.ts(898,17): error TS7005: Variable 'categories' implicitly has an 'any[]' type.
src/lib/startup-config.ts(71,31): error TS2554: Expected 0 arguments, but got 1.
src/lib/subscriptions.ts(400,3): error TS2322: Type '{ created_at: string | null; duration_months: number; features: Json; id: string; is_active: boolean | null; plan_name: string; price: number; stripe_price_id: string | null; updated_at: string | null; user_type: string; }[]' is not assignable to type 'SubscriptionPlan[]'.
  Type '{ created_at: string | null; duration_months: number; features: Json; id: string; is_active: boolean | null; plan_name: string; price: number; stripe_price_id: string | null; updated_at: string | null; user_type: string; }' is not assignable to type 'SubscriptionPlan'.
    Types of property 'user_type' are incompatible.
      Type 'string' is not assignable to type '"influencer" | "business"'.
src/lib/subscriptions.ts(425,3): error TS2322: Type '{ cancel_at_period_end: boolean | null; created_at: string | null; current_period_end: string; current_period_start: string; id: string; status: string; stripe_subscription_id: string | null; subscription_plan_id: string; updated_at: string | null; user_id: string; user_type: string; }' is not assignable to type 'UserSubscription'.
  Types of property 'user_type' are incompatible.
    Type 'string' is not assignable to type '"influencer" | "business"'.
src/lib/subscriptions.ts(499,24): error TS2551: Property 'subscription_plans' does not exist on type 'UserSubscription'. Did you mean 'subscription_plan_id'?
src/lib/types.ts(43,18): error TS2430: Interface 'CampaignDetails' incorrectly extends interface '{ activated_at: string | null; additional_notes: string | null; age_range_max: number | null; age_range_min: number | null; application_deadline: string | null; applications_count: number | null; ... 33 more ...; views_count: number | null; }'.
  Types of property 'age_range_max' are incompatible.
    Type 'number | undefined' is not assignable to type 'number | null'.
      Type 'undefined' is not assignable to type 'number | null'.
