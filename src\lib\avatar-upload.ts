import { supabase } from './supabase';
import { compressImageWithProgress } from './image-compression';

// Storage bucket configuration
const AVATAR_BUCKET = 'avatars';

// Simplified storage folder structure - organized by user ID
const getStoragePath = (userId: string): string => {
  return userId;
};

export interface AvatarUrls {
  navbar_url: string;
  card_url: string;
  profile_url: string;
  preview_url: string;
}

export interface UploadProgress {
  stage:
    | 'validating'
    | 'compressing'
    | 'uploading'
    | 'updating_profile'
    | 'complete';
  percentage: number;
  message?: string;
}

/**
 * Upload avatar with compression (single optimized image)
 */
export async function uploadAvatarWithCompression(
  userId: string,
  file: File,
  onProgress?: (progress: UploadProgress) => void
): Promise<{
  avatarUrls: AvatarUrls;
  compressionStats: {
    originalSize: number;
    totalCompressedSize: number;
    compressionRatio: number;
  };
}> {
  try {
    // Step 1: Compress image to single optimized version
    onProgress?.({
      stage: 'compressing',
      percentage: 0,
      message: 'Kompresovanje slike...',
    });

    const compressionResult = await compressImageWithProgress(
      file,
      progress => {
        onProgress?.({
          stage: progress.stage,
          percentage: Math.round(progress.percentage * 0.5), // 50% of total progress
          message:
            progress.stage === 'compressing'
              ? 'Optimizovanje slike...'
              : 'Validacija slike...',
        });
      }
    );

    // Step 2: Upload single optimized image to storage
    onProgress?.({
      stage: 'uploading',
      percentage: 50,
      message: 'Upload slike na server...',
    });

    const fileName = `avatar-${Date.now()}.webp`;
    const filePath = `${getStoragePath(userId)}/${fileName}`;

    // Delete old avatar files first
    await deleteOldAvatarFiles(userId);

    const { error: uploadError } = await supabase.storage
      .from(AVATAR_BUCKET)
      .upload(filePath, compressionResult.compressedFile, {
        cacheControl: '3600',
        upsert: true,
      });

    if (uploadError) {
      throw new Error(`Upload failed: ${uploadError.message}`);
    }

    // Get public URL
    const { data } = supabase.storage
      .from(AVATAR_BUCKET)
      .getPublicUrl(filePath);

    const avatarUrl = data.publicUrl;

    // Create avatar URLs object - all point to the same optimized image
    const avatarUrls: AvatarUrls = {
      navbar_url: avatarUrl,
      card_url: avatarUrl,
      profile_url: avatarUrl,
      preview_url: avatarUrl,
    };

    onProgress?.({
      stage: 'updating_profile',
      percentage: 80,
      message: 'Ažuriranje profila...',
    });

    // Step 3: Update profile in database (maintain backward compatibility)
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        avatar_url: avatarUrl, // Main avatar URL
        navbar_avatar_url: avatarUrl, // All point to the same image
        card_avatar_url: avatarUrl,
        profile_avatar_url: avatarUrl,
        preview_avatar_url: avatarUrl,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (updateError) {
      throw new Error(`Profile update failed: ${updateError.message}`);
    }

    onProgress?.({
      stage: 'complete',
      percentage: 100,
      message: 'Upload završen!',
    });

    return {
      avatarUrls,
      compressionStats: {
        originalSize: compressionResult.originalSize,
        totalCompressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressionRatio,
      },
    };
  } catch (error) {
    console.error('Avatar upload error:', error);
    throw error;
  }
}

/**
 * Delete old avatar files from storage (simplified for single file structure)
 */
export async function deleteOldAvatarFiles(userId: string): Promise<void> {
  try {
    // List all files for this user in their folder
    const { data: files, error } = await supabase.storage
      .from(AVATAR_BUCKET)
      .list(userId, {
        limit: 100,
      });

    if (error) {
      console.error(`Error listing files for user ${userId}:`, error);
      return;
    }

    if (files && files.length > 0) {
      // Get all image files (both old structure with subfolders and new direct files)
      const filesToDelete: string[] = [];

      for (const item of files) {
        if (item.name && item.name.includes('.')) {
          // Direct file in user folder
          filesToDelete.push(`${userId}/${item.name}`);
        } else if (item.name && !item.name.includes('.')) {
          // Legacy subfolder structure - get files inside
          const { data: subFiles, error: subError } = await supabase.storage
            .from(AVATAR_BUCKET)
            .list(`${userId}/${item.name}`);

          if (!subError && subFiles) {
            subFiles.forEach(subFile => {
              filesToDelete.push(`${userId}/${item.name}/${subFile.name}`);
            });
          }
        }
      }

      // Delete all old files
      if (filesToDelete.length > 0) {
        const { error: deleteError } = await supabase.storage
          .from(AVATAR_BUCKET)
          .remove(filesToDelete);

        if (deleteError) {
          console.error(
            `Error deleting old avatar files for user ${userId}:`,
            deleteError
          );
        }
      }
    }
  } catch (error) {
    console.error('Error deleting old avatar files:', error);
    // Don't throw error here as it's not critical
  }
}

/**
 * Get avatar URLs for a user
 */
export async function getAvatarUrls(
  userId: string
): Promise<AvatarUrls | null> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(
        `
        navbar_avatar_url,
        card_avatar_url,
        profile_avatar_url,
        preview_avatar_url,
        avatar_url
      `
      )
      .eq('id', userId)
      .single();

    if (error || !data) {
      return null;
    }

    // Fallback to main avatar_url if specific sizes don't exist
    return {
      navbar_url: data.navbar_avatar_url || data.avatar_url || '',
      card_url: data.card_avatar_url || data.avatar_url || '',
      profile_url: data.profile_avatar_url || data.avatar_url || '',
      preview_url: data.preview_avatar_url || data.avatar_url || '',
    };
  } catch (error) {
    console.error('Error getting avatar URLs:', error);
    return null;
  }
}

/**
 * Check if storage bucket exists and create if needed
 */
export async function ensureAvatarBucket(): Promise<void> {
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('Error listing buckets:', error);
      return;
    }

    const avatarBucket = buckets?.find(bucket => bucket.name === AVATAR_BUCKET);

    if (!avatarBucket) {
      const { error: createError } = await supabase.storage.createBucket(
        AVATAR_BUCKET,
        {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
          fileSizeLimit: 10485760, // 10MB
        }
      );

      if (createError) {
        console.error('Error creating avatar bucket:', createError);
      }
    }
  } catch (error) {
    console.error('Error ensuring avatar bucket:', error);
  }
}
