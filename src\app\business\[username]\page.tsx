import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getPublicBusinessProfile } from '@/lib/profiles';
import { BusinessProfileWithAccessControl } from './BusinessProfileWithAccessControl';

interface PageProps {
  params: Promise<{
    username: string;
  }>;
}

// Metadata za SEO
export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { data: profile } = await getPublicBusinessProfile(
    resolvedParams.username
  );

  if (!profile) {
    return {
      title: 'Biznis nije pronađen - Influexus',
      description: 'Traženi biznis profil nije pronađen.',
    };
  }

  const brandName =
    profile.brand_name || profile.full_name || 'Nepoznat biznis';
  const metaTitle = `${brandName} (@${profile.username}) - Influexus`;

  return {
    title: metaTitle,
    description:
      profile.bio ||
      profile.company_description ||
      `Pogledajte profil biznisa na Influexus platformi.`,
    openGraph: {
      title: metaTitle,
      description: profile.bio || profile.company_description || '',
      images: profile.avatar_url ? [profile.avatar_url] : [],
    },
  };
}

export default async function BusinessProfilePage({ params }: PageProps) {
  const resolvedParams = await params;

  // Dohvati profil podatke
  const { data: profile, error } = await getPublicBusinessProfile(
    resolvedParams.username
  );

  if (error || !profile || !profile.username) {
    notFound();
  }

  // Proslijedi podatke client komponenti koja će provjeriti access control
  return (
    <BusinessProfileWithAccessControl
      profile={profile as any}
      targetUsername={resolvedParams.username}
    />
  );
}
