# 🔒 API Security Implementation Guide

## Overview

This guide explains how to use the comprehensive API security middleware implemented for the Influencer Platform. The middleware provides JWT authentication, rate limiting, CORS protection, request validation, and security headers.

## Components

### 1. Core Authentication (`api-auth.ts`)

- **JWT Token Validation**: Validates Supabase JWT tokens
- **Rate Limiting**: In-memory rate limiting with configurable limits
- **CORS Headers**: Configurable CORS policy
- **Request Body Validation**: Schema-based request validation

### 2. Middleware Wrapper (`api-middleware.ts`)

- **withApiMiddleware**: General-purpose middleware wrapper
- **withWebhookMiddleware**: Specialized for webhook endpoints
- **withPublicMiddleware**: For endpoints that don't require authentication
- **withProtectedMiddleware**: For endpoints that require authentication

## Usage Examples

### Basic Protected Endpoint

```typescript
import { NextResponse } from 'next/server';
import { withProtectedMiddleware, ApiContext } from '@/lib/api-middleware';

async function getUserProfile(context: ApiContext) {
  // context.user is automatically populated with authenticated user data
  const { user } = context;

  // Your endpoint logic here
  return NextResponse.json({
    message: `Hello ${user.email}`,
    userType: user.user_type,
  });
}

export const GET = withProtectedMiddleware(getUserProfile, {
  allowedUserTypes: ['influencer', 'business'], // Optional: restrict by user type
  rateLimit: {
    enabled: true,
    customLimit: { maxRequests: 60, windowMs: 60 * 1000 }, // 60 requests per minute
  },
});
```

### Public Endpoint with Rate Limiting

```typescript
import { NextResponse } from 'next/server';
import { withPublicMiddleware, ApiContext } from '@/lib/api-middleware';

async function getPublicStats(context: ApiContext) {
  // No authentication required
  return NextResponse.json({
    totalInfluencers: 1000,
    totalBusinesses: 500,
  });
}

export const GET = withPublicMiddleware(getPublicStats, {
  rateLimit: {
    enabled: true,
    customLimit: { maxRequests: 100, windowMs: 60 * 1000 }, // 100 requests per minute
  },
});
```

### Webhook Endpoint

```typescript
import { NextResponse } from 'next/server';
import { withWebhookMiddleware, ApiContext } from '@/lib/api-middleware';

const webhookSchema = {
  type: 'object',
  required: ['event_type', 'data'],
  properties: {
    event_type: { type: 'string' },
    data: { type: 'object' },
  },
};

async function handleWebhook(context: ApiContext) {
  const body = await context.request.json();

  // Process webhook
  return NextResponse.json({ received: true });
}

export const POST = withWebhookMiddleware(
  handleWebhook,
  process.env.WEBHOOK_SECRET!,
  {
    validation: {
      enabled: true,
      bodySchema: webhookSchema,
    },
  }
);
```

### Advanced Configuration

```typescript
import { NextResponse } from 'next/server';
import { withProtectedMiddleware, ApiContext } from '@/lib/api-middleware';

const createCampaignSchema = {
  type: 'object',
  required: ['title', 'description', 'budget'],
  properties: {
    title: { type: 'string', minLength: 5, maxLength: 100 },
    description: { type: 'string', minLength: 10, maxLength: 1000 },
    budget: { type: 'number', min: 50, max: 10000 },
    email: { type: 'string', format: 'email' },
  },
};

async function createCampaign(context: ApiContext) {
  const { user } = context;
  const body = await context.request.json();

  // Only business users can create campaigns
  if (user.user_type !== 'business') {
    return NextResponse.json(
      { error: 'Only business users can create campaigns' },
      { status: 403 }
    );
  }

  // Your campaign creation logic here
  return NextResponse.json({
    message: 'Campaign created successfully',
    campaignId: 'camp_123',
  });
}

export const POST = withProtectedMiddleware(createCampaign, {
  allowedUserTypes: ['business'],
  rateLimit: {
    enabled: true,
    strict: true, // Stricter rate limiting
    customLimit: { maxRequests: 10, windowMs: 60 * 1000 }, // 10 requests per minute
  },
  validation: {
    enabled: true,
    bodySchema: createCampaignSchema,
    validateContentType: true,
  },
  cors: {
    enabled: true,
    allowedOrigins: ['https://yourdomain.com'],
  },
  securityHeaders: true,
});
```

## Security Features

### 1. JWT Token Validation

- Validates Supabase JWT tokens from Authorization header or cookies
- Verifies token signature and expiration
- Fetches user profile data for additional validation
- Supports user type restrictions

### 2. Rate Limiting

- In-memory rate limiting (consider Redis for production clusters)
- Configurable limits per endpoint
- Automatic cleanup of expired entries
- Rate limit headers in responses
- IP-based identification

### 3. CORS Protection

- Configurable allowed origins
- Proper preflight handling
- Credential support
- Security-focused defaults

### 4. Request Validation

- JSON schema validation for request bodies
- Content-Type validation
- Required field validation
- Type and format validation
- Custom error messages

### 5. Security Headers

- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin
- Content-Security-Policy

## Environment Variables

Add these to your `.env.local` file:

```env
# Required for webhook validation
SUPABASE_WEBHOOK_SECRET=your_webhook_secret_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# Optional: for CORS configuration
NEXT_PUBLIC_APP_URL=https://yourdomain.com
```

## Middleware Options

### AuthValidationOptions

```typescript
interface AuthValidationOptions {
  requireAuth?: boolean; // Default: true
  allowedUserTypes?: ('influencer' | 'business')[]; // Default: ['influencer', 'business']
  requireBearerToken?: boolean; // Default: true
  webhookSecret?: string; // For webhook endpoints
}
```

### ApiMiddlewareOptions

```typescript
interface ApiMiddlewareOptions extends AuthValidationOptions {
  rateLimit?: {
    enabled?: boolean; // Default: true
    strict?: boolean; // Default: false (uses stricter limits)
    customLimit?: { maxRequests: number; windowMs: number };
  };

  cors?: {
    enabled?: boolean; // Default: true
    allowedOrigins?: string[];
  };

  validation?: {
    enabled?: boolean; // Default: true for POST/PUT/PATCH
    bodySchema?: any; // JSON schema for validation
    validateContentType?: boolean; // Default: true
  };

  securityHeaders?: boolean; // Default: true
}
```

## Best Practices

### 1. Authentication Strategy

- Use `withProtectedMiddleware` for user-specific operations
- Use `withPublicMiddleware` for public data endpoints
- Use `withWebhookMiddleware` for external webhook callbacks
- Always specify `allowedUserTypes` when endpoint is role-specific

### 2. Rate Limiting

- Use stricter limits for sensitive operations (user registration, password reset)
- Use custom limits for high-frequency operations
- Consider implementing user-specific rate limiting for authenticated endpoints

### 3. Request Validation

- Always validate request bodies for POST/PUT/PATCH operations
- Use specific schemas for each endpoint
- Validate email formats, required fields, and data types
- Provide clear error messages for validation failures

### 4. Error Handling

- Never expose sensitive information in error messages
- Log detailed errors server-side for debugging
- Return consistent error response format
- Use appropriate HTTP status codes

### 5. Webhook Security

- Always use webhook secrets for external webhooks
- Implement idempotency for webhook handlers
- Use strict rate limiting for webhook endpoints
- Validate webhook payloads thoroughly

## Migration Guide

To migrate existing API routes:

1. **Install the middleware**:

   ```typescript
   import { withProtectedMiddleware } from '@/lib/api-middleware';
   ```

2. **Wrap your handler**:

   ```typescript
   // Before
   export async function POST(request: NextRequest) {
     // Your logic
   }

   // After
   async function handlePost(context: ApiContext) {
     // Your logic - use context.request instead of request
     // context.user contains authenticated user data
   }

   export const POST = withProtectedMiddleware(handlePost);
   ```

3. **Update request handling**:

   ```typescript
   // Before
   const body = await request.json();

   // After
   const body = await context.request.json();
   ```

4. **Use user context**:

   ```typescript
   // Before - manual token validation
   const authHeader = request.headers.get('authorization');
   // ... validation logic

   // After - automatic validation
   const { user } = context;
   // user.id, user.email, user.user_type are available
   ```

## Monitoring and Logging

The middleware automatically adds performance headers and logs errors. Monitor these for:

- Rate limiting violations
- Authentication failures
- Validation errors
- Response times
- Error rates

Consider implementing additional monitoring with tools like DataDog, New Relic, or custom logging solutions.
