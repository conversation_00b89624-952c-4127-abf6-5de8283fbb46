import { Database } from './database.types';

// Base types from database
export type Campaign = Database['public']['Tables']['campaigns']['Row'];
export type CampaignApplication =
  Database['public']['Tables']['campaign_applications']['Row'];
export type Business = Database['public']['Tables']['businesses']['Row'];
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Influencer = Database['public']['Tables']['influencers']['Row'];
export type Category = Database['public']['Tables']['categories']['Row'];
export type Platform = Database['public']['Tables']['platforms']['Row'];

// Extended platform type with additional fields
export interface PlatformWithDetails extends Platform {
  posts_required?: number;
  content_types?: string[];
}

// Enums
export type ApplicationStatus =
  Database['public']['Enums']['application_status'];
export type CampaignStatus = Database['public']['Enums']['campaign_status'];
export type ContentType = Database['public']['Enums']['content_type'];
export type UserType = Database['public']['Enums']['user_type'];

// Complex joined types for campaign details
export interface CampaignWithBusiness extends Campaign {
  business: Business & {
    profile: Profile;
  };
  platforms: Platform[];
  categories: Category[];
}

// Application with related data
export interface ApplicationWithDetails extends CampaignApplication {
  campaign: CampaignWithBusiness;
  influencer: Influencer & {
    profile: Profile;
    categories: string[];
    platforms: {
      platform_name: string;
      handle: string | null;
      followers_count: number | null;
    }[];
  };
}

// Campaign with application count and business info
export interface CampaignDetails extends Campaign {
  business?: Business & {
    profile?: Profile;
  };
  platforms?: PlatformWithDetails[];
  categories?: Category[];
  // Business profile fields (flattened for easier access)
  business_avatar?: string | null;
  company_name?: string | null;
  business_username?: string | null;
  industry?: string | null;
  // Derived/calculated fields only (not overriding base Campaign fields)
}

// Influencer application response type
export interface InfluencerApplicationResponse {
  hasApplied: boolean;
  application?: CampaignApplication;
  appliedAt?: string | null;
  status?: ApplicationStatus | null;
}

// Target audience type (from campaign.target_audience JSON field)
export interface TargetAudience {
  age_range?: {
    min: number;
    max: number;
  };
  gender?: string[];
  location?: string[];
  interests?: string[];
  languages?: string[];
}

// Campaign form data
export interface CampaignFormData {
  title: string;
  description: string;
  budget?: number;
  content_types: ContentType[];
  start_date?: string;
  end_date?: string;
  requirements?: string;
  deliverables?: string;
  campaign_goal?: string;
  product_description?: string;
  target_audience?: TargetAudience;
  show_business_name?: boolean;
}

// Application form data
export interface ApplicationFormData {
  proposed_rate: number;
  proposal_text?: string;
  portfolio_links?: string[];
  experience_relevant?: string;
  audience_insights?: string;
  delivery_timeframe?: string;
  available_start_date?: string;
  additional_services?: string;
}

// Platform data for profiles
export interface PlatformData {
  platform_id: number | null;
  handle: string | null;
  followers_count: number | null;
  is_verified?: boolean | null;
  platforms: {
    name: string;
    icon: string | null;
  };
}

// Pricing package type
export interface PricingPackage {
  id: number;
  influencer_id: string | null;
  platform_id: number | null;
  content_type_id: number | null;
  price: number;
  quantity: number | null;
  currency: string | null;
  auto_generated_name: string | null;
  video_duration: string | null;
  is_available: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

// Subscription plan type
export interface SubscriptionPlan {
  id: string;
  plan_name: string;
  price: number;
  duration_months: number;
  user_type: 'influencer' | 'business';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  features: any; // Json type
  is_active: boolean | null;
  stripe_price_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

// User subscription type
export interface UserSubscription {
  id: string;
  user_id: string;
  subscription_plan_id: string;
  user_type: 'influencer' | 'business';
  status: string;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean | null;
  stripe_subscription_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

// Profile with user type info
export interface ProfileWithUserInfo extends Profile {
  user_type: 'influencer' | 'business';
}
