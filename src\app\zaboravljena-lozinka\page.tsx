'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ArrowLeft, Mail, CheckCircle } from 'lucide-react';

const forgotPasswordSchema = z.object({
  email: z.string().email('Unesite validnu email adresu'),
});

type ForgotPasswordForm = z.infer<typeof forgotPasswordSchema>;

export default function ZaboravljenaLozinkaPage() {
  const router = useRouter();
  const { resetPassword } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    getValues,
  } = useForm<ForgotPasswordForm>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordForm) => {
    setIsLoading(true);

    try {
      const { error } = await resetPassword(data.email);

      if (error) {
        if (error.message.includes('User not found')) {
          setError('email', {
            message: 'Korisnik sa ovom email adresom ne postoji',
          });
        } else {
          setError('root', { message: error.message });
        }
        return;
      }

      setIsSuccess(true);
    } catch {
      setError('root', { message: 'Došlo je do greške. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#E02F75] via-[#6700A3] to-[#050C38] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-40 right-10 w-28 h-28 bg-white/10 rounded-full blur-xl"></div>
      </div>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
        <Card className="w-full max-w-md glass-instagram border-white/20 shadow-2xl">
          <CardHeader className="text-center">
            <div className="flex items-center justify-center mb-4">
              <Link
                href="/prijava"
                className="absolute left-6 top-6 text-white/70 hover:text-white transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </div>

            {!isSuccess ? (
              <>
                <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/20">
                  <Mail className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-2xl text-white">
                  Zaboravili ste lozinku?
                </CardTitle>
                <CardDescription className="text-white/70">
                  Unesite svoju email adresu i poslat ćemo vam link za
                  resetovanje lozinke
                </CardDescription>
              </>
            ) : (
              <>
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-500/30">
                  <CheckCircle className="h-8 w-8 text-green-400" />
                </div>
                <CardTitle className="text-2xl text-white">
                  Email je poslan!
                </CardTitle>
                <CardDescription className="text-white/70">
                  Provjerite svoj email ({getValues('email')}) i kliknite na
                  link za resetovanje lozinke
                </CardDescription>
              </>
            )}
          </CardHeader>

          <CardContent>
            {!isSuccess ? (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                {/* Email */}
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-white">
                    Email adresa
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...register('email')}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:ring-white/20"
                  />
                  {errors.email && (
                    <p className="text-red-300 text-sm">
                      {errors.email.message}
                    </p>
                  )}
                </div>

                {/* Error Message */}
                {errors.root && (
                  <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
                    <p className="text-red-300 text-sm text-center">
                      {errors.root.message}
                    </p>
                  </div>
                )}

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-white text-[#6700A3] hover:bg-white/90 font-semibold py-3 transition-all duration-200 disabled:opacity-50"
                >
                  {isLoading ? 'Šalje se...' : 'Pošalji link za resetovanje'}
                </Button>
              </form>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                  <p className="text-green-300 text-sm text-center">
                    Link za resetovanje lozinke je poslan na vašu email adresu.
                    Provjerite i spam folder ako ne vidite email.
                  </p>
                </div>

                <Button
                  onClick={() => router.push('/prijava')}
                  className="w-full bg-white text-[#6700A3] hover:bg-white/90 font-semibold py-3 transition-all duration-200"
                >
                  Nazad na prijavu
                </Button>
              </div>
            )}

            {/* Back to Login */}
            {!isSuccess && (
              <div className="mt-6 text-center">
                <Link
                  href="/prijava"
                  className="text-sm text-white/80 hover:text-white hover:underline transition-colors"
                >
                  Nazad na prijavu
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
