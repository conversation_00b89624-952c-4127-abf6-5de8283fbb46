import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface ChatButtonProps extends React.ComponentProps<'button'> {
  variant?: 'primary' | 'secondary';
}

const ChatButton = React.forwardRef<HTMLButtonElement, ChatButtonProps>(
  ({ className, variant = 'primary', ...props }, ref) => {
    const variantClasses = {
      primary:
        'bg-instagram-subtle text-white hover:opacity-90 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-blue-200/50 dark:hover:shadow-blue-900/30',
      secondary:
        'bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 text-blue-700 hover:bg-gradient-to-r hover:from-blue-100 hover:to-purple-100 hover:border-blue-300 transition-all duration-300 hover:scale-[1.02]',
    };

    return (
      <Button
        ref={ref}
        className={cn(variantClasses[variant], className)}
        {...props}
      />
    );
  }
);

ChatButton.displayName = 'ChatButton';

export { ChatButton };
