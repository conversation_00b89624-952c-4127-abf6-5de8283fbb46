Failed to compile.

./src/app/campaigns/[id]/page.tsx
11:9 Error: Replace `·CampaignDetails,·InfluencerApplicationResponse,·Category·` with `⏎··CampaignDetails,⏎··InfluencerApplicationResponse,⏎··Category,⏎` prettier/prettier

./src/app/dashboard/campaigns/page.tsx
224:6 Warning: React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
354:6 Warning: React Hook useEffect has missing dependencies: 'handleSort' and 'sortColumn'. Either include them or remove the dependency array. react-hooks/exhaustive-deps

./src/app/dashboard/campaigns/test/page.tsx
200:6 Warning: React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/dashboard/chat/permissions/page.tsx
6:29 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
6:41 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/dashboard/influencer/account/page.tsx
21:3 Error: 'Mail' is defined but never used. @typescript-eslint/no-unused-vars
83:10 Error: 'profile' is assigned a value but never used. @typescript-eslint/no-unused-vars
83:42 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
84:10 Error: 'influencer' is assigned a value but never used. @typescript-eslint/no-unused-vars
84:48 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
104:6 Warning: React Hook useEffect has missing dependencies: 'checkSubscription' and 'loadProfile'. Either include them or remove the dependency array. react-hooks/exhaustive-deps
142:14 Error: 'err' is defined but never used. @typescript-eslint/no-unused-vars
193:14 Error: 'err' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/dashboard/influencer/applications/page.tsx
46:6 Warning: React Hook useEffect has a missing dependency: 'fetchApplications'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
80:15 Error: 'data' is assigned a value but never used. @typescript-eslint/no-unused-vars

./src/app/dashboard/influencer/applications/[id]/page.tsx
11:3 Error: 'ArrowLeft' is defined but never used. @typescript-eslint/no-unused-vars
36:10 Error: 'ChatEnableButton' is defined but never used. @typescript-eslint/no-unused-vars
49:50 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
94:6 Warning: React Hook useEffect has a missing dependency: 'loadApplication'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
110:15 Error: 'data' is assigned a value but never used. @typescript-eslint/no-unused-vars
155:9 Error: 'getJobStatusBadge' is assigned a value but never used. @typescript-eslint/no-unused-vars
595:36 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/offers/page.tsx
38:15 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
51:6 Warning: React Hook useEffect has a missing dependency: 'loadOffers'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
67:60 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/offers/[id]/page.tsx
12:3 Error: 'ArrowLeft' is defined but never used. @typescript-eslint/no-unused-vars
37:3 Error: 'getUserJobCompletions' is defined but never used. @typescript-eslint/no-unused-vars
39:8 Error: 'JobCompletion' is defined but never used. @typescript-eslint/no-unused-vars
51:54 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
53:50 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
60:6 Warning: React Hook useEffect has missing dependencies: 'loadJobCompletion' and 'loadOffer'. Either include them or remove the dependency array. react-hooks/exhaustive-deps

./src/app/dashboard/influencer/packages/page.tsx
30:10 Error: 'influencer' is assigned a value but never used. @typescript-eslint/no-unused-vars
30:48 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
32:52 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
41:6 Warning: React Hook useEffect has a missing dependency: 'loadInfluencer'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
101:21 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
164:37 Error: 'index' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/dashboard/influencer/page.tsx
13:48 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
22:6 Warning: React Hook useEffect has missing dependencies: 'checkSubscription' and 'loadInfluencer'. Either include them or remove the dependency array. react-hooks/exhaustive-deps

./src/app/dashboard/influencer/pricing/page.tsx
84:6 Warning: React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/dashboard/influencer/profile/page.tsx
18:3 Error: 'updateInfluencer' is defined but never used. @typescript-eslint/no-unused-vars
30:3 Error: 'Instagram' is defined but never used. @typescript-eslint/no-unused-vars
31:3 Error: 'Youtube' is defined but never used. @typescript-eslint/no-unused-vars
45:7 Error: 'TikTokIcon' is assigned a value but never used. @typescript-eslint/no-unused-vars
80:48 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
81:42 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
139:6 Warning: React Hook useEffect has missing dependencies: 'checkSubscription' and 'loadData'. Either include them or remove the dependency array. react-hooks/exhaustive-deps
236:43 Error: 'platformsError' is assigned a value but never used. @typescript-eslint/no-unused-vars
461:37 Error: 'avatarUrls' is defined but never used. @typescript-eslint/no-unused-vars
485:39 Error: 'avatarUrls' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/dashboard/influencer/support/page.tsx
29:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars
63:1 Error: Delete `················` prettier/prettier
70:78 Error: Insert `⏎···············` prettier/prettier
83:75 Error: Replace `Javite·nam·se·za:` with `⏎······················Javite·nam·se·za:⏎····················` prettier/prettier
112:83 Error: Replace `Kontaktirajte·nas</h2>` with `⏎··················Kontaktirajte·nas` prettier/prettier
113:17 Error: Insert `</h2>⏎` prettier/prettier
116:80 Error: Replace `{supportEmail}` with `⏎····················{supportEmail}⏎··················` prettier/prettier
157:2 Error: Insert `⏎` prettier/prettier

./src/app/dashboard/influencer/zavrseni-poslovi/page.tsx
4:10 Error: 'Clock' is defined but never used. @typescript-eslint/no-unused-vars
4:17 Error: 'CheckCircle' is defined but never used. @typescript-eslint/no-unused-vars
4:30 Error: 'XCircle' is defined but never used. @typescript-eslint/no-unused-vars
36:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars
51:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/dashboard/job-completions/page.tsx
26:16 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/dashboard/notifications/page.tsx
5:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars
16:3 Error: 'Building2' is defined but never used. @typescript-eslint/no-unused-vars
17:3 Error: 'User' is defined but never used. @typescript-eslint/no-unused-vars
104:5 Error: 'error' is assigned a value but never used. @typescript-eslint/no-unused-vars
105:35 Error: 'reset' is assigned a value but never used. @typescript-eslint/no-unused-vars

./src/app/dashboard/page.tsx
18:3 Error: 'Building2' is defined but never used. @typescript-eslint/no-unused-vars
19:3 Error: 'Settings' is defined but never used. @typescript-eslint/no-unused-vars
21:3 Error: 'CheckCircle' is defined but never used. @typescript-eslint/no-unused-vars
22:3 Error: 'XCircle' is defined but never used. @typescript-eslint/no-unused-vars
30:10 Error: 'profile' is assigned a value but never used. @typescript-eslint/no-unused-vars
30:42 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
43:6 Warning: React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/email-demo/page.tsx
15:3 Error: 'Select' is defined but never used. @typescript-eslint/no-unused-vars
16:3 Error: 'SelectContent' is defined but never used. @typescript-eslint/no-unused-vars
17:3 Error: 'SelectItem' is defined but never used. @typescript-eslint/no-unused-vars
18:3 Error: 'SelectTrigger' is defined but never used. @typescript-eslint/no-unused-vars
19:3 Error: 'SelectValue' is defined but never used. @typescript-eslint/no-unused-vars
28:10 Error: 'emailType' is assigned a value but never used. @typescript-eslint/no-unused-vars
28:21 Error: 'setEmailType' is assigned a value but never used. @typescript-eslint/no-unused-vars

./src/app/influencer/[username]/InfluencerProfileClient.tsx
5:3 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
6:3 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
7:3 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
8:3 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars
9:3 Error: 'CardDescription' is defined but never used. @typescript-eslint/no-unused-vars
11:10 Error: 'GradientCard' is defined but never used. @typescript-eslint/no-unused-vars
14:10 Error: 'Avatar' is defined but never used. @typescript-eslint/no-unused-vars
14:18 Error: 'AvatarFallback' is defined but never used. @typescript-eslint/no-unused-vars
14:34 Error: 'AvatarImage' is defined but never used. @typescript-eslint/no-unused-vars
17:10 Error: 'TabsWithBadge' is defined but never used. @typescript-eslint/no-unused-vars
18:10 Error: 'TabsContent' is defined but never used. @typescript-eslint/no-unused-vars
25:3 Error: 'DialogTrigger' is defined but never used. @typescript-eslint/no-unused-vars
31:3 Error: 'Verified' is defined but never used. @typescript-eslint/no-unused-vars
36:3 Error: 'CheckCircle' is defined but never used. @typescript-eslint/no-unused-vars
49:8 Error: 'Link' is defined but never used. @typescript-eslint/no-unused-vars
53:10 Error: 'Rating' is defined but never used. @typescript-eslint/no-unused-vars
59:10 Error: 'formatResetDate' is defined but never used. @typescript-eslint/no-unused-vars
74:58 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
75:58 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
95:6 Warning: React Hook useEffect has a missing dependency: 'loadBusinessProfile'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
142:9 Error: 'getInitials' is assigned a value but never used. @typescript-eslint/no-unused-vars
215:36 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/influencer/[username]/InfluencerProfileWithAccessControl.tsx
31:6 Warning: React Hook useEffect has a missing dependency: 'checkAccess'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/influencer/[username]/page.tsx
3:10 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
3:16 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
3:29 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
3:41 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars
4:10 Error: 'Button' is defined but never used. @typescript-eslint/no-unused-vars
5:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars
6:10 Error: 'Avatar' is defined but never used. @typescript-eslint/no-unused-vars
6:18 Error: 'AvatarFallback' is defined but never used. @typescript-eslint/no-unused-vars
6:34 Error: 'AvatarImage' is defined but never used. @typescript-eslint/no-unused-vars
8:3 Error: 'MapPin' is defined but never used. @typescript-eslint/no-unused-vars
9:3 Error: 'Users' is defined but never used. @typescript-eslint/no-unused-vars
10:3 Error: 'Star' is defined but never used. @typescript-eslint/no-unused-vars
11:3 Error: 'Verified' is defined but never used. @typescript-eslint/no-unused-vars
12:3 Error: 'Mail' is defined but never used. @typescript-eslint/no-unused-vars
13:3 Error: 'MessageCircle' is defined but never used. @typescript-eslint/no-unused-vars
14:3 Error: 'Calendar' is defined but never used. @typescript-eslint/no-unused-vars
15:3 Error: 'Globe' is defined but never used. @typescript-eslint/no-unused-vars
19:8 Error: 'Link' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/marketplace/influencers/page.tsx
3:10 Error: 'useState' is defined but never used. @typescript-eslint/no-unused-vars
23:9 Error: 'router' is assigned a value but never used. @typescript-eslint/no-unused-vars
26:19 Error: 'setFilters' is assigned a value but never used. @typescript-eslint/no-unused-vars

./src/app/page.tsx
9:3 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
10:3 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
11:3 Error: 'CardDescription' is defined but never used. @typescript-eslint/no-unused-vars
12:3 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
13:3 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/politika-privatnosti/page.tsx
224:24 Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`. react/no-unescaped-entities
224:41 Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`. react/no-unescaped-entities

./src/app/prijava/page.tsx
81:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/profil/edit/page.tsx
6:10 Error: 'getProfile' is defined but never used. @typescript-eslint/no-unused-vars
33:9 Error: 'loadExistingData' is assigned a value but never used. @typescript-eslint/no-unused-vars
48:35 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
54:39 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
64:34 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
72:36 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
74:27 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
75:24 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
187:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars
210:12 Error: 'Link' is not defined. react/jsx-no-undef
214:14 Error: 'ArrowLeft' is not defined. react/jsx-no-undef
231:12 Error: 'Card' is not defined. react/jsx-no-undef
232:14 Error: 'CardHeader' is not defined. react/jsx-no-undef
233:16 Error: 'CardTitle' is not defined. react/jsx-no-undef
234:16 Error: 'CardDescription' is not defined. react/jsx-no-undef
238:14 Error: 'CardContent' is not defined. react/jsx-no-undef
247:20 Error: 'CategorySelector' is not defined. react/jsx-no-undef
260:22 Error: 'Label' is not defined. react/jsx-no-undef
261:22 Error: 'Input' is not defined. react/jsx-no-undef
275:22 Error: 'Label' is not defined. react/jsx-no-undef
276:22 Error: 'Textarea' is not defined. react/jsx-no-undef
292:24 Error: 'Label' is not defined. react/jsx-no-undef
293:24 Error: 'Input' is not defined. react/jsx-no-undef
307:24 Error: 'Label' is not defined. react/jsx-no-undef
308:24 Error: 'Input' is not defined. react/jsx-no-undef
324:24 Error: 'Label' is not defined. react/jsx-no-undef
346:24 Error: 'Label' is not defined. react/jsx-no-undef
347:24 Error: 'Input' is not defined. react/jsx-no-undef
368:20 Error: 'PlatformSelector' is not defined. react/jsx-no-undef
378:20 Error: 'PricingMatrix' is not defined. react/jsx-no-undef
388:20 Error: 'Button' is not defined. react/jsx-no-undef
396:20 Error: 'Button' is not defined. react/jsx-no-undef
404:26 Error: 'Save' is not defined. react/jsx-no-undef

./src/app/profil/kreiranje/biznis/onboarding/page.tsx
54:10 Error: 'isInitialized' is assigned a value but never used. @typescript-eslint/no-unused-vars
54:25 Error: 'setIsInitialized' is assigned a value but never used. @typescript-eslint/no-unused-vars
74:6 Warning: React Hook useEffect has a missing dependency: 'loadOnboardingProgress'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
126:30 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
177:29 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/influencer/onboarding/page.tsx
175:6 Warning: React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
198:25 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
372:59 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
392:29 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/page.tsx
7:10 Error: 'Button' is defined but never used. @typescript-eslint/no-unused-vars
20:10 Error: 'profile' is assigned a value but never used. @typescript-eslint/no-unused-vars
20:42 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
32:6 Warning: React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/app/promjena-lozinke/page.tsx
20:23 Error: 'Lock' is defined but never used. @typescript-eslint/no-unused-vars
51:50 Error: 'user' is assigned a value but never used. @typescript-eslint/no-unused-vars
96:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/registracija/business/page.tsx
46:11 Error: 'signUp' is assigned a value but never used. @typescript-eslint/no-unused-vars
83:21 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
100:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/registracija/influencer/page.tsx
42:11 Error: 'signUp' is assigned a value but never used. @typescript-eslint/no-unused-vars
75:21 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
92:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/reset-password/page.tsx
76:15 Error: 'accessToken' is assigned a value but never used. @typescript-eslint/no-unused-vars
77:15 Error: 'refreshToken' is assigned a value but never used. @typescript-eslint/no-unused-vars
120:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/app/uslovi-koristenja/page.tsx
222:48 Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`. react/no-unescaped-entities
222:59 Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`. react/no-unescaped-entities

./src/app/zaboravljena-lozinka/page.tsx
62:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/applications/WithdrawApplicationModal.tsx
3:10 Error: 'useState' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/campaigns/BusinessOfferCard.tsx
10:3 Error: 'User' is defined but never used. @typescript-eslint/no-unused-vars
11:3 Error: 'MessageCircle' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/campaigns/campaign-application-form.tsx
9:3 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
10:3 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
11:3 Error: 'CardDescription' is defined but never used. @typescript-eslint/no-unused-vars
12:3 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
13:3 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars
26:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars
31:3 Error: 'Calendar' is defined but never used. @typescript-eslint/no-unused-vars
33:11 Error: 'LinkIcon' is defined but never used. @typescript-eslint/no-unused-vars
34:3 Error: 'User' is defined but never used. @typescript-eslint/no-unused-vars
35:3 Error: 'Clock' is defined but never used. @typescript-eslint/no-unused-vars
43:3 Error: 'Dialog' is defined but never used. @typescript-eslint/no-unused-vars
44:3 Error: 'DialogContent' is defined but never used. @typescript-eslint/no-unused-vars
45:3 Error: 'DialogDescription' is defined but never used. @typescript-eslint/no-unused-vars
46:3 Error: 'DialogHeader' is defined but never used. @typescript-eslint/no-unused-vars
47:3 Error: 'DialogTitle' is defined but never used. @typescript-eslint/no-unused-vars
48:3 Error: 'DialogTrigger' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/campaigns/campaign-filters.tsx
87:66 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
345:75 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/campaigns/create-campaign-form.tsx
17:3 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
18:3 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
19:3 Error: 'CardDescription' is defined but never used. @typescript-eslint/no-unused-vars
20:3 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
21:3 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars
137:3 Error: 'onCancel' is defined but never used. @typescript-eslint/no-unused-vars
138:3 Error: 'onSaveDraft' is defined but never used. @typescript-eslint/no-unused-vars
141:3 Error: 'isEditing' is defined but never used. @typescript-eslint/no-unused-vars
197:38 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
279:9 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
358:19 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
359:28 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
363:19 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
364:20 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
383:19 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
448:18 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars
468:18 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars
842:73 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/campaigns/InfluencerApplicationCard.tsx
5:3 Error: 'Euro' is defined but never used. @typescript-eslint/no-unused-vars
15:10 Error: 'formatDate' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/chat/Chat.tsx
25:6 Warning: React Hook useEffect has missing dependencies: 'loadRoom' and 'onRoomStateChange'. Either include them or remove the dependency array. If 'onRoomStateChange' changes too often, find the parent component that defines it and wrap that definition in useCallback. react-hooks/exhaustive-deps

./src/components/chat/ChatContextBar.tsx
4:10 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
4:16 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
10:3 Error: 'DollarSign' is defined but never used. @typescript-eslint/no-unused-vars
55:6 Warning: React Hook useEffect has a missing dependency: 'loadContextData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/chat/ChatEnableButton.tsx
7:39 Error: 'Users' is defined but never used. @typescript-eslint/no-unused-vars
41:48 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
49:6 Warning: React Hook useEffect has a missing dependency: 'checkChatStatus'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/chat/ChatList.tsx
4:10 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
4:16 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
4:29 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
4:41 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars
10:3 Error: 'MessageCircle' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/chat/ChatPermissionCard.tsx
8:32 Error: 'X' is defined but never used. @typescript-eslint/no-unused-vars
38:9 Error: 'getStatusText' is assigned a value but never used. @typescript-eslint/no-unused-vars

./src/components/chat/ChatPermissionStatus.tsx
13:39 Error: 'X' is defined but never used. @typescript-eslint/no-unused-vars
47:6 Warning: React Hook useEffect has a missing dependency: 'loadPermission'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/chat/ChatRoom.tsx
14:3 Error: 'User' is defined but never used. @typescript-eslint/no-unused-vars
48:20 Error: 'setIsTyping' is assigned a value but never used. @typescript-eslint/no-unused-vars
120:6 Warning: React Hook useEffect has a missing dependency: 'loadMessages'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/dashboard/DashboardLayout.tsx
25:42 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
39:6 Warning: React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/job-completion/CampaignJobSubmissionForm.tsx
16:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/job-completion/DirectOfferJobSubmissionForm.tsx
16:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/job-completion/JobCompletionCard.tsx
117:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars
144:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/job-completion/JobSubmissionForm.tsx
9:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars
92:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/job-completion/RejectJobModal.tsx
61:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/marketplace/filters.tsx
77:10 Error: 'followersOpen' is assigned a value but never used. @typescript-eslint/no-unused-vars
77:25 Error: 'setFollowersOpen' is assigned a value but never used. @typescript-eslint/no-unused-vars
438:54 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/marketplace/horizontal-filters.tsx
84:59 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/marketplace/InfluencerCard.tsx
20:3 Error: 'filters' is defined but never used. @typescript-eslint/no-unused-vars
67:9 Error: 'displayPlatforms' is assigned a value but never used. @typescript-eslint/no-unused-vars
73:9 Error: 'mainCategory' is assigned a value but never used. @typescript-eslint/no-unused-vars

./src/components/modals/PromoteCampaignModal.tsx
18:8 Error: 'FeaturedCampaignPrice' is defined but never used. @typescript-eslint/no-unused-vars
38:3 Error: 'onSuccess' is defined but never used. @typescript-eslint/no-unused-vars
107:36 Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`. react/no-unescaped-entities
108:70 Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`. react/no-unescaped-entities

./src/components/modals/SupportModal.tsx
40:1 Error: Delete `⏎` prettier/prettier
47:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars
64:21 Error: Replace `⏎········className="sm:max-w-md·bg-card/95·backdrop-blur-md·border·border-purple-500/20·shadow-2xl·shadow-purple-500/20·rounded-2xl·overflow-hidden"⏎······` with `·className="sm:max-w-md·bg-card/95·backdrop-blur-md·border·border-purple-500/20·shadow-2xl·shadow-purple-500/20·rounded-2xl·overflow-hidden"` prettier/prettier
87:1 Error: Delete `··············` prettier/prettier
94:79 Error: Insert `⏎·············` prettier/prettier
107:65 Error: Replace `Javite·nam·se·za:` with `⏎····················Javite·nam·se·za:⏎··················` prettier/prettier
120:73 Error: Replace `Kontaktirajte·nas</h4>` with `⏎················Kontaktirajte·nas` prettier/prettier
121:15 Error: Insert `</h4>⏎` prettier/prettier
124:76 Error: Replace `{supportEmail}` with `⏎··················{supportEmail}⏎················` prettier/prettier
150:1 Error: Delete `··············` prettier/prettier

./src/components/navigation/DesktopHeader.tsx
8:3 Error: 'FileText' is defined but never used. @typescript-eslint/no-unused-vars
11:3 Error: 'MessageCircle' is defined but never used. @typescript-eslint/no-unused-vars
12:3 Error: 'ChevronDown' is defined but never used. @typescript-eslint/no-unused-vars
39:12 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
45:9 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
51:9 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
211:40 Error: Delete `·` prettier/prettier
212:52 Error: Delete `·` prettier/prettier

./src/components/navigation/MobileBottomNavigation.tsx
8:3 Error: 'FileText' is defined but never used. @typescript-eslint/no-unused-vars
10:3 Error: 'MessageCircle' is defined but never used. @typescript-eslint/no-unused-vars
139:40 Error: Delete `·` prettier/prettier
140:52 Error: Delete `·` prettier/prettier
147:40 Error: Delete `·` prettier/prettier
148:66 Error: Delete `·` prettier/prettier

./src/components/navigation/MobileTopNavbar.tsx
16:3 Error: 'DropdownMenuLabel' is defined but never used. @typescript-eslint/no-unused-vars
21:3 Error: 'ChevronDown' is defined but never used. @typescript-eslint/no-unused-vars
28:3 Error: 'Send' is defined but never used. @typescript-eslint/no-unused-vars
30:3 Error: 'Users' is defined but never used. @typescript-eslint/no-unused-vars
34:3 Error: 'ArrowLeft' is defined but never used. @typescript-eslint/no-unused-vars
53:12 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
147:6 Warning: React Hook useEffect has missing dependencies: 'loadNotifications' and 'loadUnreadCount'. Either include them or remove the dependency array. react-hooks/exhaustive-deps
323:22 Error: Delete `·` prettier/prettier
324:32 Error: Delete `·` prettier/prettier
325:26 Error: Delete `·` prettier/prettier
336:33 Error: Delete `·` prettier/prettier
337:26 Error: Delete `·` prettier/prettier
346:1 Error: Delete `················` prettier/prettier
469:35 Error: Insert `⏎·································` prettier/prettier
470:1 Error: Replace `··································notification.read·⏎····································?·'text-muted-foreground/60'·` with `····································notification.read⏎······································?·'text-muted-foreground/60'` prettier/prettier
472:37 Error: Insert `··` prettier/prettier
473:33 Error: Replace `}`}`with`··}`}⏎································` prettier/prettier
502:28 Error: Replace `·href="/dashboard/notifications"·className="text-sm·font-medium"` with `⏎························href="/dashboard/notifications"⏎························className="text-sm·font-medium"⏎······················` prettier/prettier
594:13 Error: Delete `⏎` prettier/prettier

./src/components/navigation/ResponsiveNavigation.tsx
8:12 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/notifications/NotificationDropdown.tsx
13:3 Error: 'DropdownMenuItem' is defined but never used. @typescript-eslint/no-unused-vars
23:3 Error: 'Building2' is defined but never used. @typescript-eslint/no-unused-vars
24:3 Error: 'User' is defined but never used. @typescript-eslint/no-unused-vars
51:6 Warning: React Hook useEffect has missing dependencies: 'loadNotifications' and 'loadUnreadCount'. Either include them or remove the dependency array. react-hooks/exhaustive-deps

./src/components/offers/DirectOfferForm.tsx
9:3 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
10:3 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
11:3 Error: 'CardDescription' is defined but never used. @typescript-eslint/no-unused-vars
12:3 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
13:3 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars
19:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars
27:36 Error: 'X' is defined but never used. @typescript-eslint/no-unused-vars
128:23 Error: 'error' is assigned a value but never used. @typescript-eslint/no-unused-vars
138:38 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
196:9 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
200:18 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
204:26 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
219:9 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
234:20 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
238:28 Error: '_' is defined but never used. @typescript-eslint/no-unused-vars
265:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/offers/OfferPaymentButton.tsx
20:3 Error: 'onPaymentSuccess' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/offers/PackageOrderModal.tsx
13:3 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
14:3 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
15:3 Error: 'CardDescription' is defined but never used. @typescript-eslint/no-unused-vars
16:3 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
17:3 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/onboarding/PackageStep.tsx
11:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars
19:10 Error: 'InfluencerDropdown' is defined but never used. @typescript-eslint/no-unused-vars
21:10 Error: 'cn' is defined but never used. @typescript-eslint/no-unused-vars
88:9 Error: 'watchedQuantity' is assigned a value but never used. @typescript-eslint/no-unused-vars
137:9 Error: 'getPlatformOptions' is assigned a value but never used. @typescript-eslint/no-unused-vars
145:9 Error: 'getContentTypeOptions' is assigned a value but never used. @typescript-eslint/no-unused-vars
152:9 Error: 'getVideoDurationOptions' is assigned a value but never used. @typescript-eslint/no-unused-vars

./src/components/onboarding/steps/BusinessBioStep.tsx
10:10 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
10:16 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
10:29 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
10:41 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/onboarding/steps/BusinessNameStep.tsx
10:10 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
10:16 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
10:29 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
10:41 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/onboarding/steps/BusinessSocialMediaStep.tsx
10:10 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
10:16 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
10:29 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
10:41 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars
11:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars
21:10 Error: 'cn' is defined but never used. @typescript-eslint/no-unused-vars
113:18 Error: 'errors' is assigned a value but never used. @typescript-eslint/no-unused-vars
115:5 Error: 'watch' is assigned a value but never used. @typescript-eslint/no-unused-vars

./src/components/onboarding/steps/CategoriesStep.tsx
6:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/onboarding/steps/GenderStep.tsx
4:10 Error: 'Card' is defined but never used. @typescript-eslint/no-unused-vars
4:16 Error: 'CardContent' is defined but never used. @typescript-eslint/no-unused-vars
4:29 Error: 'CardHeader' is defined but never used. @typescript-eslint/no-unused-vars
4:41 Error: 'CardTitle' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/onboarding/steps/PackageCreationStep.tsx
11:10 Error: 'Badge' is defined but never used. @typescript-eslint/no-unused-vars
21:10 Error: 'cn' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/onboarding/steps/UsernameStep.tsx
64:14 Error: 'err' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/profile/AvatarUpload.tsx
4:10 Error: 'Upload' is defined but never used. @typescript-eslint/no-unused-vars
4:18 Error: 'X' is defined but never used. @typescript-eslint/no-unused-vars
4:49 Error: 'User' is defined but never used. @typescript-eslint/no-unused-vars
71:6 Warning: React Hook useCallback has a missing dependency: 'handleFileSelection'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/components/reviews/CreateReviewForm.tsx
71:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/ui/chat-button.tsx
4:10 Error: 'VariantProps' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/ui/image-cropper.tsx
200:15 Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element @next/next/no-img-element

./src/components/ui/infinite-scroll.tsx
167:44 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
178:38 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

./src/components/ui/platform-selector.tsx
4:17 Error: 'Plus' is defined but never used. @typescript-eslint/no-unused-vars
25:11 Error: 'Platform' is defined but never used. @typescript-eslint/no-unused-vars

./src/components/ui/pricing-matrix.tsx
252:13 Error: 'platform_icon' is defined but never used. @typescript-eslint/no-unused-vars
343:19 Error: 'platform_icon' is defined but never used. @typescript-eslint/no-unused-vars
379:26 Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`. react/no-unescaped-entities

./src/contexts/AuthContext.tsx
15:16 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
16:56 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
18:45 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
19:49 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any
118:15 Error: 'data' is assigned a value but never used. @typescript-eslint/no-unused-vars
131:14 Error: 'error' is defined but never used. @typescript-eslint/no-unused-vars

./src/hooks/useInfiniteScroll.ts
47:6 Warning: React Hook useCallback has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
97:6 Warning: React Hook useCallback has a missing dependency: 'isLoading'. Either include it or remove the dependency array. react-hooks/exhaustive-deps
166:6 Warning: React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies. react-hooks/exhaustive-deps
166:6 Warning: React Hook useEffect has missing dependencies: 'dependencies' and 'reset'. Either include them or remove the dependency array. react-hooks/exhaustive-deps
173:6 Warning: React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array. react-hooks/exhaustive-deps

./src/lib/api-auth.ts
326:7 Warning: Unexpected any. Specify a different type. @typescript-eslint/no-explicit-any

info - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
