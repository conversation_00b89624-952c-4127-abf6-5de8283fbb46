'use client';

import { useState, useEffect } from 'react';
import { ChatList } from './ChatList';
import { ChatRoom } from './ChatRoom';
import { ChatRoom as ChatRoomType, getChatRoom } from '@/lib/chat';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface ChatProps {
  initialRoomId?: string;
  onRoomStateChange?: (isInRoom: boolean) => void;
}

export function Chat({ initialRoomId, onRoomStateChange }: ChatProps) {
  const [selectedRoom, setSelectedRoom] = useState<ChatRoomType | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (initialRoomId) {
      loadRoom(initialRoomId);
    } else {
      onRoomStateChange?.(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialRoomId]);

  const loadRoom = async (roomId: string) => {
    setLoading(true);
    try {
      const { data: room, error } = await getChatRoom(roomId);
      if (room && !error) {
        setSelectedRoom(room);
        onRoomStateChange?.(true);
      }
    } catch (error) {
      console.error('Error loading room:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectRoom = (room: ChatRoomType) => {
    setSelectedRoom(room);
    onRoomStateChange?.(true);
  };

  const handleBackToList = () => {
    setSelectedRoom(null);
    onRoomStateChange?.(false);
  };

  if (loading) {
    return (
      <div className="h-full w-full">
        <Card className="h-full">
          <CardHeader className="space-y-2">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            {/* Chat room skeleton */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
              <div className="space-y-3">
                {[1, 2, 3, 4].map(i => (
                  <div key={i} className="flex items-start gap-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      {/* Mobile/Tablet Layout (< 1024px) - Show one component at a time */}
      <div className="h-full lg:hidden">
        {selectedRoom ? (
          <ChatRoom room={selectedRoom} onBack={handleBackToList} />
        ) : (
          <ChatList onSelectRoom={handleSelectRoom} />
        )}
      </div>

      {/* Desktop Layout (≥ 1024px) - Show both components side by side */}
      <div className="hidden lg:flex h-full">
        {/* Left Sidebar - Chat List (Fixed 300px width) */}
        <div className="w-[300px] flex-shrink-0 border-r border-gray-200 bg-white">
          <ChatList onSelectRoom={handleSelectRoom} />
        </div>

        {/* Right Content - Chat Room */}
        <div className="flex-1 bg-gray-50">
          {selectedRoom ? (
            <ChatRoom
              room={selectedRoom}
              onBack={handleBackToList}
              hideBackButton={true} // Hide back button on desktop since list is always visible
            />
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="h-8 w-8 text-blue-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 20l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                    />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  Odaberite razgovor
                </h3>
                <p className="text-sm">
                  Kliknite na razgovor sa lijeve strane da počnete chatovanje
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
