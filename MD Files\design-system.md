# Influexus Design System

## 🎨 Dizajn Filozofija

Naš dizajn sistem je inspirisan Instagram-om i modernim glass morphism efektima. Cilj je kreirati vizuelno privlačnu, konzistentnu i funkcionalnu platformu koja odražava kreativnost i profesionalnost influencer marketing industrije.

## 🌈 Boje i Gradijenti

### Instagram-inspirisani Gradijenti

```css
/* Glavni gradijenti */
--instagram-gradient-primary: linear-gradient(
  135deg,
  #833ab4 0%,
  #fd1d1d 50%,
  #fcb045 100%
);
--instagram-gradient-secondary: linear-gradient(
  135deg,
  #405de6 0%,
  #5851db 25%,
  #833ab4 50%,
  #c13584 75%,
  #e1306c 100%
);
--instagram-gradient-story: linear-gradient(
  45deg,
  #f09433 0%,
  #e6683c 25%,
  #dc2743 50%,
  #cc2366 75%,
  #bc1888 100%
);
--instagram-gradient-subtle: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
--instagram-gradient-warm: linear-gradient(
  135deg,
  #ff9a9e 0%,
  #fecfef 50%,
  #fecfef 100%
);
```

### Strategija Korišćenja Boja po Tipovima Korisnika

#### 🎭 **Influencer Stranice**

- **Glavni gradijent**: `instagram-story` - topli, kreativni gradijent (narandžasta → ružičasta → ljubičasta)
- **Karakteristike**: Energičan, kreativan, inspirativan
- **Koristi se za**: Registration, onboarding, dashboard influencera

#### 🏢 **Business Stranice**

- **Glavni gradijent**: `instagram-subtle` - profesionalni gradijent (plava → ljubičasta)
- **Karakteristike**: Profesionalan, pouzdan, sofisticiran
- **Koristi se za**: Business registration, dashboard, kampanje

#### 🔐 **Autentifikacija Stranice**

- **Login**: `instagram-secondary` - kompleksan, sigurnosni gradijent
- **Karakteristike**: Siguran, pouzdan, elegantnan

#### 🏠 **Landing Page**

- **Glavni gradijent**: `instagram-primary` - klasični Instagram gradijent
- **Karakteristike**: Prepoznatljiv, privlačan, univerzalan

## 🪟 Glass Morphism Efekti

### Osnovna Glass Klasa

```css
.glass-instagram {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(131, 58, 180, 0.1);
}
```

### Primena

- **Kartice**: Transparentne sa blur efektom
- **Modali**: Overlay sa glass efektom
- **Navigation**: Header sa backdrop blur
- **Forms**: Input polja sa transparentnim pozadinama

## 🧩 Komponente

### 1. GradientBackground

```tsx
<GradientBackground variant="story" decorativeElements={true}>
  {children}
</GradientBackground>
```

**Varijante:**

- `primary` - Landing page
- `secondary` - Login/Auth stranice
- `story` - Influencer stranice
- `subtle` - Business stranice
- `warm` - Registration selection

### 2. GradientCard

```tsx
<GradientCard variant="primary" glassEffect={true}>
  <CardContent>...</CardContent>
</GradientCard>
```

### 3. GradientButton

```tsx
<GradientButton gradientVariant="white">Registruj se</GradientButton>
<GradientButton gradientVariant="glass">Sekundarno dugme</GradientButton>
```

**Varijante:**

- `instagram` - Glavni CTA dugmad
- `white` - Primarni dugmad na gradient pozadinama
- `glass` - Sekundarni dugmad

### 4. GradientInput

```tsx
<GradientInput placeholder="Email adresa" error={!!errors.email} />
```

### 5. GradientHeader

```tsx
<GradientHeader backHref="/registracija" backText="Nazad" title="Influexus" />
```

## 📱 Responsive Dizajn

### Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Adaptacije

- **Mobile**: Pojednostavljeni gradijenti, manji blur efekti
- **Tablet**: Umanjeni dekorativni elementi
- **Desktop**: Puni glass morphism efekti

## ✨ Animacije i Tranzicije

### Standardne Tranzicije

```css
transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
```

### Hover Efekti

- **Dugmad**: Scale + shadow povećanje
- **Kartice**: Subtle glow efekt
- **Linkovi**: Color transition

### Loading States

- **Skeleton**: Glass morphism sa pulse animacijom
- **Spinners**: Instagram gradient animacija

## 🎯 Najbolji Primeri Implementacije

### 1. Login Stranica (`/prijava`)

**Zašto je savršena:**

- Koristi `instagram-secondary` gradijent koji sugeriše sigurnost
- Glass morphism forma je elegantna i moderna
- Konzistentni input styling sa transparentnim pozadinama
- Smooth hover efekti na svim interaktivnim elementima

### 2. Influencer Registration (`/registracija/influencer`)

**Zašto je savršena:**

- `instagram-story` gradijent odražava kreativnost influencera
- Forma je vizuelno privlačna ali funkcionalna
- Dekorativni elementi dodaju dubinu bez ometanja
- Tipografija je čista i čitljiva na gradient pozadini

### 3. Business Registration (`/registracija/business`)

**Zašto je savršena:**

- `instagram-subtle` gradijent je profesionalan i pouzdan
- Više input polja je elegantno organizovano
- Glass efekti dodaju sofisticiranost
- Boje sugeriše ozbiljnost business okruženja

## 📋 Smernice za Buduće Stranice

### Dashboard Stranice

- **Influencer Dashboard**: Nastaviti sa `instagram-story` temom
- **Business Dashboard**: Koristiti `instagram-subtle` gradijent
- **Sidebar**: Glass morphism sa blur efektom

### Kampanje i Projekti

- **Kreiranje kampanje**: Business tema (`subtle`)
- **Pregled kampanja**: Neutralni `primary` gradijent
- **Analitika**: Profesionalni `subtle` gradijent

### Profile i Settings

- **Influencer profil**: `story` gradijent sa warm akcentima
- **Business profil**: `subtle` sa cool akcentima
- **Settings**: Neutralni `secondary` gradijent

## 🔧 Implementacija

### CSS Varijable

Sve boje i gradijenti su definisani kao CSS custom properties u `globals.css`

### Utility Klase

```css
.bg-instagram-primary
.bg-instagram-story
.bg-instagram-subtle
.text-instagram-primary
.border-instagram-primary
.glass-instagram
```

### Komponente

Sve gradient komponente su u `/src/components/ui/` direktorijumu i exportovane kroz `index.ts`

## 🎨 Dizajn Tokeni

### Spacing

```css
--space-1: 0.25rem; /* 4px */
--space-4: 1rem; /* 16px */
--space-8: 2rem; /* 32px */
--space-16: 4rem; /* 64px */
```

### Shadows

```css
box-shadow: 0 8px 32px rgba(131, 58, 180, 0.1); /* Glass shadow */
box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* Standard shadow */
```

### Border Radius

```css
border-radius: 0.5rem; /* Cards */
border-radius: 0.75rem; /* Buttons */
border-radius: 1rem; /* Special elements */
```

---

_Ovaj design system osigurava konzistentnost kroz celu aplikaciju dok omogućava fleksibilnost za buduće proširenja._
