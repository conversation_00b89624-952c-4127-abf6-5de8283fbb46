import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase';

export async function GET(req: NextRequest) {
  try {
    createServerClient();
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const userType = searchParams.get('userType') as 'business' | 'influencer';

    if (!userId || !userType) {
      return NextResponse.json(
        { error: 'Missing userId or userType' },
        { status: 400 }
      );
    }

    // Test subscription functions
    const {
      getUserSubscription,
      hasActivePremiumSubscription,
      getSubscriptionPlans,
    } = await import('@/lib/subscriptions');

    const subscription = await getUserSubscription(userId, userType);
    const hasActiveSub = await hasActivePremiumSubscription(userId, userType);
    const plans = await getSubscriptionPlans(userType);

    return NextResponse.json({
      userId,
      userType,
      subscription,
      hasActiveSub,
      availablePlans: plans,
      timestamp: new Date().toISOString(),
    });
  } catch (error: unknown) {
    console.error('Test subscription error:', error);
    return NextResponse.json(
      {
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
